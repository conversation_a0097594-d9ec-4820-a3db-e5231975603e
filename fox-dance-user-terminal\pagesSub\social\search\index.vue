<template>
  <view class="search-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <u-search
        v-model="keyword"
        placeholder="搜索帖子、用户或话题"
        :show-action="true"
        action-text="搜索"
        @search="onSearch"
        @custom="onSearch"
      ></u-search>
    </view>

    <!-- 搜索结果 -->
    <scroll-view v-if="searched" class="search-results" scroll-y>
      <view v-if="results.length > 0" class="results-list">
        <PostCard v-for="post in results" :key="post.id" :post="post" class="post-card-item" />
      </view>
      <u-empty v-else mode="search" text="没有找到相关内容"></u-empty>
    </scroll-view>

    <!-- 搜索历史和热门搜索 -->
    <view v-else class="discovery-section">
      <!-- 搜索历史 -->
      <view v-if="searchHistory.length > 0" class="history-section">
        <view class="section-header">
          <text class="section-title">搜索历史</text>
          <u-icon name="trash" color="#999" size="20" @click="clearHistory"></u-icon>
        </view>
        <view class="tags-container">
          <u-tag
            v-for="(item, index) in searchHistory"
            :key="index"
            :text="item"
            type="info"
            @click="onTagClick(item)"
          ></u-tag>
        </view>
      </view>

      <!-- 热门搜索 -->
      <view class="hot-searches-section">
        <view class="section-header">
          <text class="section-title">热门搜索</text>
        </view>
        <view class="tags-container">
          <u-tag
            v-for="(item, index) in hotSearches"
            :key="index"
            :text="item"
            type="warning"
            plain
            @click="onTagClick(item)"
          ></u-tag>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import PostCard from "../components/PostCard.vue";

export default {
  name: "SearchPage",
  components: {
    PostCard
  },
  data() {
    return {
      keyword: "",
      searched: false,
      searchHistory: ["街舞", "美食探店", "周末去哪儿"],
      hotSearches: ["即兴舞蹈", "旅行vlog", "健身打卡", "美食制作", "摄影技巧"],
      results: []
    };
  },
  methods: {
    onSearch(value) {
      if (!value) return;
      this.searched = true;
      this.updateHistory(value);
      // 模拟API调用
      this.results = this.generateMockResults(value);
    },
    updateHistory(keyword) {
      const index = this.searchHistory.indexOf(keyword);
      if (index > -1) {
        this.searchHistory.splice(index, 1);
      }
      this.searchHistory.unshift(keyword);
      if (this.searchHistory.length > 10) {
        this.searchHistory.pop();
      }
    },
    clearHistory() {
      this.searchHistory = [];
    },
    onTagClick(tag) {
      this.keyword = tag;
      this.onSearch(tag);
    },
    generateMockResults(keyword) {
      const mockPosts = [];
      for (let i = 0; i < 5; i++) {
        mockPosts.push({
          id: `search-${i}`,
          title: `关于“${keyword}”的帖子标题 ${i + 1}`,
          username: `用户${Math.floor(Math.random() * 1000)}`,
          userAvatar: `https://picsum.photos/100/100?random=${i}`,
          coverImage: `https://picsum.photos/300/400?random=${i}`,
          likeCount: Math.floor(Math.random() * 100),
          commentCount: Math.floor(Math.random() * 20)
        });
      }
      return mockPosts;
    }
  }
};
</script>

<style lang="scss" scoped>
.search-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}
.search-bar {
  padding: 16rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e4e7ed;
}
.discovery-section,
.search-results {
  padding: 32rpx;
  flex: 1;
  overflow-y: auto;
}
.history-section,
.hot-searches-section {
  margin-bottom: 40rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
}
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.results-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.post-card-item {
  width: 100%;
}
</style> 