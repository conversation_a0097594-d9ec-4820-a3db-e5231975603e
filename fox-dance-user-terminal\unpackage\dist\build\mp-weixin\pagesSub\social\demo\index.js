(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/demo/index"],{"09ca":function(n,t,e){},"228a":function(n,t,e){"use strict";e.d(t,"b",(function(){return c})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){return u}));var u={uIcon:function(){return e.e("components/uview-ui/components/u-icon/u-icon").then(e.bind(null,"8398"))},uButton:function(){return e.e("components/uview-ui/components/u-button/u-button").then(e.bind(null,"fcb0"))}},c=function(){var n=this.$createElement;this._self._c},o=[]},"75ad":function(n,t,e){"use strict";e.r(t);var u=e("228a"),c=e("8a09");for(var o in c)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return c[n]}))}(o);e("8e09");var a=e("828b"),i=Object(a["a"])(c["default"],u["b"],u["c"],!1,null,"6b8fbc93",null,!1,u["a"],void 0);t["default"]=i.exports},"8a09":function(n,t,e){"use strict";e.r(t);var u=e("dcbc"),c=e.n(u);for(var o in u)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(o);t["default"]=c.a},"8e09":function(n,t,e){"use strict";var u=e("09ca"),c=e.n(u);c.a},dcbc:function(n,t,e){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e={name:"SocialDemo",methods:{goPage:function(t){var e=this;n.navigateTo({url:t,fail:function(n){console.error("页面跳转失败:",n),e.$u.toast("页面跳转失败")}})}}};t.default=e}).call(this,e("df3c")["default"])},fbfb:function(n,t,e){"use strict";(function(n,t){var u=e("47a9");e("cff9");u(e("3240"));var c=u(e("75ad"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(c.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["fbfb","common/runtime","common/vendor"]]]);