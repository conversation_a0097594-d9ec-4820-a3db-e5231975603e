<template>
  <view>
    <!-- TabBar 仅在卡片不显示时渲染 -->
    <view class="tab-bar" :class="{
			'tab-bar-exit': showBox && !isTabBarReturning,
			'tab-bar-enter': isTabBarReturning,
			'tab-bar-icon-visible': showBox
		}">
      <view class="tab_bgi " :class="noIndex == 2 ? 'tab_bgi2' : ''">
        <!-- <image src="/static/images/<EMAIL>" mode="scaleToFill"></image> -->
      </view>
      <view v-for="(item, index) in list" :key="index" class="tab-bar-item"
            :class="{ 'tab-fixed-visible': index === 2 && showBox }" @click="tabbarChange(item, index)">
        <view class="" v-if="index != 2">
          <!-- <image class="tab_img" :src="current == index ? item.selectedIconPath : item.iconPath"
            mode="aspectFit">
          </image> -->
          <u-icon :name="current == index ? item.icon + '-fill' : item.icon" :color="selColor"
                  size="40"></u-icon>
          <view class="tab_text" :style="{ color: current == index ? '#131315' : '#131315' }">
            {{ item.text }}
          </view>
        </view>
        <view class="" v-if="index == 2">
          <image class="tab_img" :src="current == index ? item.selectedIconPath : item.iconPath"
                 mode="aspectFit" style="width: 104rpx;height: 104rpx;margin-top: -110rpx;">
          </image>
        </view>
      </view>
    </view>

    <!-- 全屏点击区域（为了关闭预览） -->
    <view v-if="showBox" class="preview-mask" @tap="closePreview">
      <!-- 全屏底层白色背景 -->
      <view class="fullscreen-background" :class="{ 'background-exit': isCardExiting }">
        <!-- 顶部 -->
        <view class="header-container">
          <view class="header-left">
            <image src="/static/icon/home.png" mode="aspectFit" class="header-image home-icon"></image>
          </view>
          <image src="/static/tabbar/tab_fox1.png" mode="aspectFit" class="header-image"></image>
          <view class="header-right">
            <image src="/static/icon/个人中心.png" mode="aspectFit" class="header-image"></image>
          </view>
        </view>
        <!-- 底部 -->
        <view class="bottom-hint">
          <view class="fox-logo-container">
            <view class="fox-line"></view>
            <text class="fox-text">FOX DANCE STUDIO</text>
            <view class="fox-line"></view>
          </view>
          <text class="fox-subtext">{{ cardTipText }}</text>
        </view>
      </view>

      <!-- 卡片区域（点击时停止冒泡，防止关闭） -->
      <view class="card-container-with-buttons" :class="{ 'show': showBox && !isCardExiting, 'exit': isCardExiting }"
            @touchmove.stop="onCardTouchMove" @touchstart.stop="onCardTouchStart" @touchend.stop="onCardTouchEnd">
        
        <!-- 轮播图区域 -->
        <swiper class="card-swiper" :current="currentPage" @change="onSwiperChange" :previous-margin="'30rpx'"
                :next-margin="'30rpx'" :duration="300" :circular="true" :skip-hidden-item-layout="false"
                :indicator-dots="false" :class="{ 'card-pulling': isCardPulling }">
          <swiper-item v-for="(item, index) in products" :key="index" class="card-swiper-item">
            <!-- 卡片（点击时阻止冒泡） -->
            <view class="card-preview-container card-item" @tap.stop
                  :class="{ 'active-card': currentPage === index, 'near-active': Math.abs(currentPage - index) === 1 }"
                  :data-index="index">
              <preview-card :title="item.name" :tag="item.tag" :image="item.image"
                          :targetPage="item.targetPage" @pulling="handleCardPulling" />
            </view>
          </swiper-item>
        </swiper>

        <!-- 分页指示器，固定在页面中 -->
        <view class="page-dots" @tap.stop>
          <!-- 使用内联样式固定位置 -->
          <view v-for="i in products.length" :key="i" class="page-dot" @tap.stop
                :style="{ backgroundColor: currentPage === i ? 'rgb(232,124,174)' : 'rgba(0,0,0,0.2)' }"
                @tap="handleDotTap(i - 1)">
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// 导入PreviewCard组件
import PreviewCard from '@/components/PreviewCard.vue'

export default {
  name: 'tabbar',
  components: {
    PreviewCard
  },
  props: {
    current: {
      type: [Number],
      default: 0
    }
  },
  data() {
    return {
      list: [{
        iconPath: "/static/tabbar/tab_home.png",
        selectedIconPath: "/static/tabbar/tab_home_x.png",
        icon: 'home',
        text: '首页',
        count: 0,
        isDot: false,
        customIcon: false,
        pagePath: "/pages/index/index"
      },
        {
          iconPath: "/static/tabbar/tab_buy.png",
          selectedIconPath: "/static/tabbar/tab_buy_x.png",
          icon: 'bag',
          text: '购买',
          count: 0,
          isDot: false,
          customIcon: false,
          pagePath: "/pages/buy/buy"
        },
        {
          iconPath: "/static/tabbar/tab_fox1.png",
          selectedIconPath: "/static/tabbar/tab_fox1.png",
          text: '作品',
          count: 0,
          isDot: false,
          customIcon: false,
          pagePath: "/pages/index/index"
        },
        {
          iconPath: "/static/tabbar/tab_schedule.png",
          selectedIconPath: "/static/tabbar/tab_schedule_x.png",
          icon: 'tags',
          text: '约课',
          count: 0,
          isDot: false,
          customIcon: false,
          pagePath: "/pages/Schedule/Schedule"
        },
        {
          iconPath: "/static/tabbar/tab_mine.png",
          selectedIconPath: "/static/tabbar/tab_mine_x.png",
          icon: 'account',
          text: '我的',
          count: 0,
          isDot: false,
          customIcon: false,
          pagePath: "/pages/mine/mine"
        },
      ],

      bottomHeight2: 4,
      bottomHeight: 10,
      ecology: '',
      noIndex: 0,
      showBox: false,
      showFixed: false,
      selColor: uni.getStorageSync('storeInfo') ? uni.getStorageSync('storeInfo').button : '#131315',

      // 卡片相关数据
      currentPage: 0,  // 当前页面索引
      showBoundaryHint: false,
      totalCards: 2,  // 总卡片数量

      // 商品数据
      products: [
        //https://dance-**********.cos.ap-guangzhou.myqcloud.com/static/images/vote-card-image.jpg
        { id: 1, name: '新店投票', tag: 'Fox - New store voting', image: 'https://dance-**********.cos.ap-guangzhou.myqcloud.com/static/images/vote-card-image.jpg?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85', targetPage: '/pagesSub/switch/vote' },
        { id: 2, name: 'Fox社区', tag: 'Fox - Topic square', image: 'https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85', targetPage: '/pagesSub/social/main/index' }
      ],

      // 页面滚动锁定标志
      pageScrollLocked: false,
      // TabBar显示状态
      isTabBarHidden: false,
      // TabBar返回动画标记
      isTabBarReturning: false,

      // 滚动状态
      scrollLeft: 0,
      cardWidth: 0,
      windowWidth: 0,

      // 触摸状态
      touchStartX: 0,
      touchStartY: 0,
      touchStartTime: 0,
      touchMoved: false,
      isSwiping: false,
      swipeThreshold: 10, // 滑动阈值
      cardTouchTimer: null,
      
      // 添加方向判断相关变量
      touchDirection: '', // 'horizontal' 或 'vertical'
      directionLocked: false,
      minSwipeDistance: 5, // 最小判定为滑动的距离
      directionThreshold: 0.5, // 方向判定阈值，值越小越容易判定为水平
      
      // 调整更严格的角度判断参数
      verticalAngleThreshold: 5, // 垂直方向的角度阈值(度)，小于这个角度才会被视为垂直
      horizontalAngleThreshold: 85, // 水平方向的角度阈值(度)，小于这个角度视为水平
      minVerticalDistance: 20, // 最小垂直滑动距离，小于此距离不触发下拉
      
      // 副卡相关状态
      rightCardOffsetX: 0,
      leftCardOffsetX: 0,
      rightCardScale: 0.85,
      leftCardScale: 0.85,
      rightCardOpacity: 0.7,
      leftCardOpacity: 0.7,

      // 添加卡片下拉状态
      isCardPulling: false,
      pullDistance: 0,

      // 添加关闭状态
      isClosing: false,

      // 添加卡片退场状态
      isCardExiting: false,
      
      // 卡片提示文本
      cardTipText: "随便点一下，可能会发现新大陆"
    }
  },
  computed: {
    // 卡片容器样式
    cardContainerStyle() {
      if (!this.showBox) return {}

      return {
        transform: `translateY(-20px)`,
        transition: 'transform 450ms cubic-bezier(0.4, 0, 0.2, 1)'
      }
    }
  },
  created() {
    let that = this;
    uni.getSystemInfo({
      success(res) {
        if (res.safeAreaInsets.bottom) {
          that.bottomHeight = res.safeAreaInsets.bottom
          that.bottomHeight2 = res.safeAreaInsets.bottom - 7;
        }
        // 获取屏幕宽度用于卡片定位
        that.windowWidth = res.windowWidth;
        that.cardWidth = res.windowWidth * 0.8; // 卡片宽度为窗口的80%
      }
    })

    // 全局触摸事件处理
    uni.$on('touchmove', (e) => {
      if (this.showBox || this.pageScrollLocked) {
        // 如果卡片显示或页面被锁定，阻止所有触摸移动
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
        // 如果页面被锁定，则滚动到顶部
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0
        });
        return false;
      }
    });
    
    // 在微信小程序环境中，需要将方法暴露给页面实例
    if (typeof this.$scope !== 'undefined' && this.$scope) {
      this.$scope.preventCardClick = this.preventCardClick;
      this.$scope.handleContainerClick = this.handleContainerClick;
      this.$scope.swiperBackgroundClick = this.swiperBackgroundClick;
    }
  },
  mounted() {
    // 在组件挂载后，确保初始卡片居中显示
    this.$nextTick(() => {
      setTimeout(() => {
        if (this.showBox) {
          this.scrollToCard(this.currentPage);
        }
      }, 300);
    });
    
    // 在微信小程序环境中，确保方法已注册到页面实例
    if (typeof this.$scope !== 'undefined' && this.$scope) {
      this.$scope.preventCardClick = this.preventCardClick;
      this.$scope.handleContainerClick = this.handleContainerClick;
      this.$scope.swiperBackgroundClick = this.swiperBackgroundClick;
    }
    
    // 获取卡片提示文本
    this.getCardTip();
  },
  beforeDestroy() {
    // 移除全局触摸事件监听
    uni.$off('touchmove');
  },
  methods: {
    // 轮播图切换事件
    onSwiperChange(e) {
      this.currentPage = e.detail.current;
    },

    // 切换完门店执行切换按钮颜色
    setColor(ecology) {
      try {
        this.ecology = ecology ? ecology : '敬请期待~'
        const storeInfo = uni.getStorageSync('storeInfo')
        this.selColor = (storeInfo && storeInfo.button) ? storeInfo.button : '#131315'
      } catch (error) {
        console.warn('setColor error:', error)
        this.ecology = '敬请期待~'
        this.selColor = '#131315'
      }
    },
    tabbarChange(item, index) {
      this.noIndex = index
      if (index == 2) {
        // 如果当前是约课页面(current === 3)或购买页面(current === 1)，禁止点击中间Tab进入卡片预览
        if(this.current === 3 || this.current === 1) {
          // 在约课页面或购买页面，禁止打开卡片预览
          let pageName = this.current === 3 ? '约课' : '购买'
          uni.showToast({
            title: `${pageName}页面不可用`,
            icon: 'none',
            duration: 2000
          })
          return
        }

        this.noIndex = index
        // 入场阶段：TabBar和卡片同步动画
        this.showBox = true
        this.showFixed = true
        this.isTabBarReturning = false
        // 阻止页面滚动
        this.preventScroll(true)

        // 尝试使用微信小程序API控制弹性效果
        try {
          wx.setPageStyle({
            style: {
              overflow: 'hidden'
            }
          })
        } catch(e) {
          // 忽略错误
        }

        // 修改：保留图标可见
        setTimeout(() => {
          this.isTabBarHidden = false
        }, 400)
        
        // 获取卡片提示文本
        this.getCardTip();

        return
      } else {
        uni.switchTab({
          url: item.pagePath
        })
      }
    },

    // 改进防止滚动的方法
    preventScroll(prevent) {
      // 通过页面状态控制滚动
      this.pageScrollLocked = prevent;

      // 使用全局变量控制滚动状态
      getApp().globalData = getApp().globalData || {};
      getApp().globalData.pageScrollLocked = prevent;

      // 尝试使用微信小程序原生API控制滚动
      try {
        wx.setPageStyle({
          style: {
            overflow: prevent ? 'hidden' : 'auto'
          }
        })
      } catch(e) {
        // 忽略错误
      }
    },

    // 更强的触摸阻止函数
    preventTouchMove(e) {
      // 如果已经确定了滑动方向
      if (this.directionDetermined) {
        // 如果是水平滑动，不阻止默认行为
        if (this.initialSwipeDirection === 'horizontal') {
          return true;
        }
      }
      
      if (this.pageScrollLocked) {
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
        return false;
      }
    },

    // 添加卡片下拉处理方法
    handleCardPulling(data) {
      // 只有当明确判定为垂直方向滑动时才触发下拉效果
      if (this.touchDirection === 'vertical') {
        this.isCardPulling = true;
        this.pullDistance = data.distance;
      }
    },

    // 添加触摸开始事件处理
    onCardTouchStart(e) {
      const touch = e.touches[0];
      this.touchStartX = touch.clientX;
      this.touchStartY = touch.clientY;
      this.touchStartTime = Date.now();
      this.touchMoved = false;
      this.directionLocked = false;
      this.touchDirection = '';
      this.sampledTouches = []; // 重置采样点数组
      
      // 重置滑动判断相关变量
      this.initialSwipeDirection = '';
      this.directionDetermined = false;
      this.lastDeltaX = 0;
      this.lastDeltaY = 0;
      this.accumulatedHorizontalDistance = 0;
      this.accumulatedVerticalDistance = 0;
      this.lastTouchTime = Date.now();
      
      // 添加首个采样点
      this.sampledTouches.push({
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now()
      });
    },

    // 添加触摸移动事件处理
    onCardTouchMove(e) {
      // 防止页面滚动
      if (this.pageScrollLocked) {
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
      }
      
      // 实现事件节流，避免过于频繁处理造成卡顿
      const now = Date.now();
      if (now - this.lastTouchTime < this.touchThrottleDelay) {
        return; // 如果距离上次处理时间太短，则跳过本次处理
      }
      this.lastTouchTime = now;

      const touch = e.touches[0];
      const currentX = touch.clientX;
      const currentY = touch.clientY;
      const deltaX = currentX - this.touchStartX;
      const deltaY = currentY - this.touchStartY;
      
      // 计算相对于上次位移的差值
      const diffX = Math.abs(deltaX) - Math.abs(this.lastDeltaX);
      const diffY = Math.abs(deltaY) - Math.abs(this.lastDeltaY);
      
      // 更新累计位移
      if (diffX > 0) this.accumulatedHorizontalDistance += diffX;
      if (diffY > 0) this.accumulatedVerticalDistance += diffY;
      
      // 更新上次位移记录
      this.lastDeltaX = Math.abs(deltaX);
      this.lastDeltaY = Math.abs(deltaY);
      
      // 仅当滑动距离超过阈值时才进行方向判断
      const totalDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      
      if (!this.directionDetermined && totalDistance >= this.swipeStartDistance) {
        // 计算水平与垂直方向的比值
        const horizontalRatio = Math.abs(deltaX) / (Math.abs(deltaY) || 0.1); // 避免除以0
        
        // 判断初始滑动方向
        if (horizontalRatio >= this.horizontalDirectionThreshold) {
          // 水平方向优先，判定为水平滑动
          this.initialSwipeDirection = 'horizontal';
          this.touchDirection = 'horizontal';
          this.directionLocked = true;
        } else if (horizontalRatio <= (1 / this.horizontalDirectionThreshold)) {
          // 垂直方向优先，判定为垂直滑动
          this.initialSwipeDirection = 'vertical';
          this.touchDirection = 'vertical';
          this.directionLocked = true;
        } else {
          // 优先考虑累计方向
          if (this.accumulatedHorizontalDistance > this.accumulatedVerticalDistance) {
            this.initialSwipeDirection = 'horizontal';
            this.touchDirection = 'horizontal';
            this.directionLocked = true;
          }
        }
        
        this.directionDetermined = true;
      }
      
      // 如果已经确定方向是水平，继续处理水平滑动逻辑
      if (this.touchDirection === 'horizontal') {
        // 清除任何可能的下拉状态
        if (this.isCardPulling) {
          this.isCardPulling = false;
          this.pullDistance = 0;
        }
      }
      
      // 如果确定方向是垂直，处理垂直滑动逻辑
      if (this.touchDirection === 'vertical' && deltaY > 0) {
        this.isCardPulling = true;
        this.pullDistance = deltaY;
      }
      
      this.touchMoved = true;
      
      // 添加当前触摸点到采样数组，用于更精确的速度计算
      this.sampledTouches.push({
        x: currentX,
        y: currentY,
        time: now
      });
      
      // 控制采样点数量
      if (this.sampledTouches.length > 5) {
        this.sampledTouches.shift();
      }
    },

    // 添加触摸结束事件处理
    onCardTouchEnd(e) {
      if (!this.touchMoved) return;
      
      // 计算触摸持续时间
      const touchDuration = Date.now() - this.touchStartTime;
      
      // 计算最终速度
      let velocityX = 0;
      let velocityY = 0;
      
      if (this.sampledTouches.length >= 2) {
        const newest = this.sampledTouches[this.sampledTouches.length - 1];
        const oldest = this.sampledTouches[0];
        const timeSpan = newest.time - oldest.time;
        
        if (timeSpan > 0) {
          velocityX = (newest.x - oldest.x) / timeSpan; // 每毫秒移动的像素数
          velocityY = (newest.y - oldest.y) / timeSpan;
        }
      }
      
      // 如果是快速水平滑动，可以添加翻页效果
      const isQuickHorizontalSwipe = Math.abs(velocityX) > 0.5 && 
                                    Math.abs(velocityX) > Math.abs(velocityY) * 1.5;
      
      if (isQuickHorizontalSwipe) {
        // 根据滑动方向和速度决定切换到下一页或上一页
        const direction = velocityX < 0 ? 1 : -1; // 负值表示向左滑，正值表示向右滑
        const targetPage = Math.max(0, Math.min(this.products.length - 1, this.currentPage + direction));
        
        if (targetPage !== this.currentPage) {
          this.currentPage = targetPage;
        }
      }
      
      // 重置下拉状态
      if (this.isCardPulling) {
        this.isCardPulling = false;
        this.pullDistance = 0;
      }
      
      // 重置方向判断状态
      this.directionLocked = false;
      this.touchDirection = '';
      this.touchMoved = false;
      this.sampledTouches = [];
      this.directionDetermined = false;
      this.initialSwipeDirection = '';
    },

    // 专门处理home图标点击的方法
    onHomeClick() {
      console.log('点击背景关闭预览');
      
      // 先触发卡片退场动画
      this.isCardExiting = true;
      
      // 等待动画结束后再隐藏
      setTimeout(() => {
        // 立即恢复页面滚动
        this.preventScroll(false);
        
        // 尝试使用微信小程序API恢复滚动效果
        try {
          wx.setPageStyle({
            style: {
              overflow: 'auto'
            }
          });
        } catch(e) {
          // 忽略错误
        }
        
        // 关闭所有遮罩和卡片
        this.showBox = false;
        this.showFixed = false;
        this.isCardExiting = false;
        
        // 重置TabBar状态
        this.isTabBarReturning = true;
        this.isTabBarHidden = false;
        
        // 重置其他状态
        this.currentPage = 0;
        this.noIndex = this.current;
        
        // 稍后重置TabBar动画状态
        setTimeout(() => {
          this.isTabBarReturning = false;
        }, 400);
      }, 300); // 等待300ms让退场动画完成
    },

    // 添加点击指示点切换页面的方法
    handleDotTap(index) {
      this.currentPage = index;
    },
    
    // 添加卡片容器点击处理方法
    handleContainerClick() {
      console.log('容器点击关闭预览');
      this.onHomeClick();
    },
    
    // 专门给微信小程序使用的阻止冒泡方法
    preventCardClick() {
      console.log('卡片点击，阻止冒泡');
      // 仅阻止事件冒泡，不关闭预览
      return false;
    },

    // 添加swiper背景点击处理方法 - 微信小程序原生方法
    swiperBackgroundClick() {
      console.log('swiper背景点击，关闭预览');
      uni.showToast({
        title: '点击了背景区域',
        icon: 'none'
      });
      this.onHomeClick();
    },

    // 添加关闭预览的方法
    closePreview() {
      this.onHomeClick();
    },
    
          // 获取基础URL
      getBaseUrl() {
        // #ifdef MP-WEIXIN
        return 'https://vote.foxdance.com.cn' // 微信小程序环境使用外部域名
        // #endif

        // 非小程序环境使用本地开发地址
        return 'https://vote.foxdance.com.cn'
      },
      
      // 获取卡片提示文本
      getCardTip() {
        const BASE_URL = this.getBaseUrl()
        uni.request({
          url: `${BASE_URL}/api/vote-info/1/card-tip`,
          method: 'GET',
          success: (res) => {
            console.log('获取卡片提示成功:', res);
            if (res.data && res.data.code === 0 && res.data.data) {
              // 更新卡片提示文本
              this.cardTipText = res.data.data;
            }
          },
          fail: (err) => {
            console.error('获取卡片提示失败:', err);
          }
        });
      }
  }
}
</script>

<style lang="scss">
/* 预览遮罩 - 全屏覆盖，点击关闭预览 */
.preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 997;
  background-color: transparent;
}

/* 全屏底层白色背景 */
.fullscreen-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FFFFFF;
  z-index: 997;
  animation: fadeIn 300ms ease;
  touch-action: none;
  transition: opacity 300ms ease-out;
}

/* 背景退场动画 */
.background-exit {
  opacity: 0;
}

/* 底部提示样式 */
.bottom-hint {
  width: 100%;
  position: absolute;
  bottom: 80rpx;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.fox-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  width: 100%;
  padding: 10rpx 0;
}

.fox-line {
  width: 125rpx;
  height: 1px;
  background-color: #CCCCCC;
  margin: 0 16rpx;
}

.fox-text {
  font-size: 26rpx;
  color: #666666;
  letter-spacing: 2rpx;
  font-weight: 500;
}

.fox-subtext {
  font-size: 22rpx;
  color: #999999;
  letter-spacing: 0.5rpx;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* 整体卡片容器 - 包含卡片和按钮 */
.card-container-with-buttons {
  position: fixed;
  top: 56%;
  left: 50%;
  transform: translate(-50%, -50%); /* 修改为-50%确保垂直居中 */
  width: 100%;
  margin-top: 0; /* 移除上边距，让transform居中生效 */
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 999; /* 确保卡片在点击层(998)之上 */
  will-change: transform, opacity;
  height: auto;
  opacity: 0;
  pointer-events: auto;
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
  touch-action: none;
  overflow: visible;
  transform: translate(-50%, -50%) translateY(20px); /* 修改为-50%确保垂直居中 */
}

.card-container-with-buttons.show {
  opacity: 1;
  transform: translate(-50%, -50%) translateY(0);
  /* 向上淡入浮现 */
   pointer-events: auto;
   overflow: visible;
}

/* swiper样式 */
.card-swiper {
  width: 100%;
  height: 73vh;
  touch-action: pan-x pan-y;
  overflow: visible;
  /* 确保卡片可以超出容器 */
  position: relative;
  top: -55rpx;
  z-index: 1000;
  background-color: transparent; /* 蓝色调试背景 */
}

.card-swiper-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  top: -90rpx; /* 改为0，让其位于垂直中心 */
  box-sizing: border-box;
  transform: scale(1);
  /* 为阴影添加额外空间 */
  overflow: visible;
  /* 确保卡片可以超出容器 */
}

.card-item {
  width: 82%;
  height: 100%;
  max-height: 900rpx;
  display: inline-block;
  border-radius: 66rpx;
  box-shadow: none;
  /* 移除阴影效果 */
  transition: all 0.3s ease;
  transform: scale(0.85) translateY(5rpx);
  opacity: 0.9;
  position: relative;
  /* 确保为绝对定位的子元素提供参考 */
  z-index: 1002;
  /* 提高卡片的z-index确保在背景区域之上 */
  overflow: visible;
  /* 改为visible使阴影可以超出容器 */
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
}

.card-item.active-card {
  transform: scale(1) translateY(-15rpx);
  opacity: 1;
  box-shadow: none;
  overflow: visible;
  z-index: 1003;
}

.card-item.near-active {
  transform: scale(0.9) translateY(0);
  opacity: 0.9;
  box-shadow: none;
}

/* 修改swiper组件样式，确保其不会裁剪子元素 */
.card-swiper .wx-swiper-dots {
  position: relative;
  z-index: 98;
}

/* TabBar样式，包含入场和退场动画 */
.tab-bar {
  .tab_bgi {
    position: absolute;
    z-index: -1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-image: url('@/static/tabbar/tab_big1.png');
    background-size: 100% 100%;
  }

  display: flex;
  justify-content: center;
  align-items: end;
  width: 702rpx;
  height: 170rpx;
  height: 98rpx;
  border-radius: 66rpx;
  z-index: 996;
  position: fixed;
  bottom: 56rpx;
  left: 50%;
  transform: translateX(-50%) translateY(0);
  opacity: 1;
  transition: transform 400ms ease-in-out,
  opacity 400ms ease-in-out;
  will-change: transform,
  opacity;

  .tab-bar-item {
    flex: 1;
    height: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    &:nth-child(4) {
      padding-top: 0;
    }

    .tab_img {
      width: 44rpx;
      height: 44rpx;
      display: block;
      margin: auto;
    }

    .tab_text {
      margin-top: 2rpx;
      color: #333;
      font-size: 26rpx;
      color: #945048;
      line-height: 30rpx;
      text-align: center;
    }
  }
}

/* TabBar退出动画 */
.tab-bar-exit {
  transform: translateX(-50%) translateY(100%);
  /* 向下淡出位移 */
  opacity: 0;
  transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保动画持续400ms */
  will-change: transform, opacity;
}

/* TabBar返回动画 */
.tab-bar-enter {
  transform: translateX(-50%) translateY(0);
  /* 向上淡入复位 */
  opacity: 1;
  transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保动画持续400ms */
  will-change: transform, opacity;
}

/* 添加图标可见的样式 */
.tab-bar-icon-visible .tab-bar-item:nth-child(3) {
  opacity: 1 !important;
  z-index: 998;
}

/* 固定显示的tab图标 */
.tab-fixed-visible {
  position: relative;
  z-index: 998;
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* 卡片通用样式 */
.card-preview-container {
  background: #FFF;
  border-radius: 66rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transform-style: preserve-3d;
  user-select: none;
  touch-action: pan-x pan-y;
  overflow: visible;
  /* 改为visible使阴影可以超出容器 */
  position: relative;
  z-index: 1002; /* 提高卡片的z-index确保在点击层之上 */
  /* 确保显示在白色背景之上 */
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 156rpx;
  left: 0;
  right: 0;
  width: 100%;
  height: 100rpx;
  z-index: 998;
}

.header-image {
  width: 100rpx;
  height: 100rpx;
}

.header-left image {
  width: 48rpx;
  height: 48rpx;
  padding-left: 20rpx;
}

.header-right image {
  width: 48rpx;
  height: 48rpx;
  padding-right: 20rpx;
}

/* 简单稳定的分页指示器样式 */
.page-dots {
  position: fixed;
  bottom: 120rpx;
  left: 0;
  width: 100%;
  height: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: -1;
}

.page-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin: 0 10rpx;
}

/* 卡片退场动画 - 更自然的淡出效果 */
.card-container-with-buttons.exit {
  opacity: 0;
  transform: translate(-50%, -50%);
  transition: opacity 300ms ease-out;
}
</style>