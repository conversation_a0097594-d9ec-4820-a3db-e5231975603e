<view class="message-detail-container data-v-f9eda908"><scroll-view class="content-scroll data-v-f9eda908" scroll-y="{{true}}"><view class="message-detail data-v-f9eda908"><view class="message-header data-v-f9eda908"><view class="{{['message-icon','data-v-f9eda908','icon-'+messageDetail.type]}}"><u-icon vue-id="2ec95270-1" name="{{$root.m0}}" size="24" color="#fff" class="data-v-f9eda908" bind:__l="__l"></u-icon></view><view class="header-info data-v-f9eda908"><text class="message-title data-v-f9eda908">{{messageDetail.title}}</text><text class="message-time data-v-f9eda908">{{$root.m1}}</text></view></view><view class="message-body data-v-f9eda908"><rich-text nodes="{{messageDetail.htmlContent||messageDetail.content}}" class="data-v-f9eda908"></rich-text></view><block wx:if="{{$root.g0}}"><view class="action-buttons data-v-f9eda908"><block wx:for="{{messageDetail.actions}}" wx:for-item="action" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['handleAction',['$0'],[[['messageDetail.actions','id',action.id]]]]]]]}}" class="{{['action-btn','data-v-f9eda908',action.type]}}" bindtap="__e"><text class="btn-text data-v-f9eda908">{{action.text}}</text></view></block></view></block><block wx:if="{{$root.g1}}"><view class="attachments data-v-f9eda908"><text class="section-title data-v-f9eda908">相关附件</text><block wx:for="{{messageDetail.attachments}}" wx:for-item="attachment" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['openAttachment',['$0'],[[['messageDetail.attachments','id',attachment.id]]]]]]]}}" class="attachment-item data-v-f9eda908" bindtap="__e"><u-icon vue-id="{{'2ec95270-2-'+__i1__}}" name="file-text" size="20" color="#2979ff" class="data-v-f9eda908" bind:__l="__l"></u-icon><text class="attachment-name data-v-f9eda908">{{attachment.name}}</text><u-icon vue-id="{{'2ec95270-3-'+__i1__}}" name="arrow-right" size="16" color="#999" class="data-v-f9eda908" bind:__l="__l"></u-icon></view></block></view></block></view></scroll-view></view>