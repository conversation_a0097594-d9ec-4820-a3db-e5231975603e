<template>
  <view class="social-main-container">
    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 首页内容 -->
      <HomePage v-if="currentTab === 0" ref="homePage" :key="homePageKey" />

      <!-- 发现页面内容 -->
      <DiscoverPage v-if="currentTab === 1" ref="discoverPage" :key="discoverPageKey" />

      <PublishPage v-if="currentTab === 2" />

      <!-- 消息页面内容 -->
      <MessagePage v-if="currentTab === 3" ref="messagePage" :key="messagePageKey" />

      <!-- 我的页面内容 -->
      <ProfilePage v-if="currentTab === 4" ref="profilePage" :key="profilePageKey" />
    </view>

    <!-- 底部导航 -->
    <TabBar :currentTab="currentTab" @tab-change="handleTabChange" />
  </view>
</template>

<script>
import TabBar from '../components/TabBar.vue'
import HomePage from '../home/<USER>'
import DiscoverPage from '../discover/index.vue'
import PublishPage from '../publish/index.vue'
import MessagePage from '../message/index.vue'
import ProfilePage from '../profile/index.vue'

export default {
  name: 'SocialMain',
  components: {
    TabBar,
    HomePage,
    DiscoverPage,
    PublishPage,
    MessagePage,
    ProfilePage
  },
  data() {
    return {
      currentTab: 0, // 默认显示首页
      homePageKey: 0, // 用于强制刷新HomePage组件
      discoverPageKey: 0, // 用于强制刷新DiscoverPage组件
      messagePageKey: 0, // 用于强制刷新MessagePage组件
      profilePageKey: 0 // 用于强制刷新ProfilePage组件
    }
  },
  onLoad(options) {
    // 根据传入的参数设置当前选项卡
    if (options.tab) {
      this.currentTab = parseInt(options.tab)
    }
  },

  onShow() {
    // 页面显示时，刷新当前tab的数据
    this.refreshCurrentTab()
  },
  methods: {
    // 处理 TabBar 切换事件
    handleTabChange(data) {
      const { index } = data
      const previousTab = this.currentTab
      this.currentTab = index

      // 切换tab时刷新对应页面数据
      if (previousTab !== index) {
        this.refreshCurrentTab()
      }

      console.log('切换到选项卡:', index)
    },

    // 刷新当前tab的数据
    refreshCurrentTab() {
      this.$nextTick(() => {
        switch (this.currentTab) {
          case 0: // 首页
            this.refreshHomePage()
            break
          case 1: // 发现页
            this.refreshDiscoverPage()
            break
          case 3: // 消息页
            this.refreshMessagePage()
            break
          case 4: // 我的页面
            this.refreshProfilePage()
            break
          default:
            console.log('当前tab无需刷新:', this.currentTab)
        }
      })
    },

    // 刷新首页数据
    refreshHomePage() {
      if (this.$refs.homePage) {
        if (this.$refs.homePage.forceRefresh) {
          this.$refs.homePage.forceRefresh()
        } else if (this.$refs.homePage.onRefresh) {
          this.$refs.homePage.onRefresh()
        } else {
          this.homePageKey += 1
        }
      } else {
        this.homePageKey += 1
      }
    },

    // 刷新发现页数据
    refreshDiscoverPage() {
      if (this.$refs.discoverPage) {
        if (this.$refs.discoverPage.forceRefresh) {
          this.$refs.discoverPage.forceRefresh()
        } else if (this.$refs.discoverPage.loadDiscoverData) {
          this.$refs.discoverPage.loadDiscoverData()
        } else {
          this.discoverPageKey += 1
        }
      } else {
        this.discoverPageKey += 1
      }
    },

    // 刷新消息页数据
    refreshMessagePage() {
      if (this.$refs.messagePage) {
        if (this.$refs.messagePage.forceRefresh) {
          this.$refs.messagePage.forceRefresh()
        } else if (this.$refs.messagePage.loadChatList) {
          this.$refs.messagePage.loadUnreadCounts()
          this.$refs.messagePage.loadChatList()
        } else {
          this.messagePageKey += 1
        }
      } else {
        this.messagePageKey += 1
      }
    },

    // 刷新我的页面数据
    refreshProfilePage() {
      console.log('刷新我的页面数据...')
      if (this.$refs.profilePage) {
        // 优先调用页面的forceRefresh方法
        if (this.$refs.profilePage.forceRefresh) {
          this.$refs.profilePage.forceRefresh()
        } else if (this.$refs.profilePage.loadUserInfo) {
          // 如果没有forceRefresh方法，调用loadUserInfo方法
          this.$refs.profilePage.loadUserInfo()
        } else if (this.$refs.profilePage.initializeData) {
          // 如果没有loadUserInfo方法，调用initializeData方法
          this.$refs.profilePage.initializeData()
        } else {
          // 如果都没有，通过改变key强制重新渲染组件
          this.profilePageKey += 1
        }
      } else {
        // 如果ref不存在，通过改变key强制重新渲染组件
        this.profilePageKey += 1
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.social-main-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  overflow: hidden;
}
</style>
