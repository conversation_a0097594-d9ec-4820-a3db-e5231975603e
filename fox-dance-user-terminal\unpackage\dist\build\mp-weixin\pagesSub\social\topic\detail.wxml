<view class="topic-detail-container data-v-258a1658"><view class="topic-header data-v-258a1658" style="{{'background-image:'+('url('+topicInfo.cover+')')+';'}}"><view class="topic-overlay data-v-258a1658"><view class="topic-content data-v-258a1658"><text class="topic-name data-v-258a1658">{{"#"+topicInfo.name}}</text><text class="topic-desc data-v-258a1658">{{topicInfo.description}}</text><view class="topic-stats data-v-258a1658"><text class="stat-item data-v-258a1658">{{topicInfo.postCount+"条帖子"}}</text><text class="stat-item data-v-258a1658">{{topicInfo.followCount+"人关注"}}</text></view><view class="topic-actions data-v-258a1658"><u-button vue-id="cae2d350-1" type="{{topicInfo.isFollowed?'default':'primary'}}" size="default" text="{{topicInfo.isFollowed?'已关注':'关注话题'}}" data-event-opts="{{[['^click',[['toggleFollow']]]]}}" bind:click="__e" class="data-v-258a1658" bind:__l="__l" vue-slots="{{['default']}}">{{topicInfo.isFollowed?'已关注':'关注话题'}}</u-button><u-button vue-id="cae2d350-2" type="primary" plain="{{true}}" size="default" text="发布帖子" data-event-opts="{{[['^click',[['publishPost']]]]}}" bind:click="__e" class="data-v-258a1658" bind:__l="__l" vue-slots="{{['default']}}">发布帖子</u-button></view></view></view></view><scroll-view class="posts-container data-v-258a1658" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="filter-bar data-v-258a1658"><u-tabs vue-id="cae2d350-3" list="{{filterTabs}}" current="{{selectedFilterIndex}}" is-scroll="{{false}}" active-color="#2979ff" inactive-color="#666" bar-width="{{40}}" bar-height="{{4}}" data-event-opts="{{[['^change',[['selectFilter']]]]}}" bind:change="__e" class="data-v-258a1658" bind:__l="__l"></u-tabs></view><view class="posts-list data-v-258a1658"><block wx:for="{{posts}}" wx:for-item="post" wx:for-index="__i0__" wx:key="id"><post-card vue-id="{{'cae2d350-4-'+__i0__}}" post="{{post}}" data-event-opts="{{[['^like',[['onPostLike']]],['^comment',[['onPostComment']]],['^share',[['onPostShare']]],['^click',[['goPostDetail']]]]}}" bind:like="__e" bind:comment="__e" bind:share="__e" bind:click="__e" class="data-v-258a1658" bind:__l="__l"></post-card></block></view><block wx:if="{{hasMore}}"><view class="load-more data-v-258a1658"><block wx:if="{{loading}}"><u-icon vue-id="cae2d350-5" name="loading" size="16" color="#999" class="data-v-258a1658" bind:__l="__l"></u-icon></block><text class="load-text data-v-258a1658">{{loading?'加载中...':'上拉加载更多'}}</text></view></block><block wx:if="{{$root.g0}}"><view class="no-more data-v-258a1658"><text class="no-more-text data-v-258a1658">没有更多帖子了</text></view></block><block wx:if="{{$root.g1}}"><view class="empty-state data-v-258a1658"><u-icon vue-id="cae2d350-6" name="file-text" size="60" color="#ccc" class="data-v-258a1658" bind:__l="__l"></u-icon><text class="empty-text data-v-258a1658">暂无帖子</text><text class="empty-desc data-v-258a1658">成为第一个发布帖子的人吧</text><u-button style="margin-top:20rpx;" vue-id="cae2d350-7" type="primary" size="default" text="发布帖子" data-event-opts="{{[['^click',[['publishPost']]]]}}" bind:click="__e" class="data-v-258a1658" bind:__l="__l" vue-slots="{{['default']}}">发布帖子</u-button></view></block></scroll-view></view>