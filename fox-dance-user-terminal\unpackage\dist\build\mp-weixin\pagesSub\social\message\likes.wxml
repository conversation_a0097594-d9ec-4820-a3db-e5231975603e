<view class="likes-container data-v-50a8bbb6"><scroll-view class="message-list data-v-50a8bbb6" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" data-event-opts="{{[['refresherrefresh',[['onRefresh',['$event']]]],['scrolltolower',[['loadMore',['$event']]]]]}}" bindrefresherrefresh="__e" bindscrolltolower="__e"><block wx:if="{{$root.g0}}"><view class="data-v-50a8bbb6"><block wx:for="{{$root.l0}}" wx:for-item="message" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['openMessageDetail',['$0'],[[['messageList','id',message.$orig.id]]]]]]]}}" class="message-card data-v-50a8bbb6" bindtap="__e"><u-avatar class="user-avatar data-v-50a8bbb6" vue-id="{{'2f55f21f-1-'+__i0__}}" src="{{message.$orig.userAvatar}}" size="40" bind:__l="__l"></u-avatar><view class="message-content data-v-50a8bbb6"><view class="message-header data-v-50a8bbb6"><text class="user-name data-v-50a8bbb6">{{message.$orig.userName}}</text><text class="action-type data-v-50a8bbb6">{{message.$orig.type==='like'?'赞了你的':'评论了你的'}}</text><text class="post-type data-v-50a8bbb6">{{message.m0}}</text><text class="message-time data-v-50a8bbb6">{{message.m1}}</text></view><block wx:if="{{message.$orig.type==='comment'}}"><text class="comment-content data-v-50a8bbb6">{{message.$orig.content}}</text></block><view data-event-opts="{{[['tap',[['openPost',['$0'],[[['messageList','id',message.$orig.id,'postId']]]]]]]}}" class="related-post data-v-50a8bbb6" catchtap="__e"><block wx:if="{{message.$orig.postCover}}"><image class="post-cover data-v-50a8bbb6" src="{{message.$orig.postCover}}" mode="aspectFill"></image></block><text class="post-title data-v-50a8bbb6">{{message.$orig.postTitle}}</text></view></view><block wx:if="{{!message.$orig.isRead}}"><view class="unread-dot data-v-50a8bbb6"></view></block></view></block></view></block><block wx:else><view class="empty-state data-v-50a8bbb6"><u-empty vue-id="2f55f21f-2" mode="data" text="暂无赞和评论" class="data-v-50a8bbb6" bind:__l="__l"></u-empty></view></block><block wx:if="{{$root.g1}}"><view class="load-more data-v-50a8bbb6"><u-loading vue-id="2f55f21f-3" mode="flower" size="24" class="data-v-50a8bbb6" bind:__l="__l"></u-loading><text class="load-text data-v-50a8bbb6">加载中...</text></view></block></scroll-view></view>