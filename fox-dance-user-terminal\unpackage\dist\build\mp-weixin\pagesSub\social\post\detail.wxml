<view class="detail-container data-v-2c523c20"><scroll-view class="content data-v-2c523c20" scroll-y="{{true}}"><view class="post-detail data-v-2c523c20"><view class="user-info data-v-2c523c20"><u-avatar vue-id="dd35d41a-1" src="{{postData.userAvatar}}" size="50" data-event-opts="{{[['^click',[['goUserProfile']]]]}}" bind:click="__e" class="data-v-2c523c20" bind:__l="__l"></u-avatar><view class="user-details data-v-2c523c20"><text data-event-opts="{{[['tap',[['goUserProfile',['$event']]]]]}}" class="username data-v-2c523c20" bindtap="__e">{{postData.username}}</text><text class="time data-v-2c523c20">{{$root.m0}}</text></view><follow-button vue-id="dd35d41a-2" user="{{$root.a0}}" followed="{{postData.isFollowed}}" size="mini" data-event-opts="{{[['^follow',[['onUserFollow']]],['^change',[['onFollowChange']]]]}}" bind:follow="__e" bind:change="__e" class="data-v-2c523c20" bind:__l="__l"></follow-button></view><block wx:if="{{postData.title}}"><view class="post-title data-v-2c523c20"><text class="title-text data-v-2c523c20">{{postData.title}}</text></view></block><view class="post-content data-v-2c523c20"><text class="content-text data-v-2c523c20">{{postData.content}}</text></view><block wx:if="{{$root.g0}}"><view class="topic-tags data-v-2c523c20"><block wx:for="{{postData.topics}}" wx:for-item="topic" wx:for-index="__i0__" wx:key="*this"><text data-event-opts="{{[['tap',[['goTopic',['$0'],[[['postData.topics','',__i0__]]]]]]]}}" class="topic-tag data-v-2c523c20" bindtap="__e">{{'#'+topic+''}}</text></block></view></block><block wx:if="{{$root.g1}}"><view class="post-images data-v-2c523c20"><swiper class="image-swiper data-v-2c523c20" indicator-dots="{{$root.g2>1}}" autoplay="{{false}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.5)" indicator-active-color="#fff"><block wx:for="{{postData.images}}" wx:for-item="img" wx:for-index="index" wx:key="index"><swiper-item class="data-v-2c523c20"><image class="swiper-image data-v-2c523c20" src="{{img}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" bindtap="__e"></image></swiper-item></block></swiper></view></block><block wx:if="{{postData.location}}"><view data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" class="location-info data-v-2c523c20" bindtap="__e"><u-icon vue-id="dd35d41a-3" name="map" color="#999" size="16" class="data-v-2c523c20" bind:__l="__l"></u-icon><text class="location-text data-v-2c523c20">{{postData.location}}</text></view></block><view class="post-stats data-v-2c523c20"><text class="stat-item data-v-2c523c20">{{postData.likeCount+"人点赞"}}</text><text class="stat-item data-v-2c523c20">{{postData.commentCount+"条评论"}}</text><text class="stat-item data-v-2c523c20">{{postData.shareCount+"次分享"}}</text></view><view class="action-bar data-v-2c523c20"><view data-event-opts="{{[['tap',[['toggleLike',['$event']]]]]}}" class="action-item data-v-2c523c20" bindtap="__e"><u-icon vue-id="dd35d41a-4" name="{{postData.isLiked?'heart-fill':'heart'}}" color="{{postData.isLiked?'#ff4757':'#666'}}" size="24" class="data-v-2c523c20" bind:__l="__l"></u-icon><text class="action-text data-v-2c523c20">{{"点赞 ("+(postData.likeCount||0)+")"}}</text></view><view data-event-opts="{{[['tap',[['focusComment',['$event']]]]]}}" class="action-item data-v-2c523c20" bindtap="__e"><u-icon vue-id="dd35d41a-5" name="chat" color="#666" size="24" class="data-v-2c523c20" bind:__l="__l"></u-icon><text class="action-text data-v-2c523c20">评论</text></view><view data-event-opts="{{[['tap',[['sharePost',['$event']]]]]}}" class="action-item data-v-2c523c20" bindtap="__e"><u-icon vue-id="dd35d41a-6" name="share" color="#666" size="24" class="data-v-2c523c20" bind:__l="__l"></u-icon><text class="action-text data-v-2c523c20">分享</text></view></view></view><view class="comments-section data-v-2c523c20"><view class="comments-header data-v-2c523c20"><text class="comments-title data-v-2c523c20">{{"评论 "+$root.g3}}</text><u-tabs vue-id="dd35d41a-7" list="{{tabList}}" current="{{currentTab}}" scrollable="{{false}}" activeColor="#2979ff" inactiveColor="#999" fontSize="28" lineColor="#2979ff" lineWidth="20" lineHeight="3" height="40" data-event-opts="{{[['^change',[['changeSortType']]]]}}" bind:change="__e" class="data-v-2c523c20" bind:__l="__l"></u-tabs></view><view class="comment-list data-v-2c523c20"><block wx:for="{{$root.l1}}" wx:for-item="comment" wx:for-index="__i1__" wx:key="id"><view class="comment-item data-v-2c523c20"><view data-event-opts="{{[['tap',[['goCommentUserProfile',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="user-avatar data-v-2c523c20" catchtap="__e"><u-avatar vue-id="{{'dd35d41a-8-'+__i1__}}" src="{{comment.$orig.userAvatar}}" size="40" class="data-v-2c523c20" bind:__l="__l"></u-avatar></view><view class="comment-content data-v-2c523c20"><view class="user-info-row data-v-2c523c20"><view class="user-info data-v-2c523c20"><view data-event-opts="{{[['tap',[['goCommentUserProfile',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="user-name data-v-2c523c20" catchtap="__e">{{''+comment.$orig.username+''}}<block wx:if="{{comment.$orig.level>=0}}"><view class="user-level data-v-2c523c20" style="{{'background-color:'+(comment.m1)+';'}}">{{"Lv."+comment.$orig.level}}</view></block></view><view class="time data-v-2c523c20">{{comment.m2}}</view></view><view data-event-opts="{{[['tap',[['toggleCommentLike',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="{{['like-btn','data-v-2c523c20',(comment.$orig.isLiked)?'liked':'']}}" catchtap="__e"><u-icon vue-id="{{'dd35d41a-9-'+__i1__}}" name="{{comment.$orig.isLiked?'heart-fill':'heart'}}" color="{{comment.$orig.isLiked?'#ff4757':'#666'}}" size="20" class="data-v-2c523c20" bind:__l="__l"></u-icon><text class="data-v-2c523c20">{{comment.$orig.likeCount||0}}</text></view></view><view class="text data-v-2c523c20"><text class="data-v-2c523c20">{{comment.$orig.showFullContent?comment.$orig.content:comment.g4>100?comment.g5+'...':comment.$orig.content}}</text><block wx:if="{{comment.g6>100}}"><view data-event-opts="{{[['tap',[['toggleContent',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="expand-btn data-v-2c523c20" catchtap="__e">{{''+(comment.$orig.showFullContent?'收起':'展开')+''}}</view></block></view><view class="actions data-v-2c523c20"><view data-event-opts="{{[['tap',[['replyComment',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="reply-btn data-v-2c523c20" catchtap="__e"><u-icon vue-id="{{'dd35d41a-10-'+__i1__}}" name="chat" color="#666" size="18" class="data-v-2c523c20" bind:__l="__l"></u-icon><text class="data-v-2c523c20">回复</text></view><view data-event-opts="{{[['tap',[['showMoreOptions',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="more-btn data-v-2c523c20" catchtap="__e"><u-icon vue-id="{{'dd35d41a-11-'+__i1__}}" name="more-dot-fill" color="#999" size="18" class="data-v-2c523c20" bind:__l="__l"></u-icon></view></view><block wx:if="{{comment.g7}}"><view class="reply-preview data-v-2c523c20"><block wx:for="{{comment.l0}}" wx:for-item="reply" wx:for-index="rIndex" wx:key="rIndex"><view class="reply-item data-v-2c523c20"><text data-event-opts="{{[['tap',[['goReplyUserProfile',['$0'],[[['commentList','id',comment.$orig.id],['replies.slice(0,2)','',rIndex]]]]]]]}}" class="reply-nickname data-v-2c523c20" catchtap="__e">{{reply.$orig.username}}</text><block wx:if="{{reply.$orig.replyTo}}"><text data-event-opts="{{[['tap',[['goReplyToUserProfile',['$0'],[[['commentList','id',comment.$orig.id],['replies.slice(0,2)','',rIndex,'replyTo']]]]]]]}}" class="reply-to data-v-2c523c20" catchtap="__e">{{"@"+reply.$orig.replyTo.username}}</text></block><text class="reply-content data-v-2c523c20">{{": "+(reply.g8>50?reply.g9+'...':reply.$orig.content)}}</text></view></block><block wx:if="{{comment.$orig.replyCount>2}}"><view data-event-opts="{{[['tap',[['viewAllReplies',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="view-more data-v-2c523c20" catchtap="__e">{{'查看全部'+comment.$orig.replyCount+'条回复 >'}}</view></block></view></block></view></view></block></view></view></scroll-view><view class="comment-input-bar data-v-2c523c20"><block wx:if="{{isReplyMode}}"><view class="reply-indicator data-v-2c523c20"><view class="reply-info data-v-2c523c20"><text class="reply-text data-v-2c523c20">{{"回复 @"+(currentReply&&currentReply.username?currentReply.username:'用户')}}</text><view data-event-opts="{{[['tap',[['cancelReplyMode',['$event']]]]]}}" class="cancel-reply-btn data-v-2c523c20" bindtap="__e"><text class="data-v-2c523c20">✕</text></view></view></view></block><view class="input-container data-v-2c523c20"><u-avatar vue-id="dd35d41a-12" src="{{currentUser.avatar}}" size="32" class="data-v-2c523c20" bind:__l="__l"></u-avatar><input class="comment-input data-v-2c523c20 vue-ref" placeholder="{{inputPlaceholder}}" data-ref="commentInput" data-event-opts="{{[['focus',[['onInputFocus',['$event']]]],['blur',[['onInputBlur',['$event']]]],['input',[['__set_model',['','commentText','$event',[]]]]]]}}" value="{{commentText}}" bindfocus="__e" bindblur="__e" bindinput="__e"/><text data-event-opts="{{[['tap',[['sendComment',['$event']]]]]}}" class="{{['send-btn','data-v-2c523c20',($root.g10)?'active':'']}}" bindtap="__e">发送</text></view></view><u-popup bind:input="__e" vue-id="dd35d41a-13" mode="bottom" border-radius="30" value="{{showMorePopup}}" data-event-opts="{{[['^input',[['__set_model',['','showMorePopup','$event',[]]]]]]}}" class="data-v-2c523c20" bind:__l="__l" vue-slots="{{['default']}}"><view class="action-popup data-v-2c523c20"><view data-event-opts="{{[['tap',[['replyFromMore',['$event']]]]]}}" class="action-item reply data-v-2c523c20" bindtap="__e"><view class="action-icon data-v-2c523c20"><u-icon vue-id="{{('dd35d41a-14')+','+('dd35d41a-13')}}" name="chat" color="#ff6b87" size="24" class="data-v-2c523c20" bind:__l="__l"></u-icon></view><text class="data-v-2c523c20">回复</text></view><view data-event-opts="{{[['tap',[['copyComment',['$event']]]]]}}" class="action-item copy data-v-2c523c20" bindtap="__e"><view class="action-icon data-v-2c523c20"><u-icon vue-id="{{('dd35d41a-15')+','+('dd35d41a-13')}}" name="file-text" color="#666" size="24" class="data-v-2c523c20" bind:__l="__l"></u-icon></view><text class="data-v-2c523c20">复制</text></view><block wx:if="{{$root.m3}}"><view data-event-opts="{{[['tap',[['deleteComment',['$event']]]]]}}" class="action-item delete data-v-2c523c20" bindtap="__e"><view class="action-icon data-v-2c523c20"><u-icon vue-id="{{('dd35d41a-16')+','+('dd35d41a-13')}}" name="trash" color="#f56c6c" size="24" class="data-v-2c523c20" bind:__l="__l"></u-icon></view><text class="data-v-2c523c20">删除</text></view></block><view data-event-opts="{{[['tap',[['reportComment',['$event']]]]]}}" class="action-item report data-v-2c523c20" bindtap="__e"><view class="action-icon data-v-2c523c20"><u-icon vue-id="{{('dd35d41a-17')+','+('dd35d41a-13')}}" name="info-circle" color="#999" size="24" class="data-v-2c523c20" bind:__l="__l"></u-icon></view><text class="data-v-2c523c20">举报</text></view></view></u-popup></view>