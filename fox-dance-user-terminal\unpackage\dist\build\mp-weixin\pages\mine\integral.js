(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/integral"],{"1d36":function(t,e,a){"use strict";a.r(e);var n=a("88e9"),o=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=o.a},"1eae":function(t,e,a){},"61f3":function(t,e,a){"use strict";a.r(e);var n=a("a8c7"),o=a("1d36");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("ed86");var i=a("828b"),c=Object(i["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=c.exports},"88e9":function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a("6061"),o={data:function(){return{isLogined:!1,isH5:!1,score:0,type:0,date_sj:"请选择",scoreLists:[],page:1,total_pages:1,zanwsj:!1,status:"loading",loadingText:"努力加载中",loadmoreText:"轻轻上拉",nomoreText:"实在没有了",imgbaseUrl:"",qjbutton:"#131315"}},created:function(){},onLoad:function(e){this.qjbutton=t.getStorageSync("storeInfo").button,this.scoreData()},methods:{tabTap:function(t){this.type=t,this.page=1,this.scoreLists=[],this.scoreData()},bindDateChange_sj:function(t){this.date_sj=t.detail.value,this.page=1,this.scoreLists=[],this.scoreData()},scoreData:function(){t.showLoading({title:"加载中"});var e=this;(0,n.scoreListApi)({page:e.page,size:10,date:"请选择"==e.date_sj?"":e.date_sj,type:e.type}).then((function(a){if(console.log("积分明细",a),1==a.code){var n=a.data.details.data;e.score=a.data.score,e.scoreLists=e.scoreLists.concat(n),e.zanwsj=!!e.scoreLists.length,e.page++,e.total_pages=a.data.details.last_page,1!=e.page&&(e.total_pages>=e.page?e.status="loading":e.status="nomore"),0==e.scoreLists.length?e.zanwsj=!0:e.zanwsj=!1,1*a.data.total<=10&&(e.status="nomore"),e.loding=!0,t.hideLoading(),t.stopPullDownRefresh()}}))},onReachBottom:function(){1!=this.page&&this.total_pages>=this.page&&this.scoreData()},onPullDownRefresh:function(){console.log("我被下拉了"),this.page=1,this.scoreLists=[],this.scoreData()},userData:function(){t.showLoading({title:"加载中"});userDetailApi().then((function(e){1==e.code&&(console.log("个人信息",e),t.hideLoading())}))},navTo:function(e){t.navigateTo({url:e})}}};e.default=o}).call(this,a("df3c")["default"])},"8c5a":function(t,e,a){"use strict";(function(t,e){var n=a("47a9");a("cff9");n(a("3240"));var o=n(a("61f3"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},a8c7:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},o=[]},ed86:function(t,e,a){"use strict";var n=a("1eae"),o=a.n(n);o.a}},[["8c5a","common/runtime","common/vendor"]]]);