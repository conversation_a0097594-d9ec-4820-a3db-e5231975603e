<view class="test-container data-v-278491ce"><view class="header data-v-278491ce"><text class="title data-v-278491ce">组件测试页面</text></view><scroll-view class="content data-v-278491ce" scroll-y="{{true}}"><view class="test-section data-v-278491ce"><text class="section-title data-v-278491ce">基础组件测试</text><view class="test-item data-v-278491ce"><text class="test-label data-v-278491ce">头像组件:</text><u-avatar vue-id="0e405898-1" src="https://picsum.photos/100/100?random=1" size="40" class="data-v-278491ce" bind:__l="__l"></u-avatar></view><view class="test-item data-v-278491ce"><text class="test-label data-v-278491ce">图标组件:</text><u-icon vue-id="0e405898-2" name="heart" color="#ff4757" size="24" class="data-v-278491ce" bind:__l="__l"></u-icon><u-icon vue-id="0e405898-3" name="chat" color="#2979ff" size="24" class="data-v-278491ce" bind:__l="__l"></u-icon><u-icon vue-id="0e405898-4" name="share" color="#52c41a" size="24" class="data-v-278491ce" bind:__l="__l"></u-icon></view><view class="test-item data-v-278491ce"><text class="test-label data-v-278491ce">按钮组件:</text><u-button vue-id="0e405898-5" type="primary" text="主要按钮" size="small" class="data-v-278491ce" bind:__l="__l"></u-button><u-button vue-id="0e405898-6" type="default" text="默认按钮" size="small" class="data-v-278491ce" bind:__l="__l"></u-button></view><view class="test-item data-v-278491ce"><text class="test-label data-v-278491ce">加载组件:</text><u-loading vue-id="0e405898-7" mode="circle" size="24" class="data-v-278491ce" bind:__l="__l"></u-loading></view></view><view class="test-section data-v-278491ce"><text class="section-title data-v-278491ce">页面导航测试</text><view class="nav-buttons data-v-278491ce"><u-button vue-id="0e405898-8" type="primary" text="首页" data-event-opts="{{[['^click',[['goPage',['/pagesSub/social/home/<USER>']]]]]}}" bind:click="__e" class="data-v-278491ce" bind:__l="__l"></u-button><u-button vue-id="0e405898-9" type="success" text="发现" data-event-opts="{{[['^click',[['goPage',['/pagesSub/social/discover/index']]]]]}}" bind:click="__e" class="data-v-278491ce" bind:__l="__l"></u-button><u-button vue-id="0e405898-10" type="warning" text="发布" data-event-opts="{{[['^click',[['goPage',['/pagesSub/social/publish/index']]]]]}}" bind:click="__e" class="data-v-278491ce" bind:__l="__l"></u-button><u-button vue-id="0e405898-11" type="error" text="消息" data-event-opts="{{[['^click',[['goPage',['/pagesSub/social/message/index']]]]]}}" bind:click="__e" class="data-v-278491ce" bind:__l="__l"></u-button><u-button vue-id="0e405898-12" type="info" text="我的" data-event-opts="{{[['^click',[['goPage',['/pagesSub/social/profile/index']]]]]}}" bind:click="__e" class="data-v-278491ce" bind:__l="__l"></u-button></view></view><view class="test-section data-v-278491ce"><text class="section-title data-v-278491ce">测试状态</text><view class="status-list data-v-278491ce"><view class="status-item success data-v-278491ce"><u-icon vue-id="0e405898-13" name="checkmark-circle" color="#52c41a" size="16" class="data-v-278491ce" bind:__l="__l"></u-icon><text class="status-text data-v-278491ce">uni.promisify.adaptor.js 已修复</text></view><view class="status-item success data-v-278491ce"><u-icon vue-id="0e405898-14" name="checkmark-circle" color="#52c41a" size="16" class="data-v-278491ce" bind:__l="__l"></u-icon><text class="status-text data-v-278491ce">u-loading 组件已替换</text></view><view class="status-item success data-v-278491ce"><u-icon vue-id="0e405898-15" name="checkmark-circle" color="#52c41a" size="16" class="data-v-278491ce" bind:__l="__l"></u-icon><text class="status-text data-v-278491ce">页面路由配置完成</text></view><view class="status-item success data-v-278491ce"><u-icon vue-id="0e405898-16" name="checkmark-circle" color="#52c41a" size="16" class="data-v-278491ce" bind:__l="__l"></u-icon><text class="status-text data-v-278491ce">uview-ui 组件正常</text></view></view></view><view class="test-section data-v-278491ce"><u-button vue-id="0e405898-17" type="primary" size="large" text="返回演示页面" data-event-opts="{{[['^click',[['goPage',['/pagesSub/social/demo/index']]]]]}}" bind:click="__e" class="data-v-278491ce" bind:__l="__l"></u-button></view></scroll-view></view>