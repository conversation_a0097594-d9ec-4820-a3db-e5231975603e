<template>
	<view class="pointsMall">
		
		
		<!-- <view class="poi_one" style="-margin-top: 1000rpx;">
			<view class="poi_one_l"><view>{{score}}</view><text>可用积分</text></view>
			<view class="poi_one_r" @click="navTo('/pages/mine/integral')"><image src="/static/images/icon30.png"></image><text>积分明细</text></view>
			<view class="poi_one_r" @click="navTo('/pages/mine/order/order')"><image src="/static/images/icon31.png"></image><text>商城订单</text></view>
		</view> -->
		
		<view class="poi_two">
			<view class="poi_two_l" @click="navTo('/pages/mine/order/order')">商城订单</view>
			<view class="poi_two_r">
				<view class="les_search_l" @click="navTo('/pages/buy/pointsMall/search','1')"><image src="/static/images/search.png"></image><input type="text" :disabled="true" placeholder-style="color:#999999" placeholder="请搜索你想要的商品" /></view>
			</view>
		</view>
		
		<view class="poi_thr" :class="scrollTop >= 225 ? 'poi_thr_fixed' : ''">
			<view class="poi_thr_l">
				<!-- <view class="poi_thr_l_li" :class="shopCateIndex == -1 ? 'poi_thr_l_li_ac' : ''" @click="shopCateTap(-1)"><text></text><view>全部商品</view></view> -->
				<view class="poi_thr_l_li" :class="shopCateIndex == index ? 'poi_thr_l_li_ac' : ''" v-for="(item,index) in shopCate" :key="index" @click="shopCateTap(index)">
					<text></text>
					<view>{{item.name}}</view>
					<image src="/static/images/yj1.png" class="yj1" v-if="shopCateIndex == index"></image>
					<image src="/static/images/yj2.png" class="yj2" v-if="shopCateIndex == index"></image>
				</view>
			</view>
			<view class="poi_thr_r">
				<view class="poi_thr_r_li" v-for="(item,index) in mallLists" :key="index" @click="navTo('/pages/buy/pointsMall/productDetails?id=' + item.id,'1')">
					<image :src="imgbaseUrl + item.image" mode="aspectFill" class="poi_thr_r_li_l"></image>
					<view class="poi_thr_r_li_r">
						<view class="poi_thr_r_li_r_a">{{item.name}}</view>
						<view class="poi_thr_r_li_r_b">
							<text>￥{{item.redeem_points}}</text>
							<view @click.stop="dhTap(item)">购买</view>
						</view>
					</view>
				</view>
				<view class="gg_loding" v-if="!zanwsj">
					<view class="loader-inner ball-clip-rotate" v-if="status == 'loading'">
						<view></view>
						<text>加载中</text>
					</view>
					<view class="gg_loding_wusj" v-else>─── 没有更多数据了 ───</view>
				</view>
				<view class="gg_zwsj" v-if="zanwsj">
					<view class="gg_zwsj_w">
						<image src="/static/images/wusj.png" mode="widthFix"></image>
						<text>暂无数据</text>
					</view>
				</view>
			</view>
		</view>
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
		<shoppingselect ref="shopCar" />
		
	</view>
</template>


<script>
import {
	userInfoApi,
	mallCategoryApi,
	mallListsApi,
} from '@/config/http.achieve.js'
import shoppingselect from "@/pages/buy/specification.vue"
export default {
	components: {
		shoppingselect,
	},
	data() {
		return {
			isLogined:true,
			bgColor:'#fff',
			shopCateIndex:0,
			shopCate:[],
			mallLists:[],
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			scrollTop:0,
			score:0,
			imgbaseUrl:''
		}
	},
	onShow() {
		
	},
	methods: {
		onLoadData(){
			this.isLogined = uni.getStorageSync('token') ? true : false;
			if(this.isLogined){
				this.userData();//个人信息
				console.log(this.$baseUrl,'this.$baseUrl')
			}else{
				this.loding = true;
			}
			this.imgbaseUrl = this.$baseUrl;
			console.log('onshow哈哈');
			this.categoryData();//分类
			this.mallLists = [];
			this.page = 1;
			this.mallData();//积分商城
		},
		//积分商城
		mallData(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			mallListsApi({
				cate_id:that.shopCateIndex == 0 ? 0 : that.shopCate[that.shopCateIndex].id,
				page:that.page,
				size:10,
			}).then(res => {
				console.log('积分商城',res)
				if (res.code == 1) {
					/*var obj = [
						{
							id: 1,
							image: "/storage/default/20241023/O1CN01v2BipQ1Pk522c5aa07389ab876c7f9c9d4fd52c8fc26d9a76.png",
							name: "印尼进口营多捞面速食泡面夜宵食品网红拉面拌面方便面整箱",
							redeem_points: "100",
						},
					]*/
					var obj = res.data.data;
					that.score = res.data.score
					that.mallLists = that.mallLists.concat(obj);
					that.zanwsj = that.mallLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.mallLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
			
		},
		onReachBottomData() {
			console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.mallData();
				}
			}
		},
		onReachBottom() {
			// console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.mallData();
				}
			}
		},
		onPullDownRefresh: function() {
		    // console.log('我被下拉了');
		    this.page = 1;
			this.mallLists = [];
			this.mallData();//积分商城
		},
		//分类
		categoryData(){
			/*uni.showLoading({
				title: '加载中'
			});*/
			let that = this;
			mallCategoryApi({}).then(res => {
				console.log('分类',res)
				if (res.code == 1) {
					/*var obj = [
						{id: 100,name: "水果1"},
						{id: 100,name: "水果2"},
					]*/
					that.shopCate = res.data;
					// that.shopCate = obj;
					// uni.hideLoading();
				}
			})
		},
		//个人信息
		userData(){
			/*uni.showLoading({
				title: '加载中'
			});*/
			let that = this;
			userInfoApi({}).then(res => {
				console.log('个人中心',res)
				if (res.code == 1) {
					that.score = res.data.score;
					// uni.hideLoading();
				}
			})
		},
		onPageScrollData(e){
			this.scrollTop = e;
			// console.log(e,'执行')
		},
		shopCateTap(index){
			this.shopCateIndex = index;
			this.mallLists = [];
			this.page = 1;
			this.mallData();//积分商城
		},
		//兑换
		dhTap(item){
			// console.log(item.spec_data)
			// if(this.isLogined){
				this.$refs.shopCar.startTanc(item);
				return false;
				/*if(this.score < item.redeem_points*1){
					uni.showToast({
						icon: 'none',
						title: '积分不足',
						duration:2000
					});
					return false;
				}*/
				var productxq = JSON.stringify(item.spec_data)
				uni.navigateTo({
					url:'/pages/buy/pointsMall/confirmOrder?productxq=' + productxq
				})
		// 	}else{
		// 		uni.showToast({
		// 			icon: 'none',
		// 			title: '请先登录',
		// 			duration:2000
		// 		});
		// 		setTimeout(function(){
		// 			uni.navigateTo({
		// 				url: '/pages/login/login'
		// 			})
		// 		},1000)
		// 	}
		},
		navTo(url,ismd){
			if(ismd){
				uni.navigateTo({
					url:url
				});
				return false;
			}
			var that = this;
			if(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
			}else{
				uni.navigateTo({
					url:url
				})
			}
		},
		
	}
}
</script>

<style lang="scss">
.pointsMall{overflow: hidden;}
page{padding-bottom: 0;background:#fff;}
</style>