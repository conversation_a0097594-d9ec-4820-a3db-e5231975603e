<template>
  <view class="post-card" @click="goPostDetail">
    <!-- 封面图片 -->
    <view class="cover-container">
      <image
        :src="post.coverImage || (post.images && post.images[0]) || defaultCover"
        class="cover-image"
        mode="aspectFill"
        @error="onImageError"
        @load="onImageLoad"
      />
      <view v-if="imageError" class="image-placeholder">
        <u-icon name="image" color="#ccc" size="32"></u-icon>
        <text class="placeholder-text">图片加载失败</text>
      </view>

      <!-- 私密标识 -->
      <view v-if="post.isPublic === 0" class="private-badge">
        <u-icon name="lock" color="#fff" size="12"></u-icon>
        <text class="private-text">私密</text>
      </view>

      <!-- 草稿标识 -->
      <view v-if="post.status === 0" class="draft-badge">
        <u-icon name="edit-pen" color="#fff" size="12"></u-icon>
        <text class="draft-text">草稿</text>
      </view>
    </view>

    <!-- 帖子标题 -->
    <view class="post-title">
      <text class="title-text">{{ post.title || post.content || '无标题' }}</text>
    </view>

    <!-- 底部信息 -->
    <view class="post-footer">
      <!-- 左侧：用户信息 -->
      <view class="user-info" @click.stop="goUserProfile">
        <u-avatar :src="post.userAvatar" size="24"></u-avatar>
        <text class="username">{{ post.username }}</text>
      </view>

      <!-- 右侧：点赞数 -->
      <view class="like-info">
        <u-icon
          :name="post.isLiked ? 'heart-fill' : 'heart'"
          :color="post.isLiked ? '#ff4757' : '#999'"
          size="16"
        ></u-icon>
        <text class="like-count">{{ formatLikeCount(post.likeCount) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "PostCard",
  props: {
    post: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      imageError: false,
      defaultCover: "https://picsum.photos/300/200?random=default"
    };
  },
  methods: {
    formatLikeCount(count) {
      if (!count || count === 0) return "0";
      if (count < 1000) return count.toString();
      if (count < 10000) return (count / 1000).toFixed(1) + "k";
      if (count < 1000000) return Math.floor(count / 1000) + "k";
      return (count / 1000000).toFixed(1) + "M";
    },

    onImageError() {
      this.imageError = true;
    },

    onImageLoad() {
      this.imageError = false;
    },

    goPostDetail() {
      this.$emit("click", this.post);
    },

    goUserProfile() {
      this.$emit("user-click", this.post);
    }
  }
};
</script>

<style lang="scss" scoped>
.post-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.post-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.12);
}

.cover-container {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.post-card:hover .cover-image {
  transform: scale(1.05);
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.post-title {
  padding: 12px 12px 8px;
}

.title-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.post-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px 12px;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.user-info:active {
  opacity: 0.7;
}

.username {
  font-size: 12px;
  color: #666;
  margin-left: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.like-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.like-count {
  font-size: 12px;
  color: #999;
  font-weight: 500;
}

/* 私密标识 */
.private-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.private-text {
  font-size: 20rpx;
  color: #fff;
  line-height: 1;
}

/* 草稿标识 */
.draft-badge {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: rgba(255, 152, 0, 0.8);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.draft-text {
  font-size: 20rpx;
  color: #fff;
  line-height: 1;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .cover-container {
    height: 140px;
  }

  .title-text {
    font-size: 13px;
  }

  .username,
  .like-count {
    font-size: 11px;
  }
}
</style>
