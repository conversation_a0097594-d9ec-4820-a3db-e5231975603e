<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="<EMAIL>">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||admin_foxdance_c||PROCESS|G
|root||admin_foxdance_c||REPLICATION CLIENT|G
|root||admin_foxdance_c||REPLICATION SLAVE|G
|root||admin_foxdance_c||XA_RECOVER_ADMIN|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||ALTER|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||ALTER ROUTINE|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||CREATE|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||CREATE ROUTINE|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||CREATE TEMPORARY TABLES|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||CREATE VIEW|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||DELETE|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||DROP|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||EVENT|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||EXECUTE|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||INDEX|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||INSERT|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||LOCK TABLES|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||REFERENCES|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||SELECT|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||SHOW VIEW|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||TRIGGER|G
admin\\_foxdance\\_c|schema||admin_foxdance_c||UPDATE|G</Grants>
      <ServerVersion>8.0.36</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="admin_foxdance_c">
      <Current>1</Current>
      <LastIntrospectionLocalTimestamp>2025-07-27.13:46:51</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="mysql">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <user id="292" parent="1" name="admin_foxdance_c"/>
    <table id="293" parent="288" name="auth_check_root">
      <Comment>用户</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="294" parent="288" name="ba_activation_record">
      <Comment>激活记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="295" parent="288" name="ba_addr">
      <Comment>用户地址</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="296" parent="288" name="ba_admin">
      <Comment>管理员表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="297" parent="288" name="ba_admin_group">
      <Comment>管理分组表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="298" parent="288" name="ba_admin_group_access">
      <Comment>管理分组映射表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="299" parent="288" name="ba_admin_log">
      <Comment>管理员日志表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="300" parent="288" name="ba_admin_rule">
      <Comment>菜单和权限规则表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="301" parent="288" name="ba_application">
      <Comment>应用</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="302" parent="288" name="ba_appointment_record">
      <Comment>预约课程记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="303" parent="288" name="ba_area">
      <Comment>省份地区表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="304" parent="288" name="ba_attachment">
      <Comment>附件表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="305" parent="288" name="ba_automatic_reply">
      <Comment>关键词回复</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="306" parent="288" name="ba_auxiliary">
      <Comment>辅助表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="307" parent="288" name="ba_cache">
      <Comment>数据缓存表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="308" parent="288" name="ba_captcha">
      <Comment>验证码表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="309" parent="288" name="ba_card_activity">
      <Comment>会员卡活动列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="310" parent="288" name="ba_card_record">
      <Comment>充值记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="311" parent="288" name="ba_card_record_copy">
      <Comment>充值记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="312" parent="288" name="ba_card_record_copy1">
      <Comment>充值记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="313" parent="288" name="ba_card_transfer_record">
      <Comment>转卡记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="314" parent="288" name="ba_carousel_image">
      <Comment>轮播图列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="315" parent="288" name="ba_category">
      <Comment>分类列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="316" parent="288" name="ba_chat_auto_reply">
      <Comment>自动回复</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="317" parent="288" name="ba_chat_service">
      <Comment>客服表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="318" parent="288" name="ba_chat_service_dialogue_record">
      <Comment>用户和客服对话记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="319" parent="288" name="ba_chat_service_group">
      <Comment>客服分组</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="320" parent="288" name="ba_chat_service_record">
      <Comment>聊天记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="321" parent="288" name="ba_chat_service_speechcraft">
      <Comment>客服话术</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="322" parent="288" name="ba_check_in_record">
      <Comment>打卡记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="323" parent="288" name="ba_config">
      <Comment>系统配置</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="324" parent="288" name="ba_coupon">
      <Comment>优惠券列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="325" parent="288" name="ba_course">
      <Comment>课程列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="326" parent="288" name="ba_course_contents">
      <Comment>课包目录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="327" parent="288" name="ba_course_package">
      <Comment>课包列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="328" parent="288" name="ba_course_package_order">
      <Comment>课包订单</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="329" parent="288" name="ba_course_package_record">
      <Comment>观看课包记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="330" parent="288" name="ba_course_updates">
      <Comment>课程动态</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="331" parent="288" name="ba_crud_log">
      <Comment>CRUD记录表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="332" parent="288" name="ba_dataexport">
      <Comment>导出任务表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="333" parent="288" name="ba_employee_leave_record">
      <Comment>员工请假记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="334" parent="288" name="ba_examples_amap">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="335" parent="288" name="ba_examples_table_cell_pre">
      <Comment>预设渲染方案</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="336" parent="288" name="ba_examples_table_cell_slot">
      <Comment>单元格slot渲染</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="337" parent="288" name="ba_examples_table_dialog">
      <Comment>详情按钮和弹窗</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="338" parent="288" name="ba_examples_table_dialog2">
      <Comment>详情按钮和弹窗2</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="339" parent="288" name="ba_examples_table_event">
      <Comment>表格事件监听</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="340" parent="288" name="ba_examples_table_event2">
      <Comment>表格事件监听2</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="341" parent="288" name="ba_examples_table_expand">
      <Comment>展开行</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="342" parent="288" name="ba_examples_table_fixed">
      <Comment>固定列固定表头</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="343" parent="288" name="ba_examples_table_form_edit">
      <Comment>数据编辑之前预处理</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="344" parent="288" name="ba_examples_table_form_other">
      <Comment>表单其他示例</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="345" parent="288" name="ba_examples_table_form_submit">
      <Comment>表单提交前数据处理</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="346" parent="288" name="ba_examples_table_header_btn">
      <Comment>自定义表头按钮</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="347" parent="288" name="ba_examples_table_method">
      <Comment>操作表格的方法</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="348" parent="288" name="ba_examples_table_mheader">
      <Comment>多级表头示例</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="349" parent="288" name="ba_examples_table_refresh">
      <Comment>编程式刷新表格</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="350" parent="288" name="ba_examples_table_span">
      <Comment>合并行或列</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="351" parent="288" name="ba_examples_table_status">
      <Comment>带状态表格</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="352" parent="288" name="ba_examples_table_summary">
      <Comment>表尾合计行</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="353" parent="288" name="ba_examples_table_tree">
      <Comment>树形数据</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="354" parent="288" name="ba_examples_table_treetable">
      <Comment>树状表格示例表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="355" parent="288" name="ba_examples_tencentmap">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="356" parent="288" name="ba_express">
      <Comment>快递公司编码</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="357" parent="288" name="ba_feedback">
      <Comment>反馈列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="358" parent="288" name="ba_goods">
      <Comment>商品列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="359" parent="288" name="ba_goods_order">
      <Comment>购买记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="360" parent="288" name="ba_invitation_record">
      <Comment>邀请记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="361" parent="288" name="ba_jackpot">
      <Comment>奖池列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="362" parent="288" name="ba_joy_spec_goods">
      <Comment>商品表(规格组件)</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="363" parent="288" name="ba_joy_spec_goods_sku">
      <Comment>商品sku表(规格组件)</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="364" parent="288" name="ba_joy_spec_goods_spec_template">
      <Comment>商品规格模板表(规格组件)</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="365" parent="288" name="ba_leave_record">
      <Comment>请假记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="366" parent="288" name="ba_level">
      <Comment>等级列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="367" parent="288" name="ba_lottery_records">
      <Comment>抽奖记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="368" parent="288" name="ba_member_card">
      <Comment>会员卡列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="369" parent="288" name="ba_message">
      <Comment>消息通知记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="370" parent="288" name="ba_message_template">
      <Comment>消息模板</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="371" parent="288" name="ba_migrations">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="372" parent="288" name="ba_mirror_image">
      <Comment>创建镜像记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="373" parent="288" name="ba_notice">
      <Comment>公告列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="374" parent="288" name="ba_order_record">
      <Comment>充值记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="375" parent="288" name="ba_performance">
      <Comment>门店业绩记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="376" parent="288" name="ba_record">
      <Comment>用户消费记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="377" parent="288" name="ba_renewal_record">
      <Comment>续费记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="378" parent="288" name="ba_school_timetable">
      <Comment>课程表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="379" parent="288" name="ba_school_timetable_copy1">
      <Comment>课程表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="380" parent="288" name="ba_security_data_recycle">
      <Comment>回收规则表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="381" parent="288" name="ba_security_data_recycle_log">
      <Comment>数据回收记录表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="382" parent="288" name="ba_security_sensitive_data">
      <Comment>敏感数据规则表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="383" parent="288" name="ba_security_sensitive_data_log">
      <Comment>敏感数据修改记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="384" parent="288" name="ba_staff">
      <Comment>会员表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="385" parent="288" name="ba_store">
      <Comment>门店列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="386" parent="288" name="ba_store_registration_record">
      <Comment>绑定门店列表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="387" parent="288" name="ba_system_admin">
      <Comment>后台管理员表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="388" parent="288" name="ba_system_attachment">
      <Comment>附件管理表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="389" parent="288" name="ba_teacher">
      <Comment>老师表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="390" parent="288" name="ba_test_build">
      <Comment>知识库表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="391" parent="288" name="ba_token">
      <Comment>用户Token表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="392" parent="288" name="ba_transfer_record">
      <Comment>转让记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="393" parent="288" name="ba_user">
      <Comment>员工表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="394" parent="288" name="ba_user_coupon">
      <Comment>用户优惠券</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="395" parent="288" name="ba_user_group">
      <Comment>会员组表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="396" parent="288" name="ba_user_money_log">
      <Comment>会员余额变动表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="397" parent="288" name="ba_user_rule">
      <Comment>会员菜单权限规则表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="398" parent="288" name="ba_user_score_log">
      <Comment>会员积分变动表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="399" parent="288" name="ba_wechat_offiaccount_autoreply">
      <Comment>公众号自动回复</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="400" parent="288" name="ba_wechat_offiaccount_material">
      <Comment>微信公众号素材</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="401" parent="288" name="ba_work_report">
      <Comment>工作报表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="402" parent="288" name="ba_working_date">
      <Comment>上班日期</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="403" parent="288" name="comment_likes">
      <Comment>评论点赞记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="404" parent="288" name="comment_replies">
      <Comment>评论回复表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="405" parent="288" name="comments">
      <Comment>评论表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="406" parent="288" name="metro_lines">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="407" parent="288" name="notifications">
      <Comment>消息通知表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="408" parent="288" name="post_favorites">
      <Comment>帖子收藏表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="409" parent="288" name="post_likes">
      <Comment>帖子点赞表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="410" parent="288" name="post_shares">
      <Comment>帖子分享记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="411" parent="288" name="post_stats">
      <Comment>帖子统计表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="412" parent="288" name="post_tags">
      <Comment>帖子标签关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="413" parent="288" name="posts">
      <Comment>帖子表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="414" parent="288" name="private_conversations">
      <Comment>私信会话表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="415" parent="288" name="private_messages">
      <Comment>私信消息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="416" parent="288" name="reply_likes">
      <Comment>回复点赞记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="417" parent="288" name="reports">
      <Comment>举报表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="418" parent="288" name="system_configs">
      <Comment>系统配置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="419" parent="288" name="tags">
      <Comment>标签表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="420" parent="288" name="topics">
      <Comment>话题表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="421" parent="288" name="user_follows">
      <Comment>用户关注表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="422" parent="288" name="user_stats">
      <Comment>用户统计表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="423" parent="288" name="vote_info">
      <Comment>投票信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="424" parent="288" name="vote_records">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <view id="425" parent="288" name="v_post_details">
      <Definer>admin_foxdance_c@%</Definer>
      <SourceTextLength>948</SourceTextLength>
    </view>
    <view id="426" parent="288" name="v_user_stats">
      <Definer>admin_foxdance_c@%</Definer>
      <SourceTextLength>439</SourceTextLength>
    </view>
    <column id="427" parent="293" name="id">
      <AutoIncrement>1931232191286198274</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="428" parent="293" name="userAccount">
      <Comment>账号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="429" parent="293" name="userPassword">
      <Comment>密码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="430" parent="293" name="unionId">
      <Comment>微信开放平台id</Comment>
      <Position>4</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="431" parent="293" name="mpOpenId">
      <Comment>公众号openId</Comment>
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="432" parent="293" name="userName">
      <Comment>用户昵称</Comment>
      <Position>6</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="433" parent="293" name="userAvatar">
      <Comment>用户头像</Comment>
      <Position>7</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="434" parent="293" name="userProfile">
      <Comment>用户简介</Comment>
      <Position>8</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="435" parent="293" name="userRole">
      <Comment>用户角色：user/admin/ban</Comment>
      <DefaultExpression>&apos;user&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="436" parent="293" name="createTime">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="437" parent="293" name="updateTime">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="438" parent="293" name="isDelete">
      <Comment>是否删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="439" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="440" parent="293" name="idx_unionId">
      <ColNames>unionId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="441" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="442" parent="294" name="id">
      <AutoIncrement>8137</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="443" parent="294" name="card_id">
      <Comment>所属会员卡</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="444" parent="294" name="operator_id">
      <Comment>操作人</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="445" parent="294" name="notes">
      <Comment>备注</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="446" parent="294" name="end_time">
      <Comment>结束时间</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="447" parent="294" name="effective_days">
      <Comment>有效天数</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="448" parent="294" name="activation_time">
      <Comment>激活时间</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="449" parent="294" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="450" parent="294" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="451" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="452" parent="295" name="id">
      <AutoIncrement>91</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="453" parent="295" name="uid">
      <Comment>用户id</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="454" parent="295" name="name">
      <Comment>收货人姓名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="455" parent="295" name="phone">
      <Comment>收货人电话</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(16)|0s</StoredType>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="456" parent="295" name="area">
      <Comment>收货人地区</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="457" parent="295" name="detail">
      <Comment>收货人详细地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(256)|0s</StoredType>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="458" parent="295" name="is_default">
      <Comment>是否默认</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="459" parent="295" name="create_time">
      <Comment>添加时间</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="460" parent="295" name="update_time">
      <Comment>编辑时间</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="461" parent="295" name="gender">
      <Comment>性别:1=男,2=女</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="462" parent="295" name="label">
      <Comment>标签</Comment>
      <Position>11</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <index id="463" parent="295" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="464" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="465" parent="296" name="id">
      <AutoIncrement>202</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="466" parent="296" name="username">
      <Comment>用户名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="467" parent="296" name="nickname">
      <Comment>昵称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="468" parent="296" name="avatar">
      <Comment>头像</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="469" parent="296" name="email">
      <Comment>邮箱</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="470" parent="296" name="mobile">
      <Comment>手机</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(11)|0s</StoredType>
    </column>
    <column id="471" parent="296" name="login_failure">
      <Comment>登录失败次数</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="472" parent="296" name="last_login_time">
      <Comment>上次登录时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="473" parent="296" name="last_login_ip">
      <Comment>上次登录IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="474" parent="296" name="password">
      <Comment>密码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="475" parent="296" name="salt">
      <Comment>密码盐</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="476" parent="296" name="motto">
      <Comment>签名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="477" parent="296" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="478" parent="296" name="update_time">
      <Comment>更新时间</Comment>
      <Position>14</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="479" parent="296" name="create_time">
      <Comment>创建时间</Comment>
      <Position>15</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="480" parent="296" name="store_id">
      <Comment>所属门店</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="481" parent="296" name="entry_time">
      <Comment>入职时间</Comment>
      <Position>17</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="482" parent="296" name="gender">
      <Comment>性别:1=男,2=女</Comment>
      <Position>18</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="483" parent="296" name="type">
      <Comment>类型:0=管理员,1=员工,2=老师</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="484" parent="296" name="secondary_card_contract">
      <Comment>次卡合同模板</Comment>
      <Position>20</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="485" parent="296" name="duration_card_contract">
      <Comment>时长卡合同模板</Comment>
      <Position>21</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="486" parent="296" name="teacher_id">
      <Comment>关联老师</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="487" parent="296" name="id_number">
      <Comment>身份证号</Comment>
      <Position>23</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="488" parent="296" name="store_ids">
      <Comment>所管理的门店</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="489" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="490" parent="296" name="mobile">
      <ColNames>mobile</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="491" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="492" parent="296" name="mobile">
      <UnderlyingIndexName>mobile</UnderlyingIndexName>
    </key>
    <column id="493" parent="297" name="id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="494" parent="297" name="pid">
      <Comment>上级分组</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="495" parent="297" name="name">
      <Comment>组名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="496" parent="297" name="rules">
      <Comment>权限规则ID</Comment>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="497" parent="297" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="498" parent="297" name="update_time">
      <Comment>更新时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="499" parent="297" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="500" parent="297" name="store_id">
      <Comment>所管理的门店</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="501" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="502" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="503" parent="298" name="uid">
      <Comment>管理员ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="504" parent="298" name="group_id">
      <Comment>分组ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <index id="505" parent="298" name="uid">
      <ColNames>uid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="506" parent="298" name="group_id">
      <ColNames>group_id</ColNames>
      <Type>btree</Type>
    </index>
    <column id="507" parent="299" name="id">
      <AutoIncrement>70319</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="508" parent="299" name="admin_id">
      <Comment>管理员ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="509" parent="299" name="username">
      <Comment>管理员用户名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="510" parent="299" name="url">
      <Comment>操作Url</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(1500)|0s</StoredType>
    </column>
    <column id="511" parent="299" name="title">
      <Comment>日志标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="512" parent="299" name="data">
      <Comment>请求数据</Comment>
      <Position>6</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="513" parent="299" name="ip">
      <Comment>IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="514" parent="299" name="useragent">
      <Comment>User-Agent</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="515" parent="299" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="516" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="517" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="518" parent="300" name="id">
      <AutoIncrement>521</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="519" parent="300" name="pid">
      <Comment>上级菜单</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="520" parent="300" name="type">
      <Comment>类型:menu_dir=菜单目录,menu=菜单项,button=页面按钮</Comment>
      <DefaultExpression>&apos;menu&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>enum(&apos;menu_dir&apos;, &apos;menu&apos;, &apos;button&apos;)|0e</StoredType>
    </column>
    <column id="521" parent="300" name="title">
      <Comment>标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="522" parent="300" name="name">
      <Comment>规则名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="523" parent="300" name="path">
      <Comment>路由路径</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="524" parent="300" name="icon">
      <Comment>图标</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="525" parent="300" name="menu_type">
      <Comment>菜单类型:tab=选项卡,link=链接,iframe=Iframe</Comment>
      <Position>8</Position>
      <StoredType>enum(&apos;tab&apos;, &apos;link&apos;, &apos;iframe&apos;)|0e</StoredType>
    </column>
    <column id="526" parent="300" name="url">
      <Comment>Url</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="527" parent="300" name="component">
      <Comment>组件路径</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="528" parent="300" name="keepalive">
      <Comment>缓存:0=关闭,1=开启</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="529" parent="300" name="extend">
      <Comment>扩展属性:none=无,add_rules_only=只添加为路由,add_menu_only=只添加为菜单</Comment>
      <DefaultExpression>&apos;none&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>enum(&apos;none&apos;, &apos;add_rules_only&apos;, &apos;add_menu_only&apos;)|0e</StoredType>
    </column>
    <column id="530" parent="300" name="remark">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="531" parent="300" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="532" parent="300" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="533" parent="300" name="update_time">
      <Comment>更新时间</Comment>
      <Position>16</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="534" parent="300" name="create_time">
      <Comment>创建时间</Comment>
      <Position>17</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="535" parent="300" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="536" parent="300" name="pid">
      <ColNames>pid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="537" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="538" parent="301" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="539" parent="301" name="name">
      <Comment>应用名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="540" parent="301" name="appid">
      <Comment>应用ID</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="541" parent="301" name="app_secret">
      <Comment>应用KEY</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="542" parent="301" name="icon">
      <Comment>应用图标</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="543" parent="301" name="introduce">
      <Comment>应用介绍</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="544" parent="301" name="timestamp">
      <Comment>TOKEN生成时间戳</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="545" parent="301" name="rand">
      <Comment>TOKEN携带随机数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="546" parent="301" name="token">
      <Comment>TOKEN</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="547" parent="301" name="token_md5">
      <Comment>短TOKEN</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="548" parent="301" name="is_delete">
      <Comment>是否删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="549" parent="301" name="create_time">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="550" parent="301" name="update_time">
      <Position>13</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="551" parent="301" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="552" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="553" parent="302" name="id">
      <AutoIncrement>446435</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="554" parent="302" name="uid">
      <Comment>所属用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="555" parent="302" name="name">
      <Comment>课程名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="556" parent="302" name="teacher_id">
      <Comment>任课老师</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="557" parent="302" name="duration">
      <Comment>课程时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="558" parent="302" name="video">
      <Comment>课程视频</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="559" parent="302" name="course_id">
      <Comment>所属课程表</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="560" parent="302" name="course_ids">
      <Comment>所属课程</Comment>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="561" parent="302" name="level">
      <Comment>课程级别</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="562" parent="302" name="status">
      <Comment>状态:1=待开课,2=授课中,3=已完成,4=等位中,5=已取消</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="563" parent="302" name="create_time">
      <Comment>预约时间</Comment>
      <Position>11</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="564" parent="302" name="sign_in_time">
      <Comment>签到时间</Comment>
      <Position>12</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="565" parent="302" name="cancel_time">
      <Comment>取消时间</Comment>
      <Position>13</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="566" parent="302" name="appointment_success_time">
      <Comment>预约成功时间</Comment>
      <Position>14</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="567" parent="302" name="card_type">
      <Comment>使用的会员卡类型:1=次卡,2=时长卡</Comment>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="568" parent="302" name="card_id">
      <Comment>会员卡ID</Comment>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="569" parent="302" name="start_time">
      <Comment>上课开始时间</Comment>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="570" parent="302" name="end_time">
      <Comment>上课结束时间</Comment>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="571" parent="302" name="completion_time">
      <Comment>完成时间</Comment>
      <Position>19</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="572" parent="302" name="course_consumption_amount">
      <Comment>消课金额</Comment>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="573" parent="302" name="store_id">
      <Comment>门店</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="574" parent="302" name="remaining_before_deduction">
      <Comment>扣费前剩余权益</Comment>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="575" parent="302" name="this_deduction">
      <Comment>本次扣费权益</Comment>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="576" parent="302" name="remaining_after_deduction">
      <Comment>扣费后剩余权益</Comment>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="577" parent="302" name="contract_name">
      <Comment>合同名称</Comment>
      <Position>25</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="578" parent="302" name="deduct_type">
      <Comment>等位扣除状态:0=已扣除,1=未扣除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="579" parent="302" name="class_reminder">
      <Comment>上课提醒通知:0=未通知,1=已通知</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>27</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="580" parent="302" name="reservation_count">
      <Comment>当前预约人数</Comment>
      <Position>28</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="581" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="582" parent="302" name="uid">
      <ColNames>uid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="583" parent="302" name="course_id">
      <ColNames>course_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="584" parent="302" name="status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="585" parent="302" name="start_time">
      <ColNames>start_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="586" parent="302" name="end_time">
      <ColNames>end_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="587" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="588" parent="303" name="id">
      <AutoIncrement>3712</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="589" parent="303" name="pid">
      <Comment>上级ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="590" parent="303" name="name">
      <Comment>名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="591" parent="303" name="level">
      <Comment>等级</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="592" parent="303" name="code">
      <Comment>行政区划代码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="593" parent="303" name="zip">
      <Comment>邮编</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <index id="594" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="595" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="596" parent="304" name="id">
      <AutoIncrement>36834</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="597" parent="304" name="topic">
      <Comment>细目</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="598" parent="304" name="admin_id">
      <Comment>上传管理员ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="599" parent="304" name="user_id">
      <Comment>上传用户ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="600" parent="304" name="url">
      <Comment>物理路径</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="601" parent="304" name="width">
      <Comment>宽度</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="602" parent="304" name="height">
      <Comment>高度</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="603" parent="304" name="name">
      <Comment>原始名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="604" parent="304" name="size">
      <Comment>大小</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="605" parent="304" name="mimetype">
      <Comment>mime类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="606" parent="304" name="quote">
      <Comment>上传(引用)次数</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="607" parent="304" name="storage">
      <Comment>存储方式</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="608" parent="304" name="sha1">
      <Comment>sha1编码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="609" parent="304" name="create_time">
      <Comment>创建时间</Comment>
      <Position>14</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="610" parent="304" name="last_upload_time">
      <Comment>最后上传时间</Comment>
      <Position>15</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="611" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="612" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="613" parent="305" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="614" parent="305" name="cate_id">
      <Comment>所属分类</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="615" parent="305" name="keyword">
      <Comment>关键词</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="616" parent="305" name="reply_content">
      <Comment>回复内容</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="617" parent="305" name="create_time">
      <Comment>添加时间</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="618" parent="305" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="619" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="620" parent="306" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="621" parent="306" name="binding_id">
      <Comment>绑定id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="622" parent="306" name="appid">
      <Comment>APPid</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="623" parent="306" name="relation_id">
      <Comment>关联id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="624" parent="306" name="type">
      <Comment>类型0=客服转接辅助</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="625" parent="306" name="other">
      <Comment>其他数据为json</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(2048)|0s</StoredType>
    </column>
    <column id="626" parent="306" name="status">
      <Comment>数据状态 0：未执行，1：成功， 2：失败， 3:删除</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="627" parent="306" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="628" parent="306" name="add_time">
      <Comment>添加时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="629" parent="306" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="630" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="631" parent="307" name="key">
      <Comment>身份管理名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="632" parent="307" name="result">
      <Comment>缓存数据</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="633" parent="307" name="expire_time">
      <Comment>失效时间0=永久</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="634" parent="307" name="add_time">
      <Comment>缓存时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="635" parent="307" name="key">
      <ColNames>key</ColNames>
      <Type>btree</Type>
    </index>
    <column id="636" parent="308" name="key">
      <Comment>验证码Key</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="637" parent="308" name="code">
      <Comment>验证码(加密后)</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="638" parent="308" name="captcha">
      <Comment>验证码数据</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="639" parent="308" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="640" parent="308" name="expire_time">
      <Comment>过期时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="641" parent="308" name="PRIMARY">
      <ColNames>key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="642" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="643" parent="309" name="id">
      <AutoIncrement>27</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="644" parent="309" name="name">
      <Comment>活动卡名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="645" parent="309" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="646" parent="309" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="647" parent="309" name="update_time">
      <Comment>修改时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="648" parent="309" name="type">
      <Comment>状态:0=赠送卡,1=购买卡,2=私教卡</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <index id="649" parent="309" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="650" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="651" parent="310" name="id">
      <AutoIncrement>38282</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="652" parent="310" name="out_trade_no">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="653" parent="310" name="uid">
      <Comment>所属用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="654" parent="310" name="sale_id">
      <Comment>所属销售</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="655" parent="310" name="card_id">
      <Comment>所属会员卡</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="656" parent="310" name="store_id">
      <Comment>适用门店</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="657" parent="310" name="type">
      <Comment>会员卡类型:0=次卡,1=年卡,2=月卡,3=周卡,4=时长卡（天）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;, &apos;2&apos;, &apos;3&apos;, &apos;4&apos;)|0e</StoredType>
    </column>
    <column id="658" parent="310" name="number">
      <Comment>次数/时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="659" parent="310" name="surplus_frequency">
      <Comment>剩余次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="660" parent="310" name="price">
      <Comment>单卡金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="661" parent="310" name="total_amount">
      <Comment>总金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="662" parent="310" name="payment_price">
      <Comment>支付金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="663" parent="310" name="leave_frequency">
      <Comment>请假次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="664" parent="310" name="leave_duration">
      <Comment>单次请假时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="665" parent="310" name="contract_name">
      <Comment>合同名称</Comment>
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="666" parent="310" name="open_store_id">
      <Comment>办理门店</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="667" parent="310" name="three_parties_order_number">
      <Comment>第三方订单号</Comment>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="668" parent="310" name="payment_voucher">
      <Comment>支付凭证</Comment>
      <Position>18</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="669" parent="310" name="notes">
      <Comment>备注</Comment>
      <Position>19</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="670" parent="310" name="gift_days">
      <Comment>赠送天数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="671" parent="310" name="gift_frequency">
      <Comment>赠送次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="672" parent="310" name="create_time">
      <Comment>创建时间</Comment>
      <Position>22</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="673" parent="310" name="payment_time">
      <Comment>支付时间</Comment>
      <Position>23</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="674" parent="310" name="activation_time">
      <Comment>激活时间</Comment>
      <Position>24</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="675" parent="310" name="end_time">
      <Comment>会员卡到期时间</Comment>
      <Position>25</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="676" parent="310" name="status">
      <Comment>状态:0=待激活,1=使用中,2=请假中,3=已耗尽,4=已过期,5=已转让,6=已退款,7=已转卡</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="677" parent="310" name="payment_status">
      <Comment>支付状态:0=待支付,1=已支付</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>27</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="678" parent="310" name="activation_method">
      <Comment>激活方式:1=立即开卡,2=手动开卡</Comment>
      <DefaultExpression>2</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>28</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="679" parent="310" name="payment_method">
      <Comment>支付方式1=微信,2=支付宝,3=现金,4=转账,5=刷卡,6=抖音,7=美团/大众,8=兑换</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>29</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="680" parent="310" name="general_type">
      <Comment>是否全店通用:1=是,0=否</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>30</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="681" parent="310" name="card_transfer_time">
      <Comment>转卡时间</Comment>
      <Position>31</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="682" parent="310" name="card_front_id">
      <Comment>来源于转卡/转让ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>32</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="683" parent="310" name="transfer_time">
      <Comment>转让时间</Comment>
      <Position>33</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="684" parent="310" name="duration">
      <Comment>次卡时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>34</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="685" parent="310" name="refund_price">
      <Comment>退款金额</Comment>
      <Position>35</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="686" parent="310" name="refund_time">
      <Comment>退款时间</Comment>
      <Position>36</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="687" parent="310" name="member_card_number">
      <Comment>会员卡号</Comment>
      <Position>37</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="688" parent="310" name="sign_contract_type">
      <Comment>是否签合同:0=未签,1=已签</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>38</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="689" parent="310" name="delete_time">
      <Comment>删除时间</Comment>
      <Position>39</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="690" parent="310" name="smoke_frequency">
      <Comment>赠送抽奖次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>40</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="691" parent="310" name="activity_id">
      <Comment>会员卡活动</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>41</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="692" parent="310" name="default">
      <Comment>默认</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>42</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="693" parent="310" name="contract">
      <Comment>会员卡合同</Comment>
      <Position>43</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="694" parent="310" name="contract_time">
      <Comment>签署时间</Comment>
      <Position>44</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="695" parent="310" name="contract_image">
      <Comment>会员卡合同内容</Comment>
      <Position>45</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="696" parent="310" name="order_status">
      <DefaultExpression>0</DefaultExpression>
      <Position>46</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="697" parent="310" name="transfer_price">
      <Comment>转卡剩余业绩</Comment>
      <Position>47</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="698" parent="310" name="transfer_type">
      <Comment>会员卡金额处理</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>48</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="699" parent="310" name="card_type">
      <Comment>会员卡类别:0=普通卡,1=私教卡</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>49</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="700" parent="310" name="renewal_frequency">
      <Comment>续费次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>50</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="701" parent="310" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="702" parent="310" name="uid">
      <ColNames>uid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="703" parent="310" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="704" parent="310" name="type">
      <ColNames>type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="705" parent="310" name="status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="706" parent="310" name="payment_status">
      <ColNames>payment_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="707" parent="310" name="default">
      <ColNames>default</ColNames>
      <Type>btree</Type>
    </index>
    <index id="708" parent="310" name="card_type">
      <ColNames>card_type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="709" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="710" parent="311" name="id">
      <AutoIncrement>34019</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="711" parent="311" name="out_trade_no">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="712" parent="311" name="uid">
      <Comment>所属用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="713" parent="311" name="sale_id">
      <Comment>所属销售</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="714" parent="311" name="card_id">
      <Comment>所属会员卡</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="715" parent="311" name="store_id">
      <Comment>适用门店</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="716" parent="311" name="type">
      <Comment>会员卡类型:0=次卡,1=年卡,2=月卡,3=周卡,4=时长卡（天）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;, &apos;2&apos;, &apos;3&apos;, &apos;4&apos;)|0e</StoredType>
    </column>
    <column id="717" parent="311" name="number">
      <Comment>次数/时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="718" parent="311" name="surplus_frequency">
      <Comment>剩余次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="719" parent="311" name="price">
      <Comment>单卡金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="720" parent="311" name="total_amount">
      <Comment>总金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="721" parent="311" name="payment_price">
      <Comment>支付金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="722" parent="311" name="leave_frequency">
      <Comment>请假次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="723" parent="311" name="leave_duration">
      <Comment>单次请假时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="724" parent="311" name="contract_name">
      <Comment>合同名称</Comment>
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="725" parent="311" name="open_store_id">
      <Comment>办理门店</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="726" parent="311" name="three_parties_order_number">
      <Comment>第三方订单号</Comment>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="727" parent="311" name="payment_voucher">
      <Comment>支付凭证</Comment>
      <Position>18</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="728" parent="311" name="notes">
      <Comment>备注</Comment>
      <Position>19</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="729" parent="311" name="gift_days">
      <Comment>赠送天数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="730" parent="311" name="gift_frequency">
      <Comment>赠送次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="731" parent="311" name="create_time">
      <Comment>创建时间</Comment>
      <Position>22</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="732" parent="311" name="payment_time">
      <Comment>支付时间</Comment>
      <Position>23</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="733" parent="311" name="activation_time">
      <Comment>激活时间</Comment>
      <Position>24</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="734" parent="311" name="end_time">
      <Comment>会员卡到期时间</Comment>
      <Position>25</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="735" parent="311" name="status">
      <Comment>状态:0=待激活,1=使用中,2=请假中,3=已耗尽,4=已过期,5=已转让,6=已退款,7=已转卡</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="736" parent="311" name="payment_status">
      <Comment>支付状态:0=待支付,1=已支付</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>27</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="737" parent="311" name="activation_method">
      <Comment>激活方式:1=立即开卡,2=手动开卡</Comment>
      <DefaultExpression>2</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>28</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="738" parent="311" name="payment_method">
      <Comment>支付方式1=微信,2=支付宝,3=现金,4=转账,5=刷卡,6=抖音,7=美团/大众,8=兑换</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>29</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="739" parent="311" name="general_type">
      <Comment>是否全店通用:1=是,0=否</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>30</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="740" parent="311" name="card_transfer_time">
      <Comment>转卡时间</Comment>
      <Position>31</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="741" parent="311" name="card_front_id">
      <Comment>来源于转卡/转让ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>32</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="742" parent="311" name="transfer_time">
      <Comment>转让时间</Comment>
      <Position>33</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="743" parent="311" name="duration">
      <Comment>次卡时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>34</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="744" parent="311" name="refund_price">
      <Comment>退款金额</Comment>
      <Position>35</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="745" parent="311" name="refund_time">
      <Comment>退款时间</Comment>
      <Position>36</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="746" parent="311" name="member_card_number">
      <Comment>会员卡号</Comment>
      <Position>37</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="747" parent="311" name="sign_contract_type">
      <Comment>是否签合同:0=未签,1=已签</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>38</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="748" parent="311" name="delete_time">
      <Comment>删除时间</Comment>
      <Position>39</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="749" parent="311" name="smoke_frequency">
      <Comment>赠送抽奖次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>40</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="750" parent="311" name="activity_id">
      <Comment>会员卡活动</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>41</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="751" parent="311" name="default">
      <Comment>默认</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>42</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="752" parent="311" name="contract">
      <Comment>会员卡合同</Comment>
      <Position>43</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="753" parent="311" name="contract_time">
      <Comment>签署时间</Comment>
      <Position>44</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="754" parent="311" name="contract_image">
      <Comment>会员卡合同内容</Comment>
      <Position>45</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="755" parent="311" name="order_status">
      <DefaultExpression>0</DefaultExpression>
      <Position>46</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="756" parent="311" name="transfer_price">
      <Comment>转卡剩余业绩</Comment>
      <Position>47</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <index id="757" parent="311" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="758" parent="311" name="uid">
      <ColNames>uid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="759" parent="311" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="760" parent="311" name="type">
      <ColNames>type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="761" parent="311" name="status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="762" parent="311" name="payment_status">
      <ColNames>payment_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="763" parent="311" name="default">
      <ColNames>default</ColNames>
      <Type>btree</Type>
    </index>
    <key id="764" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="765" parent="312" name="id">
      <AutoIncrement>33730</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="766" parent="312" name="out_trade_no">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="767" parent="312" name="uid">
      <Comment>所属用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="768" parent="312" name="sale_id">
      <Comment>所属销售</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="769" parent="312" name="card_id">
      <Comment>所属会员卡</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="770" parent="312" name="store_id">
      <Comment>适用门店</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="771" parent="312" name="type">
      <Comment>会员卡类型:0=次卡,1=年卡,2=月卡,3=周卡,4=时长卡（天）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;, &apos;2&apos;, &apos;3&apos;, &apos;4&apos;)|0e</StoredType>
    </column>
    <column id="772" parent="312" name="number">
      <Comment>次数/时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="773" parent="312" name="surplus_frequency">
      <Comment>剩余次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="774" parent="312" name="price">
      <Comment>单卡金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="775" parent="312" name="total_amount">
      <Comment>总金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="776" parent="312" name="payment_price">
      <Comment>支付金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="777" parent="312" name="leave_frequency">
      <Comment>请假次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="778" parent="312" name="leave_duration">
      <Comment>单次请假时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="779" parent="312" name="contract_name">
      <Comment>合同名称</Comment>
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="780" parent="312" name="open_store_id">
      <Comment>办理门店</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="781" parent="312" name="three_parties_order_number">
      <Comment>第三方订单号</Comment>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="782" parent="312" name="payment_voucher">
      <Comment>支付凭证</Comment>
      <Position>18</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="783" parent="312" name="notes">
      <Comment>备注</Comment>
      <Position>19</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="784" parent="312" name="gift_days">
      <Comment>赠送天数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="785" parent="312" name="gift_frequency">
      <Comment>赠送次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="786" parent="312" name="create_time">
      <Comment>创建时间</Comment>
      <Position>22</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="787" parent="312" name="payment_time">
      <Comment>支付时间</Comment>
      <Position>23</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="788" parent="312" name="activation_time">
      <Comment>激活时间</Comment>
      <Position>24</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="789" parent="312" name="end_time">
      <Comment>会员卡到期时间</Comment>
      <Position>25</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="790" parent="312" name="status">
      <Comment>状态:0=待激活,1=使用中,2=请假中,3=已耗尽,4=已过期,5=已转让,6=已退款,7=已转卡</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="791" parent="312" name="payment_status">
      <Comment>支付状态:0=待支付,1=已支付</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>27</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="792" parent="312" name="activation_method">
      <Comment>激活方式:1=立即开卡,2=手动开卡</Comment>
      <DefaultExpression>2</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>28</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="793" parent="312" name="payment_method">
      <Comment>支付方式1=微信,2=支付宝,3=现金,4=转账,5=刷卡,6=抖音,7=美团/大众,8=兑换</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>29</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="794" parent="312" name="general_type">
      <Comment>是否全店通用:1=是,0=否</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>30</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="795" parent="312" name="card_transfer_time">
      <Comment>转卡时间</Comment>
      <Position>31</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="796" parent="312" name="card_front_id">
      <Comment>来源于转卡/转让ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>32</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="797" parent="312" name="transfer_time">
      <Comment>转让时间</Comment>
      <Position>33</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="798" parent="312" name="duration">
      <Comment>次卡时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>34</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="799" parent="312" name="refund_price">
      <Comment>退款金额</Comment>
      <Position>35</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="800" parent="312" name="refund_time">
      <Comment>退款时间</Comment>
      <Position>36</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="801" parent="312" name="member_card_number">
      <Comment>会员卡号</Comment>
      <Position>37</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="802" parent="312" name="sign_contract_type">
      <Comment>是否签合同:0=未签,1=已签</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>38</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="803" parent="312" name="delete_time">
      <Comment>删除时间</Comment>
      <Position>39</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="804" parent="312" name="smoke_frequency">
      <Comment>赠送抽奖次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>40</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="805" parent="312" name="activity_id">
      <Comment>会员卡活动</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>41</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="806" parent="312" name="default">
      <Comment>默认</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>42</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="807" parent="312" name="contract">
      <Comment>会员卡合同</Comment>
      <Position>43</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="808" parent="312" name="contract_time">
      <Comment>签署时间</Comment>
      <Position>44</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="809" parent="312" name="contract_image">
      <Comment>会员卡合同内容</Comment>
      <Position>45</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="810" parent="312" name="order_status">
      <DefaultExpression>0</DefaultExpression>
      <Position>46</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="811" parent="312" name="transfer_price">
      <Comment>转卡剩余业绩</Comment>
      <Position>47</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="812" parent="312" name="transfer_type">
      <Comment>会员卡金额处理</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>48</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="813" parent="312" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="814" parent="312" name="uid">
      <ColNames>uid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="815" parent="312" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="816" parent="312" name="type">
      <ColNames>type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="817" parent="312" name="status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="818" parent="312" name="payment_status">
      <ColNames>payment_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="819" parent="312" name="default">
      <ColNames>default</ColNames>
      <Type>btree</Type>
    </index>
    <key id="820" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="821" parent="313" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="822" parent="313" name="card_id">
      <Comment>所属会员卡</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="823" parent="313" name="type">
      <Comment>新卡类型:0=次卡,1=年卡,2=月卡,3=周卡</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="824" parent="313" name="card_name">
      <Comment>新卡名称</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="825" parent="313" name="price">
      <Comment>购卡金额</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="826" parent="313" name="price_difference">
      <Comment>差价</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="827" parent="313" name="operator_id">
      <Comment>操作人</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="828" parent="313" name="transactors_id">
      <Comment>服务办理人</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="829" parent="313" name="notes">
      <Comment>备注</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="830" parent="313" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="831" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="832" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="833" parent="314" name="id">
      <AutoIncrement>123</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="834" parent="314" name="store_id">
      <Comment>所属门店</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="835" parent="314" name="type">
      <Comment>类型:1=轮播图,2=课程轮播图</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>enum(&apos;1&apos;, &apos;2&apos;)|0e</StoredType>
    </column>
    <column id="836" parent="314" name="carousel">
      <Comment>轮播图</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="837" parent="314" name="carousel_background">
      <Comment>轮播背景图</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="838" parent="314" name="jump_address">
      <Comment>跳转地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="839" parent="314" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="840" parent="314" name="update_time">
      <Comment>修改时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="841" parent="314" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="842" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="843" parent="315" name="id">
      <AutoIncrement>46</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="844" parent="315" name="type">
      <Comment>类型:1=舞种,2=课程级别,3=积分商城,4=客服话术</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>enum(&apos;1&apos;, &apos;2&apos;, &apos;3&apos;, &apos;4&apos;)|0e</StoredType>
    </column>
    <column id="845" parent="315" name="name">
      <Comment>名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="846" parent="315" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="847" parent="315" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="848" parent="315" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="849" parent="315" name="update_time">
      <Comment>修改时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="850" parent="315" name="owner_id">
      <Comment>所属客服</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="851" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="852" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="853" parent="316" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="854" parent="316" name="keyword">
      <Comment>关键词</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="855" parent="316" name="content">
      <Comment>内容</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="856" parent="316" name="user_id">
      <Comment>所属用户</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="857" parent="316" name="appid">
      <Comment>所属appid</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="858" parent="316" name="sort">
      <Comment>排序,越靠前,越是能被自会回复到</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="859" parent="316" name="add_time">
      <Comment>添加时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="860" parent="316" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="861" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="862" parent="317" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="863" parent="317" name="store_id">
      <Comment>远程下拉</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="864" parent="317" name="appid">
      <Comment>APPID</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="865" parent="317" name="account">
      <Comment>登录账号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="866" parent="317" name="password">
      <Comment>密码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="867" parent="317" name="avatar">
      <Comment>客服头像</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="868" parent="317" name="wechat_or_code">
      <Comment>微信二维码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="869" parent="317" name="wechat_number">
      <Comment>微信号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="870" parent="317" name="nickname">
      <Comment>客服名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="871" parent="317" name="status">
      <Comment>客服状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="872" parent="317" name="online">
      <Comment>是否在线</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="873" parent="317" name="auto_reply">
      <Comment>自动回复</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="874" parent="317" name="welcome_words">
      <Comment>欢迎语</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="875" parent="317" name="ip">
      <Comment>访问IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="876" parent="317" name="client_id">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="877" parent="317" name="reception">
      <Comment>接待人数</Comment>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="878" parent="317" name="conclusion">
      <Comment>结束语</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="879" parent="317" name="create_time">
      <Comment>添加时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="880" parent="317" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="881" parent="317" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="882" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="883" parent="318" name="id">
      <AutoIncrement>26</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="884" parent="318" name="appid">
      <Comment>APPID</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="885" parent="318" name="store_id">
      <Comment>所属门店</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="886" parent="318" name="msn">
      <Comment>消息内容</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="887" parent="318" name="user_id">
      <Comment>发送人id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="888" parent="318" name="to_user_id">
      <Comment>接收人id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="889" parent="318" name="is_tourist">
      <Comment>用户类型:1=用户,2=客服</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="890" parent="318" name="add_time">
      <Comment>发送时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="891" parent="318" name="type">
      <Comment>是否已读（0：否；1：是；）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="892" parent="318" name="remind">
      <Comment>是否提醒过</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="893" parent="318" name="msn_type">
      <Comment>消息类型 1=文字 2=表情 3=图片 4=语音</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="894" parent="318" name="is_send">
      <Comment>是否发送</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="895" parent="318" name="other">
      <Comment>其他参数</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="896" parent="318" name="guid">
      <Comment>guid相当于唯一值</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>14</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="897" parent="318" name="path">
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="898" parent="318" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="899" parent="318" name="to_uid">
      <ColNames>to_user_id
user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="900" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="901" parent="319" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="902" parent="319" name="name">
      <Comment>组名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="903" parent="319" name="sort">
      <Comment>排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="904" parent="319" name="create_time">
      <Comment>添加时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="905" parent="319" name="update_time">
      <Comment>更新时间</Comment>
      <Position>5</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="906" parent="319" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="907" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="908" parent="320" name="id">
      <AutoIncrement>3</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="909" parent="320" name="appid">
      <Comment>APPID</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="910" parent="320" name="store_id">
      <Comment>所属门店</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="911" parent="320" name="user_id">
      <Comment>接收人id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="912" parent="320" name="to_user_id">
      <Comment>发送人id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="913" parent="320" name="nickname">
      <Comment>用户昵称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="914" parent="320" name="avatar">
      <Comment>用户头像</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="915" parent="320" name="is_tourist">
      <Comment>1：用户  2：客服</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="916" parent="320" name="online">
      <Comment>是否在线</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="917" parent="320" name="type">
      <Comment>0 = pc,1=微信，2=小程序，3=H5</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="918" parent="320" name="add_time">
      <Comment>添加时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="919" parent="320" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="920" parent="320" name="delete_time">
      <Comment>删除字段</Comment>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="921" parent="320" name="mssage_num">
      <Comment>消息条数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="922" parent="320" name="message">
      <Comment>消息内容</Comment>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="923" parent="320" name="message_type">
      <Comment>消息类型</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="924" parent="320" name="status">
      <Comment>状态:1=咨询中,0=已关闭</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="925" parent="320" name="end_time">
      <Comment>咨询截止时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="926" parent="320" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="927" parent="320" name="to_uid">
      <ColNames>to_user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="928" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="929" parent="321" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="930" parent="321" name="kefu_id">
      <Comment>0为全局话术</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="931" parent="321" name="cate_id">
      <Comment>0为不分类全局话术</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="932" parent="321" name="title">
      <Comment>话术标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="933" parent="321" name="message">
      <Comment>话术内容</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="934" parent="321" name="sort">
      <Comment>排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="935" parent="321" name="add_time">
      <Comment>添加时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="936" parent="321" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="937" parent="321" name="kefu_id">
      <ColNames>kefu_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="938" parent="321" name="cate_id">
      <ColNames>cate_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="939" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="940" parent="322" name="id">
      <AutoIncrement>9</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="941" parent="322" name="store_id">
      <Comment>所属门店</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="942" parent="322" name="uid">
      <Comment>所属用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="943" parent="322" name="go_to_work_time">
      <Comment>上班时间</Comment>
      <Position>4</Position>
      <StoredType>time|0s</StoredType>
    </column>
    <column id="944" parent="322" name="go_off_work_time">
      <Comment>下班时间</Comment>
      <Position>5</Position>
      <StoredType>time|0s</StoredType>
    </column>
    <column id="945" parent="322" name="clock_in_time">
      <Comment>上班打卡时间</Comment>
      <Position>6</Position>
      <StoredType>time|0s</StoredType>
    </column>
    <column id="946" parent="322" name="after_work_time">
      <Comment>下班打卡时间</Comment>
      <Position>7</Position>
      <StoredType>time|0s</StoredType>
    </column>
    <column id="947" parent="322" name="go_to_work_status">
      <Comment>上班打卡状态:0=未打卡,1=已打卡,2=迟到打卡</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="948" parent="322" name="go_off_work_status">
      <Comment>下班打卡状态:0=未打卡,1=已打卡,2=早退打卡</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="949" parent="322" name="go_to_work_address">
      <Comment>上班打卡位置</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="950" parent="322" name="go_off_work_address">
      <Comment>下班打卡位置</Comment>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="951" parent="322" name="create_time">
      <Comment>创建时间</Comment>
      <Position>12</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="952" parent="322" name="working_status">
      <Comment>上班状态:0=不上班,1=上班</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="953" parent="322" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="954" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="955" parent="323" name="id">
      <AutoIncrement>302</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="956" parent="323" name="name">
      <Comment>变量名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="957" parent="323" name="group">
      <Comment>分组</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="958" parent="323" name="title">
      <Comment>变量标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="959" parent="323" name="tip">
      <Comment>变量描述</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="960" parent="323" name="type">
      <Comment>变量输入组件类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="961" parent="323" name="value">
      <Comment>变量值</Comment>
      <Position>7</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="962" parent="323" name="content">
      <Comment>字典数据</Comment>
      <Position>8</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="963" parent="323" name="rule">
      <Comment>验证规则</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="964" parent="323" name="extend">
      <Comment>扩展属性</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="965" parent="323" name="allow_del">
      <Comment>允许删除:0=否,1=是</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="966" parent="323" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="967" parent="323" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="968" parent="323" name="name">
      <ColNames>name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="969" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="970" parent="323" name="name">
      <UnderlyingIndexName>name</UnderlyingIndexName>
    </key>
    <column id="971" parent="324" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="972" parent="324" name="name">
      <Comment>名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="973" parent="324" name="type">
      <Comment>类型:0=无门槛,1=满减券</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="974" parent="324" name="day">
      <Comment>有效时间（）天</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="975" parent="324" name="discount_price">
      <Comment>优惠金额</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="976" parent="324" name="full_price">
      <Comment>满减金额</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="977" parent="324" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="978" parent="324" name="update_time">
      <Comment>修改时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="979" parent="324" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="980" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="981" parent="325" name="id">
      <AutoIncrement>8</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="982" parent="325" name="level">
      <Comment>课程级别</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="983" parent="325" name="dance">
      <Comment>舞种</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="984" parent="325" name="name">
      <Comment>课程名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="985" parent="325" name="teacher_id">
      <Comment>默认老师</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="986" parent="325" name="video">
      <Comment>课程视频</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="987" parent="325" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="988" parent="325" name="template_color">
      <Comment>模板色</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="989" parent="325" name="start_reservation_hour">
      <Comment>开始预约的时间：课前（）小时</Comment>
      <Position>9</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="990" parent="325" name="start_reservation_minute">
      <Comment>开始预约的时间：课前（）分钟</Comment>
      <Position>10</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="991" parent="325" name="end_reservation_hour">
      <Comment>截止预约的时间：课前（）小时</Comment>
      <Position>11</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="992" parent="325" name="end_reservation_minute">
      <Comment>截止预约的时间：课前（）分钟</Comment>
      <Position>12</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="993" parent="325" name="minimum_reservation">
      <Comment>开课的最小预约人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="994" parent="325" name="maximum_reservation">
      <Comment>开课的最大预约人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="995" parent="325" name="cancel_hour">
      <Comment>自动取消时间：课前（）小时</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>15</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="996" parent="325" name="cancel_minute">
      <Comment>自动取消时间：课前（）分钟</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>16</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="997" parent="325" name="autonomy_cancel_hour">
      <Comment>会员自主取消预约时间：课前（）小时</Comment>
      <Position>17</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="998" parent="325" name="autonomy_cancel_minute">
      <Comment>会员自主取消预约时间：课前（）分钟</Comment>
      <Position>18</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="999" parent="325" name="equivalent_time_hour">
      <Comment>等位时的最迟截止时间：课前（）小时</Comment>
      <Position>19</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1000" parent="325" name="equivalent_time_minute">
      <Comment>等位时的最迟截止时间：课前（）分钟</Comment>
      <Position>20</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1001" parent="325" name="create_time">
      <Comment>创建时间</Comment>
      <Position>21</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1002" parent="325" name="update_time">
      <Comment>修改时间</Comment>
      <Position>22</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1003" parent="325" name="view_type">
      <Comment>非会员查看详情:1=可查看,0=不可查看</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>23</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1004" parent="325" name="frequency">
      <Comment>扣卡次数</Comment>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="1005" parent="325" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1006" parent="325" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1007" parent="326" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1008" parent="326" name="course_package_id">
      <Comment>所属课包</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1009" parent="326" name="contents_id">
      <Comment>所属目录</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1010" parent="326" name="name">
      <Comment>名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1011" parent="326" name="video">
      <Comment>视频</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1012" parent="326" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1013" parent="326" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1014" parent="326" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1015" parent="326" name="update_time">
      <Comment>修改时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1016" parent="326" name="user">
      <Comment>观看用户</Comment>
      <Position>10</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1017" parent="326" name="fileList">
      <Comment>视频信息</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="1018" parent="326" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1019" parent="326" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1020" parent="327" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1021" parent="327" name="image">
      <Comment>封面图</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1022" parent="327" name="images">
      <Comment>轮播图</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(1500)|0s</StoredType>
    </column>
    <column id="1023" parent="327" name="level">
      <Comment>级别</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1024" parent="327" name="dance">
      <Comment>舞种</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1025" parent="327" name="teacher_id">
      <Comment>讲师</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1026" parent="327" name="name">
      <Comment>名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1027" parent="327" name="price">
      <Comment>价格</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1028" parent="327" name="introduce">
      <Comment>课程介绍内容</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1029" parent="327" name="introduce_video">
      <Comment>课程介绍视频</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1030" parent="327" name="apply_crowd">
      <Comment>适用人群</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1031" parent="327" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1032" parent="327" name="sales_volume">
      <Comment>销量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1033" parent="327" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1034" parent="327" name="create_time">
      <Comment>创建时间</Comment>
      <Position>15</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1035" parent="327" name="update_time">
      <Comment>修改时间</Comment>
      <Position>16</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1036" parent="327" name="fileList">
      <Comment>视频信息</Comment>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="1037" parent="327" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1038" parent="327" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1039" parent="328" name="id">
      <AutoIncrement>34</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1040" parent="328" name="order_no">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="1041" parent="328" name="uid">
      <Comment>所属用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1042" parent="328" name="course_package_id">
      <Comment>所属课包</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1043" parent="328" name="price">
      <Comment>订单金额</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1044" parent="328" name="coupon_id">
      <Comment>所属优惠券</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1045" parent="328" name="discount_price">
      <Comment>优惠金额</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1046" parent="328" name="payment_price">
      <Comment>支付金额</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1047" parent="328" name="status">
      <Comment>状态:1=待支付,2=已支付</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1048" parent="328" name="create_time">
      <Comment>创建时间</Comment>
      <Position>10</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1049" parent="328" name="payment_time">
      <Comment>支付时间</Comment>
      <Position>11</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="1050" parent="328" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1051" parent="328" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1052" parent="329" name="id">
      <AutoIncrement>47</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1053" parent="329" name="uid">
      <Comment>所属用户</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1054" parent="329" name="course_package_id">
      <Comment>所属课包</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1055" parent="329" name="contents_id">
      <Comment>所属目录</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1056" parent="329" name="duration">
      <Comment>观看时长</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>float|0s</StoredType>
    </column>
    <column id="1057" parent="329" name="create_time">
      <Comment>添加时间</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="1058" parent="329" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1059" parent="329" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1060" parent="330" name="id">
      <AutoIncrement>912363</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1061" parent="330" name="uid">
      <Comment>所属用户</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1062" parent="330" name="operator_id">
      <Comment>操作人员</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1063" parent="330" name="school_timetable_id">
      <Comment>所属课程</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1064" parent="330" name="content">
      <Comment>操作内容</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1065" parent="330" name="create_time">
      <Comment>操作时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1066" parent="330" name="reservation_count">
      <Comment>当前预约人数</Comment>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="1067" parent="330" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1068" parent="330" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1069" parent="331" name="id">
      <AutoIncrement>50</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1070" parent="331" name="table_name">
      <Comment>数据表名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1071" parent="331" name="table">
      <Comment>数据表数据</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1072" parent="331" name="fields">
      <Comment>字段数据</Comment>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1073" parent="331" name="status">
      <Comment>状态:delete=已删除,success=成功,error=失败,start=生成中</Comment>
      <DefaultExpression>&apos;start&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>enum(&apos;delete&apos;, &apos;success&apos;, &apos;error&apos;, &apos;start&apos;)|0e</StoredType>
    </column>
    <column id="1074" parent="331" name="connection">
      <Comment>数据库连接配置标识</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1075" parent="331" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1076" parent="331" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1077" parent="331" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1078" parent="332" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1079" parent="332" name="admin_id">
      <Comment>导出人</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1080" parent="332" name="name">
      <Comment>任务名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1081" parent="332" name="main_table">
      <Comment>数据源表</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1082" parent="332" name="field_config">
      <Comment>字段配置</Comment>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1083" parent="332" name="join_table">
      <Comment>关联表配置</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1084" parent="332" name="where_field">
      <Comment>筛选规则</Comment>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1085" parent="332" name="order_field">
      <Comment>排序规则</Comment>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1086" parent="332" name="xls_max_number">
      <Comment>单个xls最大记录数</Comment>
      <DefaultExpression>&apos;10000&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1087" parent="332" name="concurrent_create_xls">
      <Comment>并发创建xls</Comment>
      <DefaultExpression>&apos;3&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1088" parent="332" name="memory_limit">
      <Comment>脚本内存限制(MB)</Comment>
      <DefaultExpression>&apos;128&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>smallint unsigned|0s</StoredType>
    </column>
    <column id="1089" parent="332" name="export_number">
      <Comment>导出记录数</Comment>
      <Position>12</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1090" parent="332" name="subtask">
      <Comment>子任务资料</Comment>
      <Position>13</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1091" parent="332" name="lastprogress">
      <Comment>上次导出进度</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1092" parent="332" name="lastfile">
      <Comment>上次导出文件</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1093" parent="332" name="lastexporttime">
      <Comment>上次导出时间</Comment>
      <Position>16</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1094" parent="332" name="createtime">
      <Comment>创建时间</Comment>
      <Position>17</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <index id="1095" parent="332" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1096" parent="332" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1097" parent="333" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1098" parent="333" name="staff_id">
      <Comment>请假员工</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1099" parent="333" name="store_id">
      <Comment>所属门店</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1100" parent="333" name="start_datetime">
      <Comment>开始时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1101" parent="333" name="end_datetime">
      <Comment>结束时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1102" parent="333" name="duration">
      <Comment>请假时长</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(5,2 digit)|0s</StoredType>
    </column>
    <column id="1103" parent="333" name="day">
      <Comment>请假天数</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>decimal(5,2 digit)|0s</StoredType>
    </column>
    <column id="1104" parent="333" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1105" parent="333" name="update_time">
      <Comment>修改时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1106" parent="333" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1107" parent="333" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1108" parent="334" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1109" parent="334" name="poiname">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1110" parent="334" name="poiaddress">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1111" parent="334" name="latitude">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1112" parent="334" name="longitude">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1113" parent="334" name="create_time">
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1114" parent="334" name="update_time">
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="1115" parent="334" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1116" parent="334" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1117" parent="335" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1118" parent="335" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1119" parent="335" name="url">
      <Comment>链接</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1120" parent="335" name="image">
      <Comment>图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1121" parent="335" name="icon">
      <Comment>图标选择</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1122" parent="335" name="color">
      <Comment>颜色选择器</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1123" parent="335" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1124" parent="335" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1125" parent="335" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1126" parent="335" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1127" parent="335" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1128" parent="336" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1129" parent="336" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1130" parent="336" name="date">
      <Comment>日期</Comment>
      <Position>3</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1131" parent="336" name="address">
      <Comment>详细地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1132" parent="336" name="code">
      <Comment>邮编</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1133" parent="336" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1134" parent="336" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1135" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1136" parent="337" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1137" parent="337" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1138" parent="337" name="switch">
      <Comment>开关:0=关,1=开</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1139" parent="337" name="datetime">
      <Comment>时间日期</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1140" parent="337" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1141" parent="337" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1142" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1143" parent="338" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1144" parent="338" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1145" parent="338" name="switch">
      <Comment>开关:0=关,1=开</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1146" parent="338" name="datetime">
      <Comment>时间日期</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1147" parent="338" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1148" parent="338" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1149" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1150" parent="339" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1151" parent="339" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1152" parent="339" name="switch">
      <Comment>开关:0=关,1=开</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1153" parent="339" name="datetime">
      <Comment>时间日期</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1154" parent="339" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1155" parent="339" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1156" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1157" parent="340" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1158" parent="340" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1159" parent="340" name="switch">
      <Comment>开关:0=关,1=开</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1160" parent="340" name="datetime">
      <Comment>时间日期</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1161" parent="340" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1162" parent="340" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1163" parent="340" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1164" parent="341" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1165" parent="341" name="user_id">
      <Comment>会员ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1166" parent="341" name="date">
      <Comment>日期</Comment>
      <Position>3</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1167" parent="341" name="address">
      <Comment>详细地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1168" parent="341" name="code">
      <Comment>邮编</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1169" parent="341" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1170" parent="341" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1171" parent="341" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1172" parent="342" name="id">
      <AutoIncrement>27</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1173" parent="342" name="string1">
      <Comment>字符串1</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1174" parent="342" name="string2">
      <Comment>字符串2</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1175" parent="342" name="string3">
      <Comment>字符串3</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1176" parent="342" name="string4">
      <Comment>字符串4</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1177" parent="342" name="string5">
      <Comment>字符串5</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1178" parent="342" name="update_time">
      <Comment>修改时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1179" parent="342" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1180" parent="342" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1181" parent="342" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1182" parent="343" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1183" parent="343" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <index id="1184" parent="343" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1185" parent="343" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1186" parent="344" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1187" parent="344" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1188" parent="344" name="number">
      <Comment>数字</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1189" parent="344" name="datetime">
      <Comment>时间日期</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1190" parent="344" name="image">
      <Comment>图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1191" parent="344" name="user_id">
      <Comment>会员ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1192" parent="344" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1193" parent="344" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1194" parent="344" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1195" parent="345" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1196" parent="345" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <index id="1197" parent="345" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1198" parent="345" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1199" parent="346" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1200" parent="346" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1201" parent="346" name="number">
      <Comment>数字</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1202" parent="346" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1203" parent="346" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1204" parent="346" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1205" parent="347" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1206" parent="347" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1207" parent="347" name="switch">
      <Comment>开关:0=关,1=开</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1208" parent="347" name="datetime">
      <Comment>时间日期</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1209" parent="347" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1210" parent="347" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1211" parent="347" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1212" parent="348" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1213" parent="348" name="date">
      <Comment>日期</Comment>
      <Position>2</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1214" parent="348" name="user_id">
      <Comment>远程下拉</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1215" parent="348" name="city">
      <Comment>城市</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1216" parent="348" name="address">
      <Comment>详细地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1217" parent="348" name="code">
      <Comment>邮编</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1218" parent="348" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1219" parent="348" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1220" parent="348" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1221" parent="349" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1222" parent="349" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <index id="1223" parent="349" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1224" parent="349" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1225" parent="350" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1226" parent="350" name="date">
      <Comment>日期</Comment>
      <Position>2</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1227" parent="350" name="user_id">
      <Comment>远程下拉</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1228" parent="350" name="city">
      <Comment>城市</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1229" parent="350" name="address">
      <Comment>详细地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1230" parent="350" name="code">
      <Comment>邮编</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1231" parent="350" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1232" parent="350" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1233" parent="350" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1234" parent="351" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1235" parent="351" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1236" parent="351" name="number">
      <Comment>数字</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1237" parent="351" name="float">
      <Comment>浮点数</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>decimal(5,2 digit)|0s</StoredType>
    </column>
    <column id="1238" parent="351" name="date">
      <Comment>日期</Comment>
      <Position>5</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1239" parent="351" name="update_time">
      <Comment>修改时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1240" parent="351" name="datetime">
      <Comment>时间日期</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1241" parent="351" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1242" parent="351" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1243" parent="351" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1244" parent="352" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1245" parent="352" name="number1">
      <Comment>数字1</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1246" parent="352" name="number2">
      <Comment>数字2</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1247" parent="352" name="float1">
      <Comment>浮点数1</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>decimal(5,2 digit)|0s</StoredType>
    </column>
    <column id="1248" parent="352" name="float2">
      <Comment>浮点数2</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(5,2 digit)|0s</StoredType>
    </column>
    <column id="1249" parent="352" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1250" parent="352" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1251" parent="352" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1252" parent="353" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1253" parent="353" name="string">
      <Comment>字符串</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1254" parent="353" name="date">
      <Comment>日期</Comment>
      <Position>3</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1255" parent="353" name="address">
      <Comment>详细地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1256" parent="353" name="code">
      <Comment>邮编</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1257" parent="353" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1258" parent="353" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1259" parent="353" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1260" parent="354" name="id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1261" parent="354" name="pid">
      <Comment>上级</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1262" parent="354" name="name">
      <Comment>名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1263" parent="354" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <index id="1264" parent="354" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1265" parent="354" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1266" parent="355" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1267" parent="355" name="poiname">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1268" parent="355" name="poiaddress">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1269" parent="355" name="latitude">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1270" parent="355" name="longitude">
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1271" parent="355" name="create_time">
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1272" parent="355" name="update_time">
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="1273" parent="355" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1274" parent="355" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1275" parent="356" name="id">
      <AutoIncrement>24</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1276" parent="356" name="name">
      <Comment>快递公司名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1277" parent="356" name="code">
      <Comment>快递公司编码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1278" parent="356" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1279" parent="356" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1280" parent="357" name="id">
      <AutoIncrement>67</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1281" parent="357" name="uid">
      <Comment>所属用户</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1282" parent="357" name="type">
      <Comment>反馈类型</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1283" parent="357" name="problem">
      <Comment>反馈内容</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1284" parent="357" name="image">
      <Comment>反馈图片</Comment>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1285" parent="357" name="contact_mode">
      <Comment>联系方式</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1286" parent="357" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1287" parent="357" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1288" parent="357" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1289" parent="358" name="id">
      <AutoIncrement>38</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1290" parent="358" name="spec_list">
      <Comment>规格属性数据</Comment>
      <Position>2</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1291" parent="358" name="category_id">
      <Comment>所属分类</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1292" parent="358" name="type">
      <Comment>商品类型:1=商城商品,2=抽奖商品</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1293" parent="358" name="name">
      <Comment>商品名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1294" parent="358" name="image">
      <Comment>商品图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1295" parent="358" name="images">
      <Comment>轮播图</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(1500)|0s</StoredType>
    </column>
    <column id="1296" parent="358" name="redeem_points">
      <Comment>商品金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1297" parent="358" name="inventory">
      <Comment>库存</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1298" parent="358" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1299" parent="358" name="parameter">
      <Comment>商品参数</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1300" parent="358" name="details">
      <Comment>商品详情</Comment>
      <Position>12</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1301" parent="358" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1302" parent="358" name="create_time">
      <Comment>创建时间</Comment>
      <Position>14</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1303" parent="358" name="update_time">
      <Comment>修改时间</Comment>
      <Position>15</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1304" parent="358" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1305" parent="358" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1306" parent="359" name="id">
      <AutoIncrement>42</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1307" parent="359" name="order_no">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1308" parent="359" name="uid">
      <Comment>所属用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1309" parent="359" name="goods_id">
      <Comment>所属商品</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1310" parent="359" name="name">
      <Comment>商品名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1311" parent="359" name="image">
      <Comment>商品图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1312" parent="359" name="redeem_points">
      <Comment>支付金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1313" parent="359" name="total_price">
      <Comment>商品总金额</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1314" parent="359" name="express_code">
      <Comment>快递公司编码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1315" parent="359" name="tracking_number">
      <Comment>快递单号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1316" parent="359" name="addr_id">
      <Comment>所属地址</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1317" parent="359" name="consignee_name">
      <Comment>收货人姓名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1318" parent="359" name="phone">
      <Comment>收货人电话</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1319" parent="359" name="area">
      <Comment>收货地区</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1320" parent="359" name="detail">
      <Comment>收货详细地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1321" parent="359" name="remark">
      <Comment>备注信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1322" parent="359" name="status">
      <Comment>状态:0=未支付,1=待发货,2=已发货,3=已完成</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1323" parent="359" name="create_time">
      <Comment>创建时间</Comment>
      <Position>18</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1324" parent="359" name="payment_time">
      <Comment>支付时间</Comment>
      <Position>19</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1325" parent="359" name="delivery_time">
      <Comment>发货时间</Comment>
      <Position>20</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1326" parent="359" name="completion_time">
      <Comment>完成时间</Comment>
      <Position>21</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1327" parent="359" name="sku_id">
      <Comment>规格ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1328" parent="359" name="sku_name">
      <Comment>规格名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1329" parent="359" name="num">
      <Comment>购买数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="1330" parent="359" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1331" parent="359" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1332" parent="360" name="id">
      <AutoIncrement>197156</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1333" parent="360" name="uid">
      <Comment>所属用户</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1334" parent="360" name="invited_id">
      <Comment>被邀请用户</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1335" parent="360" name="type">
      <Comment>类型:1=办卡,2=上课</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1336" parent="360" name="number_type">
      <Comment>类型:1=经验,2=次数</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1337" parent="360" name="number">
      <Comment>数量</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1338" parent="360" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1339" parent="360" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1340" parent="360" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1341" parent="361" name="id">
      <AutoIncrement>42</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1342" parent="361" name="name">
      <Comment>奖品名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1343" parent="361" name="image">
      <Comment>奖品图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1344" parent="361" name="type">
      <Comment>类型:1=商品,2=红包,3=会员卡,4=无,5=特殊奖品</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>enum(&apos;1&apos;, &apos;2&apos;, &apos;3&apos;, &apos;4&apos;)|0e</StoredType>
    </column>
    <column id="1345" parent="361" name="probability">
      <Comment>概率(%)</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="1346" parent="361" name="goods_id">
      <Comment>所属商品</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1347" parent="361" name="member_card_id">
      <Comment>所属会员卡</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1348" parent="361" name="frequency">
      <Comment>中奖次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1349" parent="361" name="price">
      <Comment>红包金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1350" parent="361" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1351" parent="361" name="create_time">
      <Comment>创建时间</Comment>
      <Position>11</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1352" parent="361" name="update_time">
      <Comment>修改时间</Comment>
      <Position>12</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1353" parent="361" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1354" parent="361" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1355" parent="362" name="id">
      <AutoIncrement>77</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1356" parent="362" name="name">
      <Comment>商品名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1357" parent="362" name="spec_list">
      <Comment>规格属性数据</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1358" parent="362" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1359" parent="362" name="update_time">
      <Comment>更新时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1360" parent="362" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1361" parent="362" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1362" parent="363" name="id">
      <AutoIncrement>275</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1363" parent="363" name="goods_id">
      <Comment>商品ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1364" parent="363" name="spec">
      <Comment>规格信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1365" parent="363" name="image">
      <Comment>规格图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1366" parent="363" name="price">
      <Comment>售价</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(10,2 digit) unsigned|0s</StoredType>
    </column>
    <column id="1367" parent="363" name="origin_price">
      <Comment>划线价</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10,2 digit) unsigned|0s</StoredType>
    </column>
    <column id="1368" parent="363" name="stock">
      <Comment>库存</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1369" parent="363" name="weight">
      <Comment>重量</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>float unsigned|0s</StoredType>
    </column>
    <column id="1370" parent="363" name="volume">
      <Comment>体积</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>float unsigned|0s</StoredType>
    </column>
    <column id="1371" parent="363" name="sku_code">
      <Comment>sku编码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1372" parent="363" name="create_time">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1373" parent="363" name="update_time">
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1374" parent="363" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1375" parent="363" name="goods_id_delete_time">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1376" parent="363" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1377" parent="364" name="id">
      <AutoIncrement>18</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1378" parent="364" name="name">
      <Comment>模板名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1379" parent="364" name="value">
      <Comment>模板参数</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1380" parent="364" name="status">
      <Comment>模板状态:0=禁用,1=启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>4</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1381" parent="364" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1382" parent="364" name="update_time">
      <Comment>修改时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1383" parent="364" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1384" parent="364" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1385" parent="365" name="id">
      <AutoIncrement>1073</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1386" parent="365" name="uid">
      <Comment>所属用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1387" parent="365" name="card_id">
      <Comment>所属会员卡</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1388" parent="365" name="start_time">
      <Comment>请假开始时间</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1389" parent="365" name="end_time">
      <Comment>请假结束时间</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1390" parent="365" name="notes">
      <Comment>备注信息</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1391" parent="365" name="status">
      <Comment>状态:0=请假中,1=已过期,2=已销假</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1392" parent="365" name="end_way">
      <Comment>结束请假方式:0=终止请假,1=取消请假</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1393" parent="365" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1394" parent="365" name="duration">
      <Comment>请假时长</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="1395" parent="365" name="pin_time">
      <Comment>销假时间</Comment>
      <Position>11</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1396" parent="365" name="new_expiration_date">
      <Comment>新失效时间</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1397" parent="365" name="type">
      <Comment>操作人:0=学员操作,1=员工操作</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1398" parent="365" name="operator_id">
      <Comment>操作人</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="1399" parent="365" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1400" parent="365" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1401" parent="366" name="id">
      <AutoIncrement>15</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1402" parent="366" name="name">
      <Comment>等级名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1403" parent="366" name="level">
      <Comment>等级</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1404" parent="366" name="experience">
      <Comment>经验值</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1405" parent="366" name="award">
      <Comment>奖品</Comment>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1406" parent="366" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1407" parent="366" name="update_time">
      <Comment>修改时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1408" parent="366" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1409" parent="366" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1410" parent="367" name="id">
      <AutoIncrement>18083</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1411" parent="367" name="user_id">
      <Comment>中奖用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1412" parent="367" name="type">
      <Comment>类型:1=商品,2=红包,3=会员卡</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>enum(&apos;1&apos;, &apos;2&apos;, &apos;3&apos;, &apos;4&apos;, &apos;5&apos;)|0e</StoredType>
    </column>
    <column id="1413" parent="367" name="goods_id">
      <Comment>中奖商品</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1414" parent="367" name="member_card_id">
      <Comment>中奖会员卡</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1415" parent="367" name="price">
      <Comment>中奖金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="1416" parent="367" name="status">
      <Comment>状态:0=待兑换,1=已兑换,2=已发货,3=已过期</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1417" parent="367" name="create_time">
      <Comment>抽奖时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1418" parent="367" name="exchange_time">
      <Comment>兑换时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1419" parent="367" name="end_time">
      <Comment>到期时间</Comment>
      <Position>10</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1420" parent="367" name="addr_id">
      <Comment>所属地址</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1421" parent="367" name="consignee_name">
      <Comment>收货人姓名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1422" parent="367" name="phone">
      <Comment>收货人电话</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1423" parent="367" name="area">
      <Comment>收货地区</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1424" parent="367" name="detail">
      <Comment>收货详细地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1425" parent="367" name="remark">
      <Comment>备注信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1426" parent="367" name="payment_code">
      <Comment>收款码</Comment>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1427" parent="367" name="reward_type">
      <Comment>奖励类型:0=抽奖获得,1=等级奖励</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1428" parent="367" name="completion_time">
      <Comment>完成时间</Comment>
      <Position>19</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1429" parent="367" name="express_code">
      <Comment>快递公司编码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>20</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1430" parent="367" name="tracking_number">
      <Comment>快递单号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>21</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1431" parent="367" name="coupon_id">
      <Comment>中奖优惠券</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1432" parent="367" name="name">
      <Comment>奖品名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1433" parent="367" name="image">
      <Comment>奖品图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1434" parent="367" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1435" parent="367" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1436" parent="368" name="id">
      <AutoIncrement>67</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1437" parent="368" name="name">
      <Comment>会员卡名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1438" parent="368" name="card_id">
      <Comment>所属卡活动</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1439" parent="368" name="type">
      <Comment>会员卡类型:0=次卡,1=年卡,2=月卡,3=周卡,4=时长卡（）天</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;, &apos;2&apos;, &apos;3&apos;, &apos;4&apos;)|0e</StoredType>
    </column>
    <column id="1440" parent="368" name="image">
      <Comment>会员卡图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1441" parent="368" name="number">
      <Comment>次数/时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1442" parent="368" name="duration">
      <Comment>次卡时长（天）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1443" parent="368" name="proportion">
      <Comment>多门店多支付（）%</Comment>
      <Position>8</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="1444" parent="368" name="price">
      <Comment>支付金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1445" parent="368" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1446" parent="368" name="create_time">
      <Comment>创建时间</Comment>
      <Position>11</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1447" parent="368" name="update_time">
      <Comment>修改时间</Comment>
      <Position>12</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1448" parent="368" name="leave_frequency">
      <Comment>请假次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1449" parent="368" name="leave_duration">
      <Comment>单次请假时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1450" parent="368" name="smoke_frequency">
      <Comment>抽奖次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1451" parent="368" name="types">
      <Comment>状态:0=赠送卡,1=购买卡,2=私教卡</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <index id="1452" parent="368" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1453" parent="368" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1454" parent="369" name="id">
      <AutoIncrement>93342</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1455" parent="369" name="uid">
      <Comment>所属用户</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1456" parent="369" name="source_id">
      <Comment>来源ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1457" parent="369" name="type">
      <Comment>类型:1=系统消息,2=通知,3=周报,4=月报,5=年报,6=上传视频通知,7=升级通知</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1458" parent="369" name="content">
      <Comment>通知内容</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1459" parent="369" name="date">
      <Comment>日期</Comment>
      <Position>6</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1460" parent="369" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1461" parent="369" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1462" parent="369" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1463" parent="370" name="id">
      <AutoIncrement>10</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1464" parent="370" name="name">
      <Comment>通知名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1465" parent="370" name="type">
      <Comment>模板类型:1=账号绑定通知,2=开课提醒,3=会员卡过期提醒,4=报名结果通知,5=会员卡状态提醒,6=会员预约课程提醒,7=服务人员变更提醒,8=签到成功通知</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1466" parent="370" name="template">
      <Comment>模板ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1467" parent="370" name="content">
      <Comment>通知内容</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1468" parent="370" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1469" parent="370" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1470" parent="370" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1471" parent="371" name="version">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1472" parent="371" name="migration_name">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1473" parent="371" name="start_time">
      <Position>3</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1474" parent="371" name="end_time">
      <Position>4</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="1475" parent="371" name="breakpoint">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="1476" parent="371" name="PRIMARY">
      <ColNames>version</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1477" parent="371" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1478" parent="372" name="id">
      <AutoIncrement>108</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1479" parent="372" name="name">
      <Comment>镜像名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1480" parent="372" name="ImageId">
      <Comment>镜像ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1481" parent="372" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1482" parent="372" name="delete_time">
      <Comment>删除时间</Comment>
      <Position>5</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="1483" parent="372" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1484" parent="372" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1485" parent="373" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1486" parent="373" name="title">
      <Comment>标题</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1487" parent="373" name="content">
      <Comment>内容</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1488" parent="373" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1489" parent="373" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1490" parent="373" name="update_time">
      <Comment>修改时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1491" parent="373" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1492" parent="373" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1493" parent="374" name="id">
      <AutoIncrement>35118</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1494" parent="374" name="out_trade_no">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1495" parent="374" name="sale_id">
      <Comment>操作人</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1496" parent="374" name="card_id">
      <Comment>所属会员卡</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1497" parent="374" name="type">
      <Comment>类型:0=新购,1=续费,2=转卡,3=转让</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1498" parent="374" name="payment_price">
      <Comment>支付金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1499" parent="374" name="open_store_id">
      <Comment>办理门店</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1500" parent="374" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1501" parent="374" name="payment_time">
      <Comment>支付时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1502" parent="374" name="notes">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1503" parent="374" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1504" parent="374" name="card_id">
      <ColNames>card_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1505" parent="374" name="type">
      <ColNames>type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1506" parent="374" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1507" parent="375" name="id">
      <AutoIncrement>20557</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1508" parent="375" name="uid">
      <Comment>所属用户</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1509" parent="375" name="card_id">
      <Comment>来源会员卡</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1510" parent="375" name="store_id">
      <Comment>所属门店</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1511" parent="375" name="sale_id">
      <Comment>所属销售</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1512" parent="375" name="price">
      <Comment>业绩金额</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1513" parent="375" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1514" parent="375" name="type">
      <Comment>类型:0=门店业绩,1=销售业绩</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1515" parent="375" name="performance_type">
      <Comment>业绩类型:1=新购,2=转卡,3=转让,4=续费</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1516" parent="375" name="payment_price">
      <Comment>办理金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1517" parent="375" name="performance_ratio">
      <Comment>业绩比例</Comment>
      <Position>11</Position>
      <StoredType>float|0s</StoredType>
    </column>
    <column id="1518" parent="375" name="delete_time">
      <Comment>删除时间</Comment>
      <Position>12</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1519" parent="375" name="deserve_price">
      <Comment>应得金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1520" parent="375" name="handle">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="1521" parent="375" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1522" parent="375" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1523" parent="375" name="create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1524" parent="375" name="type">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1525" parent="375" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1526" parent="376" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment> </Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1527" parent="376" name="uid">
      <Comment>所属用户</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1528" parent="376" name="pid">
      <Comment>来源用户</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1529" parent="376" name="consume_id">
      <Comment>来源id</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1530" parent="376" name="price">
      <Comment>数量</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1531" parent="376" name="balance">
      <Comment>剩余数量</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1532" parent="376" name="type">
      <Comment>类型:1=兑换扣除,2=邀请增加</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1533" parent="376" name="note">
      <Comment>备注信息</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1534" parent="376" name="create_time">
      <Comment>添加时间</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1535" parent="376" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1536" parent="376" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1537" parent="377" name="id">
      <AutoIncrement>5239</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1538" parent="377" name="card_id">
      <Comment>所属会员卡</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1539" parent="377" name="renew_front_expiration_time">
      <Comment>续费前失效时间</Comment>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1540" parent="377" name="service_start_time">
      <Comment>服务起始时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1541" parent="377" name="payment_price">
      <Comment>支付金额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1542" parent="377" name="renewal_days">
      <Comment>续费天数</Comment>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1543" parent="377" name="renewal_rights">
      <Comment>续费权益</Comment>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1544" parent="377" name="expiration_time_after_renewal">
      <Comment>续费后失效时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1545" parent="377" name="renewal_method">
      <Comment>续费方式:1=微信,2=支付宝,3=现金,4=转账,5=刷卡,6=抖音,7=美团/大众</Comment>
      <Position>9</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1546" parent="377" name="notes">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="1547" parent="377" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1548" parent="377" name="operator_id">
      <Comment>操作人</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1549" parent="377" name="renewal_type">
      <Comment>续费类型:0=增加权益,1=扣除权益</Comment>
      <Position>13</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1550" parent="377" name="buckle_day">
      <Comment>扣费天数</Comment>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1551" parent="377" name="buckle_frequency">
      <Comment>扣费次数</Comment>
      <Position>15</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1552" parent="377" name="handle_type">
      <Comment>处理:0=未处理,1=已处理</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="1553" parent="377" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1554" parent="377" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1555" parent="378" name="id">
      <AutoIncrement>47784</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1556" parent="378" name="store_id">
      <Comment>门店</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1557" parent="378" name="name">
      <Comment>课程名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1558" parent="378" name="frequency">
      <Comment>扣卡次数</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1559" parent="378" name="level">
      <Comment>课程级别</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1560" parent="378" name="dance">
      <Comment>课程舞种</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1561" parent="378" name="course_id">
      <Comment>所属课程</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1562" parent="378" name="teacher_id">
      <Comment>任课老师</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1563" parent="378" name="duration">
      <Comment>课程时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1564" parent="378" name="start_time">
      <Comment>上课开始时间</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1565" parent="378" name="end_time">
      <Comment>上课结束时间</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1566" parent="378" name="minimum_reservation">
      <Comment>开课的最小预约人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1567" parent="378" name="maximum_reservation">
      <Comment>开课的最大预约人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1568" parent="378" name="cancel">
      <Comment>自动取消:0=关闭,1=开启</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1569" parent="378" name="equivalent">
      <Comment>课程预约满员时开启等位:0=关闭,1=开启</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1570" parent="378" name="cancel_hour">
      <Comment>自动取消时间：课前（）小时</Comment>
      <Position>16</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1571" parent="378" name="cancel_minute">
      <Comment>自动取消时间：课前（）分钟</Comment>
      <Position>17</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1572" parent="378" name="autonomy_cancel_hour">
      <Comment>会员自主取消预约时间：课前（）小时</Comment>
      <Position>18</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1573" parent="378" name="autonomy_cancel_minute">
      <Comment>会员自主取消预约时间：课前（）分钟</Comment>
      <Position>19</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1574" parent="378" name="equivalent_time_hour">
      <Comment>等位时的最迟截止时间：课前（）小时</Comment>
      <Position>20</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1575" parent="378" name="equivalent_time_minute">
      <Comment>等位时的最迟截止时间：课前（）分钟</Comment>
      <Position>21</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1576" parent="378" name="start_reservation_hour">
      <Comment>开始预约的时间：课前（）小时</Comment>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1577" parent="378" name="start_reservation_minute">
      <Comment>开始预约的时间：课前（）分钟</Comment>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1578" parent="378" name="end_reservation_hour">
      <Comment>截止预约的时间：课前（）小时</Comment>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1579" parent="378" name="end_reservation_minute">
      <Comment>截止预约的时间：课前（）分钟</Comment>
      <NotNull>1</NotNull>
      <Position>25</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1580" parent="378" name="music_link">
      <Comment>音乐链接</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1581" parent="378" name="review_video">
      <Comment>回顾视频</Comment>
      <Position>27</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1582" parent="378" name="notes">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>28</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1583" parent="378" name="create_time">
      <Comment>创建时间</Comment>
      <Position>29</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1584" parent="378" name="update_time">
      <Comment>修改时间</Comment>
      <Position>30</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1585" parent="378" name="completion_time">
      <Comment>完成时间</Comment>
      <Position>31</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1586" parent="378" name="status">
      <Comment>状态:1=待开课,2=上课中,3=已完成,4=已取消</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>32</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1587" parent="378" name="cancel_time">
      <Comment>取消时间</Comment>
      <Position>33</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1588" parent="378" name="font_color">
      <Comment>字体色</Comment>
      <NotNull>1</NotNull>
      <Position>34</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1589" parent="378" name="background_color">
      <Comment>背景色</Comment>
      <NotNull>1</NotNull>
      <Position>35</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1590" parent="378" name="video">
      <Comment>预告视频</Comment>
      <Position>36</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1591" parent="378" name="dance_id">
      <Comment>课程舞种</Comment>
      <NotNull>1</NotNull>
      <Position>37</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1592" parent="378" name="videoFileList">
      <Comment>预告视频文件信息</Comment>
      <Position>38</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1593" parent="378" name="reviewVideoFileList">
      <Comment>本节回顾视频文件信息</Comment>
      <Position>39</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1594" parent="378" name="continuous_courses_id">
      <Comment>连堂课程</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>40</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1595" parent="378" name="delete_time">
      <Comment>删除时间</Comment>
      <Position>41</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1596" parent="378" name="class_reminder">
      <Comment>上课提醒通知:0=未通知,1=已通知</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>42</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1597" parent="378" name="preview">
      <Comment>预告内容</Comment>
      <NotNull>1</NotNull>
      <Position>43</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1598" parent="378" name="continuous_courses_type">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>44</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="1599" parent="378" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1600" parent="378" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1601" parent="378" name="teacher_id">
      <ColNames>teacher_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1602" parent="378" name="start_time">
      <ColNames>start_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1603" parent="378" name="end_time">
      <ColNames>end_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1604" parent="378" name="status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1605" parent="378" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1606" parent="379" name="id">
      <AutoIncrement>41467</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1607" parent="379" name="store_id">
      <Comment>门店</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1608" parent="379" name="name">
      <Comment>课程名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1609" parent="379" name="frequency">
      <Comment>扣卡次数</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1610" parent="379" name="level">
      <Comment>课程级别</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1611" parent="379" name="dance">
      <Comment>课程舞种</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1612" parent="379" name="course_id">
      <Comment>所属课程</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1613" parent="379" name="teacher_id">
      <Comment>任课老师</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1614" parent="379" name="duration">
      <Comment>课程时长</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1615" parent="379" name="start_time">
      <Comment>上课开始时间</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1616" parent="379" name="end_time">
      <Comment>上课结束时间</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1617" parent="379" name="minimum_reservation">
      <Comment>开课的最小预约人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1618" parent="379" name="maximum_reservation">
      <Comment>开课的最大预约人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1619" parent="379" name="cancel">
      <Comment>自动取消:0=关闭,1=开启</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1620" parent="379" name="equivalent">
      <Comment>课程预约满员时开启等位:0=关闭,1=开启</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1621" parent="379" name="cancel_hour">
      <Comment>自动取消时间：课前（）小时</Comment>
      <Position>16</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1622" parent="379" name="cancel_minute">
      <Comment>自动取消时间：课前（）分钟</Comment>
      <Position>17</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1623" parent="379" name="autonomy_cancel_hour">
      <Comment>会员自主取消预约时间：课前（）小时</Comment>
      <Position>18</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1624" parent="379" name="autonomy_cancel_minute">
      <Comment>会员自主取消预约时间：课前（）分钟</Comment>
      <Position>19</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1625" parent="379" name="equivalent_time_hour">
      <Comment>等位时的最迟截止时间：课前（）小时</Comment>
      <Position>20</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1626" parent="379" name="equivalent_time_minute">
      <Comment>等位时的最迟截止时间：课前（）分钟</Comment>
      <Position>21</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1627" parent="379" name="start_reservation_hour">
      <Comment>开始预约的时间：课前（）小时</Comment>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1628" parent="379" name="start_reservation_minute">
      <Comment>开始预约的时间：课前（）分钟</Comment>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1629" parent="379" name="end_reservation_hour">
      <Comment>截止预约的时间：课前（）小时</Comment>
      <NotNull>1</NotNull>
      <Position>24</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1630" parent="379" name="end_reservation_minute">
      <Comment>截止预约的时间：课前（）分钟</Comment>
      <NotNull>1</NotNull>
      <Position>25</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1631" parent="379" name="music_link">
      <Comment>音乐链接</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1632" parent="379" name="review_video">
      <Comment>回顾视频</Comment>
      <Position>27</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1633" parent="379" name="notes">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>28</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1634" parent="379" name="create_time">
      <Comment>创建时间</Comment>
      <Position>29</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1635" parent="379" name="update_time">
      <Comment>修改时间</Comment>
      <Position>30</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1636" parent="379" name="completion_time">
      <Comment>完成时间</Comment>
      <Position>31</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1637" parent="379" name="status">
      <Comment>状态:1=待开课,2=上课中,3=已完成,4=已取消</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>32</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1638" parent="379" name="cancel_time">
      <Comment>取消时间</Comment>
      <Position>33</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1639" parent="379" name="font_color">
      <Comment>字体色</Comment>
      <NotNull>1</NotNull>
      <Position>34</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1640" parent="379" name="background_color">
      <Comment>背景色</Comment>
      <NotNull>1</NotNull>
      <Position>35</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1641" parent="379" name="video">
      <Comment>预告视频</Comment>
      <Position>36</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1642" parent="379" name="dance_id">
      <Comment>课程舞种</Comment>
      <NotNull>1</NotNull>
      <Position>37</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1643" parent="379" name="videoFileList">
      <Comment>预告视频文件信息</Comment>
      <Position>38</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1644" parent="379" name="reviewVideoFileList">
      <Comment>本节回顾视频文件信息</Comment>
      <Position>39</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1645" parent="379" name="continuous_courses_id">
      <Comment>连堂课程</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>40</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1646" parent="379" name="delete_time">
      <Comment>删除时间</Comment>
      <Position>41</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1647" parent="379" name="class_reminder">
      <Comment>上课提醒通知:0=未通知,1=已通知</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>42</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1648" parent="379" name="preview">
      <Comment>预告内容</Comment>
      <NotNull>1</NotNull>
      <Position>43</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1649" parent="379" name="continuous_courses_type">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>44</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="1650" parent="379" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1651" parent="379" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1652" parent="379" name="teacher_id">
      <ColNames>teacher_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1653" parent="379" name="start_time">
      <ColNames>start_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1654" parent="379" name="end_time">
      <ColNames>end_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1655" parent="379" name="status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1656" parent="379" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1657" parent="380" name="id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1658" parent="380" name="name">
      <Comment>规则名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1659" parent="380" name="controller">
      <Comment>控制器</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1660" parent="380" name="controller_as">
      <Comment>控制器别名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1661" parent="380" name="data_table">
      <Comment>对应数据表</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1662" parent="380" name="connection">
      <Comment>数据库连接配置标识</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1663" parent="380" name="primary_key">
      <Comment>数据表主键</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1664" parent="380" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="1665" parent="380" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1666" parent="380" name="create_time">
      <Comment>创建时间</Comment>
      <Position>10</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1667" parent="380" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1668" parent="380" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1669" parent="381" name="id">
      <AutoIncrement>388</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1670" parent="381" name="admin_id">
      <Comment>操作管理员</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1671" parent="381" name="recycle_id">
      <Comment>回收规则ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1672" parent="381" name="data">
      <Comment>回收的数据</Comment>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1673" parent="381" name="data_table">
      <Comment>数据表</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1674" parent="381" name="connection">
      <Comment>数据库连接配置标识</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1675" parent="381" name="primary_key">
      <Comment>数据表主键</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1676" parent="381" name="is_restore">
      <Comment>是否已还原:0=否,1=是</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1677" parent="381" name="ip">
      <Comment>操作者IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1678" parent="381" name="useragent">
      <Comment>User-Agent</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1679" parent="381" name="create_time">
      <Comment>创建时间</Comment>
      <Position>11</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1680" parent="381" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1681" parent="381" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1682" parent="382" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1683" parent="382" name="name">
      <Comment>规则名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1684" parent="382" name="controller">
      <Comment>控制器</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1685" parent="382" name="controller_as">
      <Comment>控制器别名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1686" parent="382" name="data_table">
      <Comment>对应数据表</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1687" parent="382" name="connection">
      <Comment>数据库连接配置标识</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1688" parent="382" name="primary_key">
      <Comment>数据表主键</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1689" parent="382" name="data_fields">
      <Comment>敏感数据字段</Comment>
      <Position>8</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1690" parent="382" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="1691" parent="382" name="update_time">
      <Comment>更新时间</Comment>
      <Position>10</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1692" parent="382" name="create_time">
      <Comment>创建时间</Comment>
      <Position>11</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1693" parent="382" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1694" parent="382" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1695" parent="383" name="id">
      <AutoIncrement>496</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1696" parent="383" name="admin_id">
      <Comment>操作管理员</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1697" parent="383" name="sensitive_id">
      <Comment>敏感数据规则ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1698" parent="383" name="data_table">
      <Comment>数据表</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1699" parent="383" name="connection">
      <Comment>数据库连接配置标识</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1700" parent="383" name="primary_key">
      <Comment>数据表主键</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1701" parent="383" name="data_field">
      <Comment>被修改字段</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1702" parent="383" name="data_comment">
      <Comment>被修改项</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1703" parent="383" name="id_value">
      <Comment>被修改项主键值</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1704" parent="383" name="before">
      <Comment>修改前</Comment>
      <Position>10</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1705" parent="383" name="after">
      <Comment>修改后</Comment>
      <Position>11</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1706" parent="383" name="ip">
      <Comment>操作者IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1707" parent="383" name="useragent">
      <Comment>User-Agent</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1708" parent="383" name="is_rollback">
      <Comment>是否已回滚:0=否,1=是</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1709" parent="383" name="create_time">
      <Comment>创建时间</Comment>
      <Position>15</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1710" parent="383" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1711" parent="383" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1712" parent="384" name="id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1713" parent="384" name="store_id">
      <Comment>所属门店</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1714" parent="384" name="nickname">
      <Comment>昵称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1715" parent="384" name="mobile">
      <Comment>手机</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(11)|0s</StoredType>
    </column>
    <column id="1716" parent="384" name="avatar">
      <Comment>头像</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1717" parent="384" name="last_login_time">
      <Comment>上次登录时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1718" parent="384" name="last_login_ip">
      <Comment>上次登录IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1719" parent="384" name="login_failure">
      <Comment>登录失败次数</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1720" parent="384" name="join_ip">
      <Comment>加入IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1721" parent="384" name="join_time">
      <Comment>加入时间</Comment>
      <Position>10</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1722" parent="384" name="password">
      <Comment>密码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1723" parent="384" name="salt">
      <Comment>密码盐</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1724" parent="384" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1725" parent="384" name="update_time">
      <Comment>更新时间</Comment>
      <Position>14</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1726" parent="384" name="create_time">
      <Comment>创建时间</Comment>
      <Position>15</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1727" parent="384" name="secondary_card_contract">
      <Comment>次卡合同模板</Comment>
      <Position>16</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1728" parent="384" name="duration_card_contract">
      <Comment>时长卡合同模板</Comment>
      <Position>17</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="1729" parent="384" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1730" parent="384" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1731" parent="385" name="id">
      <AutoIncrement>13</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1732" parent="385" name="image">
      <Comment>店铺封面图</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1733" parent="385" name="poster">
      <Comment>门店海报</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(1500)|0s</StoredType>
    </column>
    <column id="1734" parent="385" name="route_guidance">
      <Comment>路线指引</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1735" parent="385" name="name">
      <Comment>门店名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1736" parent="385" name="introduce">
      <Comment>门店介绍</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1737" parent="385" name="mobile">
      <Comment>联系电话</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1738" parent="385" name="start_time">
      <Comment>营业开始时间</Comment>
      <Position>8</Position>
      <StoredType>time|0s</StoredType>
    </column>
    <column id="1739" parent="385" name="end_time">
      <Comment>营业结束时间</Comment>
      <Position>9</Position>
      <StoredType>time|0s</StoredType>
    </column>
    <column id="1740" parent="385" name="city">
      <Comment>地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1741" parent="385" name="address">
      <Comment>详细地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1742" parent="385" name="longitude">
      <Comment>经度</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1743" parent="385" name="latitude">
      <Comment>纬度</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1744" parent="385" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1745" parent="385" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1746" parent="385" name="create_time">
      <Comment>创建时间</Comment>
      <Position>16</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1747" parent="385" name="update_time">
      <Comment>修改时间</Comment>
      <Position>17</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1748" parent="385" name="background">
      <Comment>背景颜色</Comment>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1749" parent="385" name="navigation_bar">
      <Comment>导航栏颜色</Comment>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1750" parent="385" name="written_words">
      <Comment>文字颜色</Comment>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1751" parent="385" name="button">
      <Comment>按钮颜色</Comment>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1752" parent="385" name="entry_time">
      <Comment>入驻时间</Comment>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1753" parent="385" name="attendance_scope">
      <Comment>考勤范围（）米</Comment>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="1754" parent="385" name="fileList">
      <Comment>文件信息</Comment>
      <Position>24</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1755" parent="385" name="carousel_background">
      <Comment>轮播背景图</Comment>
      <Position>25</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1756" parent="385" name="carousel">
      <Comment>轮播图</Comment>
      <Position>26</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1757" parent="385" name="course_carousel">
      <Comment>课程轮播图</Comment>
      <Position>27</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="1758" parent="385" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1759" parent="385" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1760" parent="386" name="id">
      <AutoIncrement>76179</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1761" parent="386" name="uid">
      <Comment>所属用户</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1762" parent="386" name="store_id">
      <Comment>登记门店</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1763" parent="386" name="service_handler_id">
      <Comment>服务办理人</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1764" parent="386" name="create_time">
      <Comment>登记时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1765" parent="386" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1766" parent="386" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1767" parent="386" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1768" parent="387" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1769" parent="387" name="account">
      <Comment>后台管理员账号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1770" parent="387" name="head_pic">
      <Comment>后台管理员头像</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1771" parent="387" name="pwd">
      <Comment>后台管理员密码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1772" parent="387" name="real_name">
      <Comment>后台管理员姓名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1773" parent="387" name="roles">
      <Comment>后台管理员权限(menus_id)</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1774" parent="387" name="last_ip">
      <Comment>后台管理员最后一次登录ip</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1775" parent="387" name="last_time">
      <Comment>后台管理员最后一次登录时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1776" parent="387" name="add_time">
      <Comment>后台管理员添加时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1777" parent="387" name="login_count">
      <Comment>登录次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1778" parent="387" name="level">
      <Comment>后台管理员级别</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1779" parent="387" name="status">
      <Comment>后台管理员状态 1有效0无效</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1780" parent="387" name="is_del">
      <Comment>是否删除 1有效0无效</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="1781" parent="387" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1782" parent="387" name="account">
      <ColNames>account
status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1783" parent="387" name="account_2">
      <ColNames>account</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1784" parent="387" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1785" parent="388" name="att_id">
      <AutoIncrement>4</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1786" parent="388" name="name">
      <Comment>附件名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1787" parent="388" name="att_dir">
      <Comment>附件路径</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1788" parent="388" name="satt_dir">
      <Comment>压缩图片路径</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1789" parent="388" name="att_size">
      <Comment>附件大小</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1790" parent="388" name="att_type">
      <Comment>附件类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1791" parent="388" name="pid">
      <Comment>分类ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1792" parent="388" name="time">
      <Comment>上传时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1793" parent="388" name="image_type">
      <Comment>图片上传类型 1本地 2七牛云 3OSS 4COS </Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1794" parent="388" name="module_type">
      <Comment>图片上传模块类型 1 后台上传 2 用户生成</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1795" parent="388" name="real_name">
      <Comment>原始文件名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1796" parent="388" name="PRIMARY">
      <ColNames>att_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1797" parent="388" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1798" parent="389" name="id">
      <AutoIncrement>163</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1799" parent="389" name="image">
      <Comment>照片</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1800" parent="389" name="masterpiece">
      <Comment>高光时刻视频(代表作)</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1801" parent="389" name="level">
      <Comment>课程级别</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1802" parent="389" name="dance_id">
      <Comment>擅长舞蹈</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1803" parent="389" name="name">
      <Comment>名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1804" parent="389" name="work_year">
      <Comment>工作年限</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1805" parent="389" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1806" parent="389" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1807" parent="389" name="create_time">
      <Comment>创建时间</Comment>
      <Position>10</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1808" parent="389" name="update_time">
      <Comment>修改时间</Comment>
      <Position>11</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1809" parent="389" name="evaluate">
      <Comment>老师评价</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1810" parent="389" name="score">
      <Comment>评分</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="1811" parent="389" name="fileList">
      <Comment>视频信息</Comment>
      <Position>14</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1812" parent="389" name="id_number">
      <Comment>身份证号</Comment>
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="1813" parent="389" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1814" parent="389" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1815" parent="390" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1816" parent="390" name="title">
      <Comment>标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1817" parent="390" name="keyword_rows">
      <Comment>关键词</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1818" parent="390" name="content">
      <Comment>内容</Comment>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1819" parent="390" name="views">
      <Comment>浏览量</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1820" parent="390" name="likes">
      <Comment>有帮助数</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1821" parent="390" name="dislikes">
      <Comment>无帮助数</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1822" parent="390" name="note_textarea">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1823" parent="390" name="status">
      <Comment>状态:0=隐藏,1=正常</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="1824" parent="390" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1825" parent="390" name="update_time">
      <Comment>更新时间</Comment>
      <Position>11</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1826" parent="390" name="create_time">
      <Comment>创建时间</Comment>
      <Position>12</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1827" parent="390" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1828" parent="390" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1829" parent="391" name="token">
      <Comment>Token</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1830" parent="391" name="type">
      <Comment>类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(15)|0s</StoredType>
    </column>
    <column id="1831" parent="391" name="user_id">
      <Comment>用户ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1832" parent="391" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1833" parent="391" name="expire_time">
      <Comment>过期时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1834" parent="391" name="PRIMARY">
      <ColNames>token</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1835" parent="391" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1836" parent="392" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1837" parent="392" name="card_id">
      <Comment>所属会员卡</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1838" parent="392" name="transfer_uid">
      <Comment>转让人</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1839" parent="392" name="undertake_uid">
      <Comment>承接人</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1840" parent="392" name="gender">
      <Comment>性别:0=未知,1=男,2=女</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1841" parent="392" name="cost">
      <Comment>承接费用</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1842" parent="392" name="notes">
      <Comment>备注</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="1843" parent="392" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1844" parent="392" name="operator_id">
      <Comment>操作人</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="1845" parent="392" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1846" parent="392" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1847" parent="393" name="id">
      <AutoIncrement>30537</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1848" parent="393" name="group_id">
      <Comment>分组ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1849" parent="393" name="username">
      <Comment>用户名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1850" parent="393" name="nickname">
      <Comment>昵称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1851" parent="393" name="social_id">
      <Comment>社区ID，用于用户展示</Comment>
      <Position>5</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1852" parent="393" name="email">
      <Comment>邮箱</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1853" parent="393" name="mobile">
      <Comment>手机</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(15)|0s</StoredType>
    </column>
    <column id="1854" parent="393" name="note_image">
      <Comment>备注图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1855" parent="393" name="avatar">
      <Comment>头像</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1856" parent="393" name="gender">
      <Comment>性别:0=未知,1=男,2=女</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1857" parent="393" name="birthday">
      <Comment>生日</Comment>
      <Position>11</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1858" parent="393" name="money">
      <Comment>余额</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1859" parent="393" name="score">
      <Comment>积分</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1860" parent="393" name="last_login_time">
      <Comment>上次登录时间</Comment>
      <Position>14</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1861" parent="393" name="last_login_ip">
      <Comment>上次登录IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1862" parent="393" name="login_failure">
      <Comment>登录失败次数</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1863" parent="393" name="join_ip">
      <Comment>加入IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1864" parent="393" name="join_time">
      <Comment>加入时间</Comment>
      <Position>18</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1865" parent="393" name="motto">
      <Comment>签名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1866" parent="393" name="password">
      <Comment>密码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1867" parent="393" name="salt">
      <Comment>密码盐</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1868" parent="393" name="status">
      <Comment>状态</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1869" parent="393" name="update_time">
      <Comment>更新时间</Comment>
      <Position>23</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1870" parent="393" name="create_time">
      <Comment>创建时间</Comment>
      <Position>24</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1871" parent="393" name="frequency">
      <Comment>会员卡次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>25</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1872" parent="393" name="share_code">
      <Comment>分享二维码</Comment>
      <Position>26</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1873" parent="393" name="sale_id">
      <Comment>所属销售</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>27</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1874" parent="393" name="online">
      <Comment>在线状态：0：不在线 1：在线</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>28</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1875" parent="393" name="introduction">
      <Comment>简介</Comment>
      <Position>29</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1876" parent="393" name="pid">
      <Comment>上级ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>30</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1877" parent="393" name="openid">
      <Comment>openid</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>31</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1878" parent="393" name="store_id">
      <Comment>所属门店</Comment>
      <Position>32</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1879" parent="393" name="register_time">
      <Comment>登记时间</Comment>
      <Position>33</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="1880" parent="393" name="luck_draw_frequency">
      <Comment>抽奖次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>34</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1881" parent="393" name="experience_value">
      <Comment>当前经验值</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>35</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1882" parent="393" name="level">
      <Comment>当前等级</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>36</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1883" parent="393" name="payment_code">
      <Comment>收款码</Comment>
      <Position>37</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1884" parent="393" name="grant">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>38</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1885" parent="393" name="unionid">
      <Comment>unionid</Comment>
      <Position>39</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1886" parent="393" name="official_openid">
      <Comment>公众号openid</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>40</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1887" parent="393" name="message_status">
      <Comment>消息状态开关</Comment>
      <Position>41</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1888" parent="393" name="sign_contract_type">
      <Comment>是否签合同:0=不签署,1=签署</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>42</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1889" parent="393" name="is_member">
      <Comment>是否是会员 0-否 1-是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>43</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1890" parent="393" name="remaining_votes">
      <Comment>剩余投票次数</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>44</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1891" parent="393" name="bio">
      <Comment>个人简介</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>45</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1892" parent="393" name="dance_type">
      <Comment>学习舞种</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>46</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1893" parent="393" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1894" parent="393" name="uk_social_id">
      <ColNames>social_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1895" parent="393" name="idx_social_id">
      <ColNames>social_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1896" parent="393" name="mobile">
      <ColNames>mobile</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1897" parent="393" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1898" parent="393" name="uk_social_id">
      <UnderlyingIndexName>uk_social_id</UnderlyingIndexName>
    </key>
    <column id="1899" parent="394" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1900" parent="394" name="uid">
      <Comment>所属用户</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1901" parent="394" name="name">
      <Comment>优惠券名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1902" parent="394" name="coupon_id">
      <Comment>所属优惠券</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1903" parent="394" name="type">
      <Comment>类型:0=无门槛,1=满减券</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="1904" parent="394" name="use">
      <Comment>使用状态:1=未使用,2=已使用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1905" parent="394" name="discount_price">
      <Comment>优惠金额</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1906" parent="394" name="full_price">
      <Comment>满减金额</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1907" parent="394" name="end_time">
      <Comment>到期时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1908" parent="394" name="create_time">
      <Comment>获得时间</Comment>
      <Position>10</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1909" parent="394" name="use_time">
      <Comment>使用时间</Comment>
      <Position>11</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="1910" parent="394" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1911" parent="394" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1912" parent="395" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1913" parent="395" name="name">
      <Comment>组名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1914" parent="395" name="rules">
      <Comment>权限节点</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1915" parent="395" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="1916" parent="395" name="update_time">
      <Comment>更新时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1917" parent="395" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1918" parent="395" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1919" parent="395" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1920" parent="396" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1921" parent="396" name="user_id">
      <Comment>会员ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1922" parent="396" name="money">
      <Comment>变更余额</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1923" parent="396" name="before">
      <Comment>变更前余额</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1924" parent="396" name="after">
      <Comment>变更后余额</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1925" parent="396" name="memo">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1926" parent="396" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1927" parent="396" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1928" parent="396" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1929" parent="397" name="id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1930" parent="397" name="pid">
      <Comment>上级菜单</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1931" parent="397" name="type">
      <Comment>类型:route=路由,menu_dir=菜单目录,menu=菜单项,nav_user_menu=顶栏会员菜单下拉项,nav=顶栏菜单项,button=页面按钮</Comment>
      <DefaultExpression>&apos;menu&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>enum(&apos;route&apos;, &apos;menu_dir&apos;, &apos;menu&apos;, &apos;nav_user_menu&apos;, &apos;nav&apos;, &apos;button&apos;)|0e</StoredType>
    </column>
    <column id="1932" parent="397" name="title">
      <Comment>标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1933" parent="397" name="name">
      <Comment>规则名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1934" parent="397" name="path">
      <Comment>路由路径</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1935" parent="397" name="icon">
      <Comment>图标</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1936" parent="397" name="menu_type">
      <Comment>菜单类型:tab=选项卡,link=链接,iframe=Iframe</Comment>
      <DefaultExpression>&apos;tab&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>enum(&apos;tab&apos;, &apos;link&apos;, &apos;iframe&apos;)|0e</StoredType>
    </column>
    <column id="1937" parent="397" name="url">
      <Comment>Url</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1938" parent="397" name="component">
      <Comment>组件路径</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1939" parent="397" name="no_login_valid">
      <Comment>未登录有效:0=否,1=是</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint unsigned|0s</StoredType>
    </column>
    <column id="1940" parent="397" name="extend">
      <Comment>扩展属性:none=无,add_rules_only=只添加为路由,add_menu_only=只添加为菜单</Comment>
      <DefaultExpression>&apos;none&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>enum(&apos;none&apos;, &apos;add_rules_only&apos;, &apos;add_menu_only&apos;)|0e</StoredType>
    </column>
    <column id="1941" parent="397" name="remark">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1942" parent="397" name="weigh">
      <Comment>权重</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1943" parent="397" name="status">
      <Comment>状态:0=禁用,1=启用</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="1944" parent="397" name="update_time">
      <Comment>更新时间</Comment>
      <Position>16</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1945" parent="397" name="create_time">
      <Comment>创建时间</Comment>
      <Position>17</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1946" parent="397" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1947" parent="397" name="pid">
      <ColNames>pid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1948" parent="397" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1949" parent="398" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1950" parent="398" name="user_id">
      <Comment>会员ID</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1951" parent="398" name="pid">
      <Comment>来源用户</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1952" parent="398" name="consume_id">
      <Comment>来源id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1953" parent="398" name="score">
      <Comment>变更积分</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1954" parent="398" name="before">
      <Comment>变更前积分</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1955" parent="398" name="after">
      <Comment>变更后积分</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1956" parent="398" name="type">
      <Comment>类型:1=兑换扣除,2=邀请增加,3=系统增加,4=系统扣除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="1957" parent="398" name="memo">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1958" parent="398" name="create_time">
      <Comment>创建时间</Comment>
      <Position>10</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1959" parent="398" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1960" parent="398" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1961" parent="399" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1962" parent="399" name="keyword">
      <Comment>关键词</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1963" parent="399" name="type">
      <Comment>类型：follow=关注回复；keyword=关键词回复；invalidword=无效词回复</Comment>
      <DefaultExpression>&apos;keyword&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>enum(&apos;follow&apos;, &apos;keyword&apos;, &apos;default&apos;)|0e</StoredType>
    </column>
    <column id="1964" parent="399" name="msg_type">
      <Comment>消息类型：text=文本；image=图片；voice=语音；news=图文</Comment>
      <DefaultExpression>&apos;text&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>enum(&apos;text&apos;, &apos;image&apos;, &apos;voice&apos;, &apos;video&apos;, &apos;news&apos;)|0e</StoredType>
    </column>
    <column id="1965" parent="399" name="status">
      <Comment>状态:0=禁用;1=启用</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>enum(&apos;0&apos;, &apos;1&apos;)|0e</StoredType>
    </column>
    <column id="1966" parent="399" name="reply_content">
      <Comment>回复内容</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1967" parent="399" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1968" parent="399" name="update_time">
      <Comment>更新时间</Comment>
      <Position>8</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1969" parent="399" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1970" parent="399" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1971" parent="400" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1972" parent="400" name="event_key">
      <Comment>事件Key</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1973" parent="400" name="type">
      <Comment>类型</Comment>
      <DefaultExpression>&apos;text&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>enum(&apos;image&apos;, &apos;video&apos;, &apos;voice&apos;, &apos;news&apos;, &apos;text&apos;)|0e</StoredType>
    </column>
    <column id="1974" parent="400" name="content">
      <Comment>内容</Comment>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1975" parent="400" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1976" parent="400" name="update_time">
      <Comment>更新时间</Comment>
      <Position>6</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1977" parent="400" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1978" parent="400" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1979" parent="401" name="id">
      <AutoIncrement>13</AutoIncrement>
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1980" parent="401" name="staff_id">
      <Comment>所属员工</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1981" parent="401" name="type">
      <Comment>类型:1=微信,2=小红书,3=抖音,4=业绩</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>enum(&apos;1&apos;, &apos;2&apos;, &apos;3&apos;, &apos;4&apos;)|0e</StoredType>
    </column>
    <column id="1982" parent="401" name="new_additions">
      <Comment>新增数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1983" parent="401" name="chat">
      <Comment>聊天数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1984" parent="401" name="make_an_appointment">
      <Comment>预约体验人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1985" parent="401" name="release">
      <Comment>发布数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1986" parent="401" name="interaction">
      <Comment>互动数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1987" parent="401" name="transaction_volume">
      <Comment>成交单量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1988" parent="401" name="transaction_amount">
      <Comment>成交额</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>decimal(12,2 digit)|0s</StoredType>
    </column>
    <column id="1989" parent="401" name="statement_date">
      <Comment>报表日期</Comment>
      <Position>11</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1990" parent="401" name="create_time">
      <Comment>创建时间</Comment>
      <Position>12</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="1991" parent="401" name="update_time">
      <Comment>修改时间</Comment>
      <Position>13</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <index id="1992" parent="401" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1993" parent="401" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1994" parent="402" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="1995" parent="402" name="store_id">
      <Comment>所属门店</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="1996" parent="402" name="date">
      <Comment>打卡日期</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="1997" parent="402" name="week">
      <Comment>星期:1=周一,2=周二,3=周三,4=周四,5=周五,6=周六,7=周日</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1998" parent="402" name="work_hours">
      <Comment>上班时间</Comment>
      <Position>5</Position>
      <StoredType>time|0s</StoredType>
    </column>
    <column id="1999" parent="402" name="off_work_time">
      <Comment>下班时间</Comment>
      <Position>6</Position>
      <StoredType>time|0s</StoredType>
    </column>
    <column id="2000" parent="402" name="staff_ids">
      <Comment>打卡员工</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="2001" parent="402" name="month">
      <Comment>月份</Comment>
      <Position>8</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="2002" parent="402" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="2003" parent="402" name="update_time">
      <Comment>编辑时间</Comment>
      <Position>10</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="2004" parent="402" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="2005" parent="402" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2006" parent="403" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>点赞ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2007" parent="403" name="comment_id">
      <Comment>评论ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2008" parent="403" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2009" parent="403" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2010" parent="403" name="is_delete">
      <Comment>是否删除，0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="2011" parent="403" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2012" parent="403" name="uk_comment_user">
      <ColNames>comment_id
user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2013" parent="403" name="idx_user">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2014" parent="403" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2015" parent="403" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2016" parent="403" name="uk_comment_user">
      <UnderlyingIndexName>uk_comment_user</UnderlyingIndexName>
    </key>
    <column id="2017" parent="404" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>回复ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2018" parent="404" name="comment_id">
      <Comment>评论ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2019" parent="404" name="user_id">
      <Comment>回复用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2020" parent="404" name="content">
      <Comment>回复内容</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="2021" parent="404" name="reply_to_id">
      <Comment>被回复的用户ID，如果直接回复评论则为NULL</Comment>
      <Position>5</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2022" parent="404" name="likes">
      <Comment>点赞数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2023" parent="404" name="nickname">
      <Comment>用户昵称</Comment>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="2024" parent="404" name="avatar">
      <Comment>用户头像URL</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="2025" parent="404" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2026" parent="404" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2027" parent="404" name="is_delete">
      <Comment>是否删除，0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="2028" parent="404" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2029" parent="404" name="idx_comment">
      <ColNames>comment_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2030" parent="404" name="idx_user">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2031" parent="404" name="idx_reply_to">
      <ColNames>reply_to_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2032" parent="404" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2033" parent="404" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2034" parent="405" name="id">
      <AutoIncrement>11</AutoIncrement>
      <Comment>评论ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2035" parent="405" name="content_id">
      <Comment>内容ID，如视频ID、文章ID等</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="2036" parent="405" name="user_id">
      <Comment>评论用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2037" parent="405" name="content">
      <Comment>评论内容</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="2038" parent="405" name="likes">
      <Comment>点赞数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2039" parent="405" name="reply_count">
      <Comment>回复数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2040" parent="405" name="nickname">
      <Comment>用户昵称</Comment>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="2041" parent="405" name="avatar">
      <Comment>用户头像URL</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="2042" parent="405" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2043" parent="405" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2044" parent="405" name="is_delete">
      <Comment>是否删除，0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2045" parent="405" name="store_id">
      <Comment>店铺ID</Comment>
      <Position>12</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2046" parent="405" name="topic_id">
      <Comment>话题ID</Comment>
      <Position>13</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2047" parent="405" name="post_id">
      <Comment>帖子ID</Comment>
      <Position>14</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="2048" parent="405" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2049" parent="405" name="idx_content">
      <ColNames>content_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2050" parent="405" name="idx_user">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2051" parent="405" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2052" parent="405" name="idx_topic_id">
      <ColNames>topic_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2053" parent="405" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2054" parent="406" name="id">
      <AutoIncrement>49</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2055" parent="406" name="line_name">
      <Comment>地铁线路名称（唯一）</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="2056" parent="406" name="stations">
      <Comment>该线路的所有站点（JSON数组）</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="2057" parent="406" name="vote_counts">
      <Comment>各站点的投票数（JSON对象）</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="2058" parent="406" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="2059" parent="406" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="2060" parent="406" name="is_delete">
      <Comment>逻辑删除（0：未删除，1：已删除）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="2061" parent="406" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2062" parent="406" name="line_name">
      <ColNames>line_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="2063" parent="406" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2064" parent="406" name="line_name">
      <UnderlyingIndexName>line_name</UnderlyingIndexName>
    </key>
    <column id="2065" parent="407" name="id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>通知ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2066" parent="407" name="user_id">
      <Comment>接收通知的用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2067" parent="407" name="sender_id">
      <Comment>发送通知的用户ID</Comment>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2068" parent="407" name="type">
      <Comment>通知类型：1-点赞，2-评论，3-关注，4-系统通知</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2069" parent="407" name="title">
      <Comment>通知标题</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="2070" parent="407" name="content">
      <Comment>通知内容</Comment>
      <Position>6</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="2071" parent="407" name="related_id">
      <Comment>相关ID（帖子ID、评论ID等）</Comment>
      <Position>7</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2072" parent="407" name="related_type">
      <Comment>相关类型（post、comment等）</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="2073" parent="407" name="is_read">
      <Comment>是否已读：0-未读，1-已读</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2074" parent="407" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2075" parent="407" name="read_time">
      <Comment>阅读时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2076" parent="407" name="is_delete">
      <Comment>是否删除：0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="2077" parent="407" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2078" parent="407" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2079" parent="407" name="idx_sender_id">
      <ColNames>sender_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2080" parent="407" name="idx_type">
      <ColNames>type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2081" parent="407" name="idx_related">
      <ColNames>related_type
related_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2082" parent="407" name="idx_is_read">
      <ColNames>is_read</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2083" parent="407" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2084" parent="407" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2085" parent="408" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>收藏ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2086" parent="408" name="post_id">
      <Comment>帖子ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2087" parent="408" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2088" parent="408" name="create_time">
      <Comment>收藏时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2089" parent="408" name="is_delete">
      <Comment>是否删除：0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="2090" parent="408" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2091" parent="408" name="uk_post_user_favorite">
      <ColNames>post_id
user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2092" parent="408" name="idx_post_id">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2093" parent="408" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2094" parent="408" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2095" parent="408" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2096" parent="408" name="uk_post_user_favorite">
      <UnderlyingIndexName>uk_post_user_favorite</UnderlyingIndexName>
    </key>
    <column id="2097" parent="409" name="id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>点赞ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2098" parent="409" name="post_id">
      <Comment>帖子ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2099" parent="409" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2100" parent="409" name="create_time">
      <Comment>点赞时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2101" parent="409" name="is_delete">
      <Comment>是否删除：0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="2102" parent="409" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2103" parent="409" name="uk_post_user_like">
      <ColNames>post_id
user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2104" parent="409" name="idx_post_id">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2105" parent="409" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2106" parent="409" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2107" parent="409" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2108" parent="409" name="uk_post_user_like">
      <UnderlyingIndexName>uk_post_user_like</UnderlyingIndexName>
    </key>
    <trigger id="2109" parent="409" name="tr_post_like_insert">
      <Definer>admin_foxdance_c@%</Definer>
      <Events>I</Events>
      <SourceTextLength>620</SourceTextLength>
    </trigger>
    <trigger id="2110" parent="409" name="tr_post_like_update">
      <Definer>admin_foxdance_c@%</Definer>
      <Events>U</Events>
      <SourceTextLength>1133</SourceTextLength>
    </trigger>
    <column id="2111" parent="410" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>分享ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2112" parent="410" name="post_id">
      <Comment>帖子ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2113" parent="410" name="user_id">
      <Comment>分享用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2114" parent="410" name="share_type">
      <Comment>分享类型：1-微信好友，2-朋友圈，3-复制链接</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2115" parent="410" name="create_time">
      <Comment>分享时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="2116" parent="410" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2117" parent="410" name="idx_post_id">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2118" parent="410" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2119" parent="410" name="idx_share_type">
      <ColNames>share_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2120" parent="410" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2121" parent="410" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2122" parent="411" name="post_id">
      <Comment>帖子ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2123" parent="411" name="like_count">
      <Comment>点赞数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2124" parent="411" name="comment_count">
      <Comment>评论数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2125" parent="411" name="share_count">
      <Comment>分享数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2126" parent="411" name="view_count">
      <Comment>浏览数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2127" parent="411" name="last_activity_time">
      <Comment>最后活跃时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2128" parent="411" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <foreign-key id="2129" parent="411" name="post_stats_ibfk_1">
      <ColNames>post_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>posts</RefTableName>
    </foreign-key>
    <index id="2130" parent="411" name="PRIMARY">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2131" parent="411" name="idx_like_count">
      <ColNames>like_count</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2132" parent="411" name="idx_comment_count">
      <ColNames>comment_count</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2133" parent="411" name="idx_last_activity">
      <ColNames>last_activity_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2134" parent="411" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2135" parent="412" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>关联ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2136" parent="412" name="post_id">
      <Comment>帖子ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2137" parent="412" name="tag_id">
      <Comment>标签ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2138" parent="412" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="2139" parent="412" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2140" parent="412" name="uk_post_tag">
      <ColNames>post_id
tag_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2141" parent="412" name="idx_post_id">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2142" parent="412" name="idx_tag_id">
      <ColNames>tag_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2143" parent="412" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2144" parent="412" name="uk_post_tag">
      <UnderlyingIndexName>uk_post_tag</UnderlyingIndexName>
    </key>
    <column id="2145" parent="413" name="id">
      <AutoIncrement>43</AutoIncrement>
      <Comment>帖子ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2146" parent="413" name="user_id">
      <Comment>发布用户ID，关联ba_user表</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2147" parent="413" name="content">
      <Comment>帖子内容</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="2148" parent="413" name="title">
      <Comment>帖子标题，用于搜索和展示</Comment>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="2149" parent="413" name="images">
      <Comment>帖子图片数组，存储图片URL列表</Comment>
      <Position>5</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="2150" parent="413" name="cover_image">
      <Comment>封面图片URL，用于搜索结果和列表展示</Comment>
      <Position>6</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="2151" parent="413" name="location_name">
      <Comment>位置名称</Comment>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="2152" parent="413" name="location_latitude">
      <Comment>纬度</Comment>
      <Position>8</Position>
      <StoredType>decimal(10,8 digit)|0s</StoredType>
    </column>
    <column id="2153" parent="413" name="location_longitude">
      <Comment>经度</Comment>
      <Position>9</Position>
      <StoredType>decimal(11,8 digit)|0s</StoredType>
    </column>
    <column id="2154" parent="413" name="location_address">
      <Comment>详细地址</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="2155" parent="413" name="like_count">
      <Comment>点赞数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2156" parent="413" name="comment_count">
      <Comment>评论数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2157" parent="413" name="share_count">
      <Comment>分享数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2158" parent="413" name="view_count">
      <Comment>浏览数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2159" parent="413" name="is_public">
      <Comment>是否公开：0-私密，1-公开</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2160" parent="413" name="status">
      <Comment>状态：0-草稿，1-已发布，2-已删除</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2161" parent="413" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2162" parent="413" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2163" parent="413" name="is_delete">
      <Comment>是否删除：0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2164" parent="413" name="tags">
      <Comment>标签列表，多个标签用逗号分隔</Comment>
      <Position>20</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="2165" parent="413" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2166" parent="413" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2167" parent="413" name="idx_location">
      <ColNames>location_latitude
location_longitude</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2168" parent="413" name="idx_like_count">
      <ColNames>like_count</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2169" parent="413" name="idx_is_public">
      <ColNames>is_public</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2170" parent="413" name="idx_composite_list">
      <ColNames>status
is_public
create_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2171" parent="413" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2172" parent="413" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2173" parent="413" name="idx_posts_tags">
      <ColNames>tags</ColNames>
      <PrefixLengths>255</PrefixLengths>
      <Type>btree</Type>
    </index>
    <key id="2174" parent="413" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <trigger id="2175" parent="413" name="tr_post_insert">
      <Definer>admin_foxdance_c@%</Definer>
      <Events>I</Events>
      <SourceTextLength>422</SourceTextLength>
    </trigger>
    <column id="2176" parent="414" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>会话ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2177" parent="414" name="user1_id">
      <Comment>用户1ID（较小的用户ID）</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2178" parent="414" name="user2_id">
      <Comment>用户2ID（较大的用户ID）</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2179" parent="414" name="last_message_id">
      <Comment>最后一条消息ID</Comment>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2180" parent="414" name="last_message_time">
      <Comment>最后消息时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2181" parent="414" name="user1_unread_count">
      <Comment>用户1未读消息数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2182" parent="414" name="user2_unread_count">
      <Comment>用户2未读消息数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2183" parent="414" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2184" parent="414" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2185" parent="414" name="is_delete">
      <Comment>是否删除：0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="2186" parent="414" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2187" parent="414" name="uk_conversation">
      <ColNames>user1_id
user2_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2188" parent="414" name="idx_user1">
      <ColNames>user1_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2189" parent="414" name="idx_user2">
      <ColNames>user2_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2190" parent="414" name="idx_last_message_time">
      <ColNames>last_message_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2191" parent="414" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2192" parent="414" name="uk_conversation">
      <UnderlyingIndexName>uk_conversation</UnderlyingIndexName>
    </key>
    <column id="2193" parent="415" name="id">
      <AutoIncrement>11</AutoIncrement>
      <Comment>消息ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2194" parent="415" name="conversation_id">
      <Comment>会话ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2195" parent="415" name="sender_id">
      <Comment>发送者用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2196" parent="415" name="receiver_id">
      <Comment>接收者用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2197" parent="415" name="message_type">
      <Comment>消息类型：1-文本，2-图片，3-语音，4-视频</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2198" parent="415" name="content">
      <Comment>消息内容</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="2199" parent="415" name="media_url">
      <Comment>媒体文件URL（图片、语音、视频）</Comment>
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="2200" parent="415" name="is_read">
      <Comment>是否已读：0-未读，1-已读</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2201" parent="415" name="read_time">
      <Comment>阅读时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2202" parent="415" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2203" parent="415" name="is_delete">
      <Comment>是否删除：0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <foreign-key id="2204" parent="415" name="private_messages_ibfk_1">
      <ColNames>conversation_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>private_conversations</RefTableName>
    </foreign-key>
    <index id="2205" parent="415" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2206" parent="415" name="idx_conversation_id">
      <ColNames>conversation_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2207" parent="415" name="idx_sender_id">
      <ColNames>sender_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2208" parent="415" name="idx_receiver_id">
      <ColNames>receiver_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2209" parent="415" name="idx_is_read">
      <ColNames>is_read</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2210" parent="415" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2211" parent="415" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2212" parent="416" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>点赞ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2213" parent="416" name="reply_id">
      <Comment>回复ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2214" parent="416" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2215" parent="416" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2216" parent="416" name="is_delete">
      <Comment>是否删除，0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="2217" parent="416" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2218" parent="416" name="uk_reply_user">
      <ColNames>reply_id
user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2219" parent="416" name="idx_user">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2220" parent="416" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2221" parent="416" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2222" parent="416" name="uk_reply_user">
      <UnderlyingIndexName>uk_reply_user</UnderlyingIndexName>
    </key>
    <column id="2223" parent="417" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>举报ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2224" parent="417" name="reporter_id">
      <Comment>举报人用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2225" parent="417" name="reported_user_id">
      <Comment>被举报用户ID</Comment>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2226" parent="417" name="target_type">
      <Comment>举报目标类型：post、comment、user</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="2227" parent="417" name="target_id">
      <Comment>举报目标ID</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2228" parent="417" name="reason">
      <Comment>举报原因：1-垃圾信息，2-违法违规，3-色情内容，4-其他</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2229" parent="417" name="description">
      <Comment>举报描述</Comment>
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="2230" parent="417" name="status">
      <Comment>处理状态：0-待处理，1-已处理，2-已忽略</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2231" parent="417" name="handler_id">
      <Comment>处理人ID</Comment>
      <Position>9</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2232" parent="417" name="handle_time">
      <Comment>处理时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2233" parent="417" name="handle_result">
      <Comment>处理结果</Comment>
      <Position>11</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="2234" parent="417" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="2235" parent="417" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2236" parent="417" name="idx_reporter_id">
      <ColNames>reporter_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2237" parent="417" name="idx_reported_user_id">
      <ColNames>reported_user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2238" parent="417" name="idx_target">
      <ColNames>target_type
target_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2239" parent="417" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2240" parent="417" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2241" parent="417" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2242" parent="418" name="id">
      <AutoIncrement>8</AutoIncrement>
      <Comment>配置ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2243" parent="418" name="config_key">
      <Comment>配置键</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="2244" parent="418" name="config_value">
      <Comment>配置值</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="2245" parent="418" name="config_type">
      <Comment>配置类型：string、number、boolean、json</Comment>
      <DefaultExpression>&apos;string&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="2246" parent="418" name="description">
      <Comment>配置描述</Comment>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="2247" parent="418" name="is_public">
      <Comment>是否公开：0-私有，1-公开</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2248" parent="418" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2249" parent="418" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="2250" parent="418" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2251" parent="418" name="uk_config_key">
      <ColNames>config_key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2252" parent="418" name="idx_is_public">
      <ColNames>is_public</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2253" parent="418" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2254" parent="418" name="uk_config_key">
      <UnderlyingIndexName>uk_config_key</UnderlyingIndexName>
    </key>
    <column id="2255" parent="419" name="id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>标签ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2256" parent="419" name="name">
      <Comment>标签名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="2257" parent="419" name="description">
      <Comment>标签描述</Comment>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="2258" parent="419" name="cover_image">
      <Comment>封面图URL</Comment>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="2259" parent="419" name="color">
      <Comment>标签颜色（十六进制）</Comment>
      <DefaultExpression>&apos;#1890ff&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(7)|0s</StoredType>
    </column>
    <column id="2260" parent="419" name="use_count">
      <Comment>使用次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2261" parent="419" name="is_hot">
      <Comment>是否热门标签：0-否，1-是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2262" parent="419" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2263" parent="419" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2264" parent="419" name="is_delete">
      <Comment>是否删除：0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="2265" parent="419" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2266" parent="419" name="uk_tag_name">
      <ColNames>name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2267" parent="419" name="idx_use_count">
      <ColNames>use_count</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2268" parent="419" name="idx_is_hot">
      <ColNames>is_hot</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2269" parent="419" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2270" parent="419" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2271" parent="419" name="uk_tag_name">
      <UnderlyingIndexName>uk_tag_name</UnderlyingIndexName>
    </key>
    <column id="2272" parent="420" name="id">
      <AutoIncrement>12</AutoIncrement>
      <Comment>话题ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2273" parent="420" name="title">
      <Comment>话题标题</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="2274" parent="420" name="description">
      <Comment>话题描述</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="2275" parent="420" name="user_id">
      <Comment>创建用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2276" parent="420" name="comment_user_count">
      <Comment>评论人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2277" parent="420" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2278" parent="420" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2279" parent="420" name="is_delete">
      <Comment>是否删除，0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2280" parent="420" name="cover_image">
      <Comment>话题封面图URL</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="2281" parent="420" name="topic_images">
      <Comment>话题图片数组</Comment>
      <Position>10</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <index id="2282" parent="420" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2283" parent="420" name="idx_user">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2284" parent="420" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2285" parent="420" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2286" parent="421" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>关注ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2287" parent="421" name="follower_id">
      <Comment>关注者用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2288" parent="421" name="following_id">
      <Comment>被关注者用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2289" parent="421" name="status">
      <Comment>关注状态：0-已取消，1-已关注</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>4</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="2290" parent="421" name="create_time">
      <Comment>关注时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2291" parent="421" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2292" parent="421" name="is_delete">
      <Comment>是否删除：0-未删除，1-已删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="2293" parent="421" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2294" parent="421" name="uk_follow_relation">
      <ColNames>follower_id
following_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2295" parent="421" name="idx_follower">
      <ColNames>follower_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2296" parent="421" name="idx_following">
      <ColNames>following_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2297" parent="421" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2298" parent="421" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2299" parent="421" name="uk_follow_relation">
      <UnderlyingIndexName>uk_follow_relation</UnderlyingIndexName>
    </key>
    <trigger id="2300" parent="421" name="tr_user_follow_insert">
      <Definer>admin_foxdance_c@%</Definer>
      <Events>I</Events>
      <SourceTextLength>423</SourceTextLength>
    </trigger>
    <trigger id="2301" parent="421" name="tr_user_follow_update">
      <Definer>admin_foxdance_c@%</Definer>
      <Events>U</Events>
      <SourceTextLength>846</SourceTextLength>
    </trigger>
    <column id="2302" parent="422" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2303" parent="422" name="following_count">
      <Comment>关注数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2304" parent="422" name="follower_count">
      <Comment>粉丝数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2305" parent="422" name="post_count">
      <Comment>帖子数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2306" parent="422" name="like_received_count">
      <Comment>收到的点赞数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2307" parent="422" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="2308" parent="422" name="PRIMARY">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2309" parent="422" name="idx_following_count">
      <ColNames>following_count</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2310" parent="422" name="idx_follower_count">
      <ColNames>follower_count</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2311" parent="422" name="idx_post_count">
      <ColNames>post_count</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2312" parent="422" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2313" parent="423" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2314" parent="423" name="title">
      <Comment>标题</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="2315" parent="423" name="info">
      <Comment>信息</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="2316" parent="423" name="tips">
      <Comment>提示</Comment>
      <Position>4</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="2317" parent="423" name="createTime">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2318" parent="423" name="updateTime">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2319" parent="423" name="isDelete">
      <Comment>是否删除</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2320" parent="423" name="card_tip">
      <Comment>投票提示</Comment>
      <Position>8</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <index id="2321" parent="423" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="2322" parent="423" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2323" parent="424" name="id">
      <AutoIncrement>3741</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2324" parent="424" name="user_id">
      <Comment>用户ID</Comment>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2325" parent="424" name="open_id">
      <Comment>微信OpenID</Comment>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="2326" parent="424" name="ip_address">
      <Comment>IP地址</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="2327" parent="424" name="user_agent">
      <Comment>浏览器UA信息</Comment>
      <Position>5</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="2328" parent="424" name="metro_line_id">
      <Comment>地铁线路ID</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2329" parent="424" name="station_name">
      <Comment>站点名称</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="2330" parent="424" name="is_suspicious">
      <Comment>是否可疑投票 0-否 1-是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2331" parent="424" name="create_time">
      <Comment>投票时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="2332" parent="424" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2333" parent="424" name="idx_user_station_time">
      <ColNames>user_id
metro_line_id
station_name
create_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2334" parent="424" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2335" parent="424" name="idx_open_id">
      <ColNames>open_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2336" parent="424" name="idx_ip_station_time">
      <ColNames>ip_address
metro_line_id
station_name
create_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2337" parent="424" name="idx_ip_address">
      <ColNames>ip_address</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2338" parent="424" name="idx_metro_station">
      <ColNames>metro_line_id
station_name</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2339" parent="424" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2340" parent="425" name="id">
      <Comment>帖子ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2341" parent="425" name="user_id">
      <Comment>发布用户ID，关联ba_user表</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2342" parent="425" name="content">
      <Comment>帖子内容</Comment>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="2343" parent="425" name="images">
      <Comment>帖子图片数组，存储图片URL列表</Comment>
      <Position>4</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="2344" parent="425" name="location_name">
      <Comment>位置名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="2345" parent="425" name="location_latitude">
      <Comment>纬度</Comment>
      <Position>6</Position>
      <StoredType>decimal(10,8 digit)|0s</StoredType>
    </column>
    <column id="2346" parent="425" name="location_longitude">
      <Comment>经度</Comment>
      <Position>7</Position>
      <StoredType>decimal(11,8 digit)|0s</StoredType>
    </column>
    <column id="2347" parent="425" name="location_address">
      <Comment>详细地址</Comment>
      <Position>8</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="2348" parent="425" name="is_public">
      <Comment>是否公开：0-私密，1-公开</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2349" parent="425" name="status">
      <Comment>状态：0-草稿，1-已发布，2-已删除</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="2350" parent="425" name="create_time">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2351" parent="425" name="update_time">
      <Comment>更新时间</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2352" parent="425" name="nickname">
      <Comment>昵称</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="2353" parent="425" name="avatar">
      <Comment>头像</Comment>
      <Position>14</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="2354" parent="425" name="level">
      <Comment>当前等级</Comment>
      <Position>15</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2355" parent="425" name="like_count">
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2356" parent="425" name="comment_count">
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2357" parent="425" name="share_count">
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2358" parent="425" name="view_count">
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2359" parent="425" name="last_activity_time">
      <Comment>最后活跃时间</Comment>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="2360" parent="426" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="2361" parent="426" name="nickname">
      <Comment>昵称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="2362" parent="426" name="avatar">
      <Comment>头像</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="2363" parent="426" name="level">
      <Comment>当前等级</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="2364" parent="426" name="following_count">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2365" parent="426" name="follower_count">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2366" parent="426" name="post_count">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="2367" parent="426" name="like_received_count">
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
  </database-model>
</dataSource>