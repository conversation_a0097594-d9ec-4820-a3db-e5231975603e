{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/coursePackage/teacherDetails.vue?4cc4", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/coursePackage/teacherDetails.vue?768b", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/coursePackage/teacherDetails.vue?e4b5", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/coursePackage/teacherDetails.vue?d4ed", "uni-app:///pages/buy/coursePackage/teacherDetails.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/coursePackage/teacherDetails.vue?9a68", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/coursePackage/teacherDetails.vue?4dfa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loding", "safeAreaTop", "menuButtonInfoHeight", "isLogined", "navBg", "coursePackageLists", "page", "total_pages", "zanwsj", "status", "loadingText", "loadmoreText", "nomoreText", "onLoad", "onShow", "onPageScroll", "scrollTop", "e", "methods", "coursePackageData", "uni", "title", "size", "id", "console", "that", "onReachBottom", "onPullDownRefresh", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAuvB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACgE3wB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IAAA,oDACA,sDACA,wDACA,wDACA,uDACA,qDACA;EAEA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA,IACAC,YACAC,EADAD;IAEA;IACA;EACA;EACAE;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QACAf;QACAgB;QACAC;MACA;QACAC;QACA;UACA;UACAC;UACAA;UACA;UACAA;UACAA;UACAA;UACA;UACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UACA;YACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAA;UACAL;UACAA;QACA;MACA;IAEA;IACAM;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACAR;QACAS;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChLA;AAAA;AAAA;AAAA;AAAk5C,CAAgB,uvCAAG,EAAC,C;;;;;;;;;;;ACAt6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/buy/coursePackage/teacherDetails.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/buy/coursePackage/teacherDetails.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./teacherDetails.vue?vue&type=template&id=2f90a1c6&\"\nvar renderjs\nimport script from \"./teacherDetails.vue?vue&type=script&lang=js&\"\nexport * from \"./teacherDetails.vue?vue&type=script&lang=js&\"\nimport style0 from \"./teacherDetails.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/buy/coursePackage/teacherDetails.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetails.vue?vue&type=template&id=2f90a1c6&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-navbar/u-navbar\" */ \"@/components/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var a0 = _vm.loding\n    ? {\n        background: \"rgba(26,26, 26,\" + _vm.navBg + \")\",\n      }\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        a0: a0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetails.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetails.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"teacherDetails\" :style=\"{ '--qjbutton-color': qjbutton }\" v-if=\"loding\">\r\n\t\t\r\n\t\t<u-navbar title=\"讲师详情\" :back-icon-name=\"'arrow-left'\" back-icon-color=\"#ffffff\" :background=\"{ background: 'rgba(26,26, 26,' + navBg + ')' }\"\r\n\t\t\t:border-bottom=\"false\" :title-color=\"navBg==1?'#fff':'#fff' \" title-size=\"32\"></u-navbar>\r\n\t\t\r\n\t\t<view class=\"lsxq_ban\" :style=\"'margin-top:-'+(safeAreaTop+20+menuButtonInfoHeight)+'px;'\"><image :src=\"imgbaseUrl + teacherImage\" mode=\"aspectFill\"></image></view>\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"lsxq_title\"><text>{{teacherName}}</text></view>\r\n\t\t<view class=\"lsxq_two\">\r\n\t\t\t<!-- <view class=\"cour_two\">\r\n\t\t\t\t<view class=\"cour_two_li\" v-for=\"(item,index) in 6\" :key=\"index\" @click=\"navTo('/pages/buy/coursePackage/myCoursexq?id=' + item.id)\">\r\n\t\t\t\t\t<view class=\"cour_two_li_l\"><image src=\"/static/images/icon23.jpg\" mode=\"aspectFill\"></image></view>\r\n\t\t\t\t\t<view class=\"cour_two_li_r\">\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_a\">拉丁舞线上课拉丁舞线上课拉丁舞线上</view>\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_b\">基础/拉丁舞</view>\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_c\">课程时长：60分钟</view>\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_d\">讲师:LINDA</view>\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_e\">\r\n\t\t\t\t\t\t\t<view class=\"cour_two_li_r_e_l\">已售1521<text>¥999</text></view>\r\n\t\t\t\t\t\t\t<view class=\"cour_two_li_r_e_r\">详情</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"cour_two\">\r\n\t\t\t\t<view class=\"cour_two_li\" v-for=\"(item,index) in coursePackageLists\" :key=\"index\" @click=\"navTo('/pages/buy/coursePackage/myCoursexq?id=' + item.id)\">\r\n\t\t\t\t\t<view class=\"cour_two_li_l\"><image :src=\"imgbaseUrl + item.image\" mode=\"aspectFill\"></image></view>\r\n\t\t\t\t\t<view class=\"cour_two_li_r\">\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_a\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_b\">{{item.levelTable.name}}/{{item.danceTable.name}}</view>\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_c\">课程时长：{{item.duration*1}}分钟</view>\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_d\">讲师:{{teacherName}}</view>\r\n\t\t\t\t\t\t<view class=\"cour_two_li_r_e\">\r\n\t\t\t\t\t\t\t<view class=\"cour_two_li_r_e_l\">已售{{item.sales_volume*1}}<text>¥{{item.price*1}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"cour_two_li_r_e_r\">详情</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"gg_loding\" v-if=\"!zanwsj\">\r\n\t\t\t\t\t<view class=\"loader-inner ball-clip-rotate\" v-if=\"status == 'loading'\">\r\n\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t<text>加载中</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gg_loding_wusj\" v-else>─── 没有更多数据了 ───</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"gg_zwsj\" v-if=\"zanwsj\">\r\n\t\t\t\t\t<view class=\"gg_zwsj_w\">\r\n\t\t\t\t\t\t<image src=\"/static/images/wusj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t<text>暂无数据</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tteacherDetailApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloding:false,\r\n\t\t\tsafeAreaTop:wx.getWindowInfo().safeArea.top,\r\n\t\t\tmenuButtonInfoHeight:uni.getMenuButtonBoundingClientRect().height,\r\n\t\t\tisLogined:true,\r\n\t\t\tnavBg: '',\r\n\t\t\tcoursePackageLists:[],\r\n\t\t\tpage:1,//当前页数\r\n\t\t\ttotal_pages: 1,//总页数\r\n\t\t\tzanwsj:false,//是否有数据\r\n\t\t\tstatus: 'loading',//底部loding是否显示\r\n\t\t\tloadingText: '努力加载中',\r\n\t\t\tloadmoreText: '轻轻上拉',\r\n\t\t\tnomoreText: '实在没有了',\r\n\t\t\tisLogined:false,\r\n\t\t\tpageId:0,\r\n\t\t\tteacherImage:'',\r\n\t\t\tteacherName:'',\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.pageId = option.id;\r\n\t},\r\n\tonShow() {\r\n\t\tthis.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.page = 1;\r\n\t\tthis.coursePackageLists = [];\r\n\t\tthis.coursePackageData();//课包\r\n\t},\r\n\tonPageScroll(e) {\r\n\t\tconst top = uni.upx2px(100)\r\n\t\tconst {\r\n\t\t\tscrollTop\r\n\t\t} = e\r\n\t\tlet percent = scrollTop / top > 1 ? 1 : scrollTop / top\r\n\t\tthis.navBg = percent\r\n\t},\r\n\tmethods: {\r\n\t\t//课包列表\r\n\t\tcoursePackageData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tteacherDetailApi({\r\n\t\t\t\tpage:that.page,\r\n\t\t\t\tsize:10,\r\n\t\t\t\tid:that.pageId,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('课包列表',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t// res.data.package.data = []\r\n\t\t\t\t\tthat.teacherImage = res.data.image;\r\n\t\t\t\t\tthat.teacherName = res.data.name;\r\n\t\t\t\t\tvar obj = res.data.package.data;\r\n\t\t\t\t\tthat.coursePackageLists = that.coursePackageLists.concat(obj);\r\n\t\t\t\t\tthat.zanwsj = that.coursePackageLists.length ? true : false;\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t// that.total_pages = Math.ceil(res.total/20);\r\n\t\t\t\t\tthat.total_pages = res.data.package.last_page;\r\n\t\t\t\t\tif (that.page != 1) {\r\n\t\t\t\t\t\tif (that.total_pages >= that.page) {\r\n\t\t\t\t\t\t\tthat.status = 'loading'\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.coursePackageLists.length == 0){\r\n\t\t\t\t\t\tthat.zanwsj = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.zanwsj = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.data.total*1<=10){\r\n\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\t// console.log('到底了');\r\n\t\t\tif (this.page != 1) {\r\n\t\t\t\tif (this.total_pages >= this.page) {\r\n\t\t\t\t\tthis.coursePackageData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t    // console.log('我被下拉了');\r\n\t\t    this.page = 1;\r\n\t\t\tthis.coursePackageLists = [];\r\n\t\t\tthis.coursePackageData();//课包列表\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.teacherDetails{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetails.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetails.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753500075653\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}