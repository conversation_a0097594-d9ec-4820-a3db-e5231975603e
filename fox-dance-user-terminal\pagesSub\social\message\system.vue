<template>
  <view class="system-message-container">
    <!-- 一键已读按钮 -->
    <view class="read-all-section">
      <view class="read-all-btn" @click="markAllAsRead">
        <u-icon name="checkmark-circle" size="20" color="#2979ff"></u-icon>
        <text class="read-all-text">一键已读</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view 
      class="message-list"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view v-if="messageList && messageList.length > 0">
        <view
          v-for="message in messageList"
          :key="message.id"
          class="message-card"
          @click="openMessageDetail(message)"
        >
          <!-- 消息图标 -->
          <view class="message-icon" :class="'icon-' + message.type">
            <u-icon :name="getIconName(message.type)" size="20" color="#fff"></u-icon>
          </view>
          
          <!-- 消息内容 -->
          <view class="message-content">
            <view class="message-header">
              <text class="message-title">{{ message.title }}</text>
              <text class="message-time">{{ formatTime(message.createTime) }}</text>
            </view>
            <text class="message-desc">{{ message.content }}</text>
          </view>
          
          <!-- 未读标识 -->
          <view v-if="!message.isRead" class="unread-dot"></view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <image src="/static/images/empty-message.png" class="empty-image"></image>
        <text class="empty-text">暂无系统消息</text>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && messageList && messageList.length > 0" class="load-more">
        <u-loading mode="flower" size="24"></u-loading>
        <text class="load-text">加载中...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'SystemMessage',
  data() {
    return {
      isRefreshing: false,
      hasMore: true,
      page: 1,
      pageSize: 20,
      messageList: [
        {
          id: 1,
          type: 'system',
          title: '系统维护通知',
          content: '系统将于今晚23:00-次日1:00进行维护升级，期间可能影响部分功能使用，请您提前做好准备。',
          createTime: new Date(Date.now() - 3600000),
          isRead: false
        },
        {
          id: 2,
          type: 'activity',
          title: '舞蹈大赛开始报名',
          content: '2024年度舞蹈大赛正式开始报名，丰厚奖品等你来拿！报名截止时间：本月底。',
          createTime: new Date(Date.now() - 7200000),
          isRead: true
        },
        {
          id: 3,
          type: 'security',
          title: '账户安全提醒',
          content: '检测到您的账户在新设备上登录，如非本人操作，请及时修改密码并联系客服。',
          createTime: new Date(Date.now() - 86400000),
          isRead: false
        },
        {
          id: 4,
          type: 'system',
          title: '新功能上线',
          content: '视频剪辑功能已上线，快来体验全新的创作工具，让您的舞蹈视频更加精彩！',
          createTime: new Date(Date.now() - 172800000),
          isRead: true
        }
      ]
    }
  },
  onLoad() {
    this.loadMessages()
  },
  methods: {
    markAllAsRead() {
      const unreadCount = this.messageList.filter(msg => !msg.isRead).length
      if (unreadCount === 0) {
        this.$u.toast('暂无未读消息')
        return
      }

      uni.showModal({
        title: '确认操作',
        content: `确定要将所有${unreadCount}条未读消息标记为已读吗？`,
        success: (res) => {
          if (res.confirm) {
            this.messageList.forEach(msg => {
              msg.isRead = true
            })
            this.$u.toast('已全部标记为已读')
          }
        }
      })
    },

    getIconName(type) {
      const iconMap = {
        system: 'setting',
        activity: 'volume',
        security: 'shield'
      }
      return iconMap[type] || 'bell'
    },

    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    },

    openMessageDetail(message) {
      // 标记为已读
      message.isRead = true
      
      // 跳转到消息详情页
      uni.navigateTo({
        url: `/pagesSub/social/message/detail?id=${message.id}`
      })
    },

    onRefresh() {
      this.isRefreshing = true
      this.page = 1
      this.loadMessages().finally(() => {
        this.isRefreshing = false
      })
    },

    loadMore() {
      if (!this.hasMore) return
      this.page++
      this.loadMessages()
    },

    async loadMessages() {
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        if (this.page >= 3) {
          this.hasMore = false
        }
      } catch (error) {
        console.error('加载消息失败:', error)
        this.$u.toast('加载失败，请重试')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.system-message-container {
  //height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.read-all-section {
  background: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #e4e7ed;
}

.read-all-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 72rpx;
  background: #f8f9fa;
  border: 2rpx solid #e4e7ed;
  border-radius: 36rpx;
  transition: all 0.2s ease;
}

.read-all-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.read-all-text {
  font-size: 28rpx;
  color: #2979ff;
  margin-left: 12rpx;
  font-weight: 500;
}

.message-list {
  flex: 1;
  padding: 32rpx 0;
}

.message-card {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  margin: 0 32rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.message-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.icon-system {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-activity {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.icon-security {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.message-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

.message-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

.unread-dot {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.load-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}
</style>
