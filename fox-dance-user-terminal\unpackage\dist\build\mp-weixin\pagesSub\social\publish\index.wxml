<view class="publish-container data-v-254180e3"><scroll-view class="content data-v-254180e3" scroll-y="{{true}}"><view class="user-section data-v-254180e3"><u-avatar vue-id="b4098e7e-1" src="{{userInfo.avatar}}" size="40" class="data-v-254180e3" bind:__l="__l"></u-avatar><text class="username data-v-254180e3">{{userInfo.nickname}}</text></view><view class="title-section data-v-254180e3"><input class="title-input data-v-254180e3" placeholder="标题（可选）" maxlength="{{50}}" data-event-opts="{{[['input',[['__set_model',['','postTitle','$event',[]]]]]]}}" value="{{postTitle}}" bindinput="__e"/><view class="title-char-count data-v-254180e3">{{$root.g0+"/50"}}</view></view><view class="text-section data-v-254180e3"><textarea class="content-input data-v-254180e3" placeholder="分享你的生活..." maxlength="{{500}}" auto-height="{{true}}" show-confirm-bar="{{false}}" data-event-opts="{{[['input',[['__set_model',['','postContent','$event',[]]]]]]}}" value="{{postContent}}" bindinput="__e"></textarea><view class="char-count data-v-254180e3">{{$root.g1+"/500"}}</view></view><view class="image-section data-v-254180e3"><view class="image-grid data-v-254180e3"><block wx:for="{{selectedImages}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view class="image-item data-v-254180e3"><image class="uploaded-image data-v-254180e3" src="{{image}}" mode="aspectFill"></image><view data-event-opts="{{[['tap',[['removeImage',[index]]]]]}}" class="delete-btn data-v-254180e3" bindtap="__e"><u-icon vue-id="{{'b4098e7e-2-'+index}}" name="close" color="#fff" size="16" class="data-v-254180e3" bind:__l="__l"></u-icon></view></view></block><block wx:if="{{$root.g2<9}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="add-image-btn data-v-254180e3" bindtap="__e"><u-icon vue-id="b4098e7e-3" name="camera" color="#999" size="32" class="data-v-254180e3" bind:__l="__l"></u-icon><text class="add-text data-v-254180e3">添加图片</text></view></block></view></view><view class="options-section data-v-254180e3"><view data-event-opts="{{[['tap',[['selectTopic',['$event']]]]]}}" class="option-item data-v-254180e3" bindtap="__e"><view class="option-left data-v-254180e3"><u-icon vue-id="b4098e7e-4" name="tags" color="#2979ff" size="20" class="data-v-254180e3" bind:__l="__l"></u-icon><text class="option-text data-v-254180e3">添加话题</text></view><view class="option-right data-v-254180e3"><block wx:if="{{$root.g3}}"><text class="selected-topics data-v-254180e3">{{''+$root.g4+''}}</text></block><u-icon vue-id="b4098e7e-5" name="arrow-right" color="#999" size="16" class="data-v-254180e3" bind:__l="__l"></u-icon></view></view><view data-event-opts="{{[['tap',[['selectLocation',['$event']]]]]}}" class="option-item data-v-254180e3" bindtap="__e"><view class="option-left data-v-254180e3"><u-icon vue-id="b4098e7e-6" name="map" color="#2979ff" size="20" class="data-v-254180e3" bind:__l="__l"></u-icon><text class="option-text data-v-254180e3">添加位置</text></view><view class="option-right data-v-254180e3"><block wx:if="{{selectedLocation}}"><view class="location-selected data-v-254180e3"><view class="location-info-inline data-v-254180e3"><text class="selected-location data-v-254180e3">{{selectedLocation.name}}</text><text class="selected-address data-v-254180e3">{{selectedLocation.address}}</text></view><u-icon vue-id="b4098e7e-7" name="close-circle-fill" color="#999" size="18" data-event-opts="{{[['^click',[['clearLocation',['$event']]]]]}}" catch:click="__e" class="data-v-254180e3" bind:__l="__l"></u-icon></view></block><block wx:else><u-icon vue-id="b4098e7e-8" name="arrow-right" color="#999" size="16" class="data-v-254180e3" bind:__l="__l"></u-icon></block></view></view><view data-event-opts="{{[['tap',[['setVisibility',['$event']]]]]}}" class="option-item data-v-254180e3" bindtap="__e"><view class="option-left data-v-254180e3"><u-icon vue-id="b4098e7e-9" name="eye" color="#2979ff" size="20" class="data-v-254180e3" bind:__l="__l"></u-icon><text class="option-text data-v-254180e3">可见性</text></view><view class="option-right data-v-254180e3"><text class="visibility-text data-v-254180e3">{{visibilityText}}</text><u-icon vue-id="b4098e7e-10" name="arrow-right" color="#999" size="16" class="data-v-254180e3" bind:__l="__l"></u-icon></view></view></view><view class="tips-section data-v-254180e3"><text class="tips-text data-v-254180e3">发布即表示同意《社区公约》，请文明发言，共建和谐社区</text></view><view class="publish-section data-v-254180e3"><text data-event-opts="{{[['tap',[['publishPost',['$event']]]]]}}" class="{{['publish-btn','data-v-254180e3',(!canPublish)?'disabled':'']}}" bindtap="__e">{{''+(publishing?'发布中...':'发布')+''}}</text></view></scroll-view><u-popup bind:input="__e" vue-id="b4098e7e-11" mode="bottom" border-radius="20" value="{{showTopicModal}}" data-event-opts="{{[['^input',[['__set_model',['','showTopicModal','$event',[]]]]]]}}" class="data-v-254180e3" bind:__l="__l" vue-slots="{{['default']}}"><view class="topic-modal data-v-254180e3"><view class="modal-header data-v-254180e3"><text class="modal-title data-v-254180e3">选择话题</text><u-icon vue-id="{{('b4098e7e-12')+','+('b4098e7e-11')}}" name="close" data-event-opts="{{[['^click',[['e0']]]]}}" bind:click="__e" class="data-v-254180e3" bind:__l="__l"></u-icon></view><view class="topic-search data-v-254180e3"><u-input vue-id="{{('b4098e7e-13')+','+('b4098e7e-11')}}" placeholder="搜索话题" prefix-icon="search" value="{{topicKeyword}}" data-event-opts="{{[['^input',[['__set_model',['','topicKeyword','$event',[]]],['searchTopics']]]]}}" bind:input="__e" class="data-v-254180e3" bind:__l="__l"></u-input></view><view class="topic-list data-v-254180e3"><block wx:for="{{$root.l0}}" wx:for-item="topic" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['toggleTopic',['$0'],[[['filteredTopics','id',topic.$orig.id]]]]]]]}}" class="{{['topic-option','data-v-254180e3',(topic.g5)?'selected':'']}}" bindtap="__e"><text class="topic-name data-v-254180e3">{{"#"+topic.$orig.name}}</text><text class="topic-count data-v-254180e3">{{topic.$orig.postCount+"条帖子"}}</text></view></block></view></view></u-popup><u-popup bind:input="__e" vue-id="b4098e7e-14" mode="bottom" border-radius="20" value="{{showLocationModal}}" data-event-opts="{{[['^input',[['__set_model',['','showLocationModal','$event',[]]]]]]}}" class="data-v-254180e3" bind:__l="__l" vue-slots="{{['default']}}"><view class="location-modal data-v-254180e3"><view class="modal-header data-v-254180e3"><text class="modal-title data-v-254180e3">选择位置</text><u-icon vue-id="{{('b4098e7e-15')+','+('b4098e7e-14')}}" name="close" data-event-opts="{{[['^click',[['e1']]]]}}" bind:click="__e" class="data-v-254180e3" bind:__l="__l"></u-icon></view><view class="location-list data-v-254180e3"><block wx:for="{{nearbyLocations}}" wx:for-item="location" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['selectLocationItem',['$0'],[[['nearbyLocations','id',location.id]]]]]]]}}" class="location-option data-v-254180e3" bindtap="__e"><u-icon vue-id="{{('b4098e7e-16-'+__i1__)+','+('b4098e7e-14')}}" name="map-pin" color="#2979ff" size="16" class="data-v-254180e3" bind:__l="__l"></u-icon><view class="location-info data-v-254180e3"><text class="location-name data-v-254180e3">{{location.name}}</text><text class="location-address data-v-254180e3">{{location.address}}</text></view></view></block></view></view></u-popup></view>