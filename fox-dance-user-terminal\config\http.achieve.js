import $http, { $upload } from "./http.request";

// 登录
export const login = (data) => {
    return $http({
        url: "/api/user/authLogin",
        data,
        method: "post",
        authen: false,
    });
};

// 获取未签署的合同
export const getContractApi = (data) => {
    return $http({
        url: "/api/user/getContract",
        data,
        method: "post",
        authen: true,
    });
};

// 获取合同内容
export const hqhtnrApi = (data) => {
    return $http({
        url: "/api/card/signContract",
        data,
        method: "get",
        authen: true,
        ymgh: 1,
    });
};

// 签署合同
export const signContractApi = (data) => {
    return $http({
        url: "/api/card/signContract",
        data,
        method: "post",
        authen: true,
        ymgh: 1,
    });
};

// 个人中心
export const userInfoApi = (data) => {
    return $http({
        url: "/api/user/user",
        data,
        method: "post",
        authen: true,
    });
};

// 单页面
export const XieYi = (data) => {
    return $http({
        url: "/api/index/agreement",
        data,
        method: "post",
        authen: false,
    });
};

// 修改个人信息
export const toeditUserApi = (data) => {
    return $http({
        url: "/api/user/profile",
        data,
        method: "post",
        authen: true,
    });
};

// 地址列表
export const addrList = (data) => {
    return $http({
        url: "/api/addr/address_list",
        data,
        method: "post",
        authen: true,
    });
};

// 地址添加/编辑
export const addrAdd = (data) => {
    return $http({
        url: "/api/addr/address_edit",
        data,
        method: "post",
        authen: true,
    });
};

// 地址删除
export const addrDel = (data) => {
    return $http({
        url: "/api/addr/address_del",
        data,
        method: "post",
        authen: true,
    });
};

// 设置默认地址
export const addrmor = (data) => {
    return $http({
        url: "/api/addr/address_default_set",
        data,
        method: "post",
        authen: true,
    });
};

// 上传图片
export const upImg = (filePath, name, formData) => {
    return $upload({
        url: "/api/ajax/upload",
        filePath,
        name,
        formData,
        authen: true,
        ymgh: 1,
    });
};

// 获取反馈类型
export const feedbackCateApi = (data) => {
    return $http({
        url: "/api/user/feedback",
        data,
        method: "get",
        authen: true,
    });
};

// 获取反馈类型
export const feedbackSubApi = (data) => {
    return $http({
        url: "/api/user/feedback",
        data,
        method: "post",
        authen: true,
    });
};

// 我的积分列表
export const scoreListApi = (data) => {
    return $http({
        url: "/api/user/scoreList",
        data,
        method: "post",
        authen: true,
    });
};

// 优惠券列表
export const couponListApi = (data) => {
    return $http({
        url: "/api/user/couponList",
        data,
        method: "post",
        authen: true,
    });
};

// 首页
export const homeDataApi = (data) => {
    return $http({
        url: "/api/index/home",
        data,
        method: "post",
        authen: true,
    });
};

// 门店列表
export const storeListsApi = (data) => {
    return $http({
        url: "/api/store/index",
        data,
        method: "post",
        authen: true,
    });
};

// FOX介绍
export const foxJsApi = (data) => {
    return $http({
        url: "/api/index/exo",
        data,
        method: "post",
        authen: true,
    });
};

// 门店详情
export const storeDetailApi = (data) => {
    return $http({
        url: "/api/index/store",
        data,
        method: "post",
        authen: true,
    });
};

// 老师-筛选分类
export const lscxCategoryApi = (data) => {
    return $http({
        url: "/api/teacher/category",
        data,
        method: "post",
        authen: true,
    });
};

// 老师接口
export const teacherApi = (data) => {
    return $http({
        url: "/api/teacher/teacher",
        data,
        method: "post",
        authen: true,
    });
};

// 门店课程
export const storeCourseApi = (data) => {
    return $http({
        url: "/api/index/store_courses",
        data,
        method: "post",
        authen: true,
    });
};

//积分商城>分类
export const mallCategoryApi = (data) => {
    return $http({
        url: "/api/shop/getCategory",
        data,
        method: "post",
        authen: true,
    });
};

//积分商城>获取商品列表
export const mallListsApi = (data) => {
    return $http({
        url: "/api/shop/getGoodsList",
        data,
        method: "post",
        authen: true,
    });
};

//积分商城>商品详情
export const mallListsxqApi = (data) => {
    return $http({
        url: "/api/shop/getGoodsDetail",
        data,
        method: "post",
        authen: true,
    });
};

//积分商城>立即兑换
export const exchangeSubApi = (data) => {
    return $http({
        url: "/api/shop/exchange",
        data,
        method: "post",
        authen: true,
    });
};

//积分商城>我的订单
export const myOrderApi = (data) => {
    return $http({
        url: "/api/shop/myOrder",
        data,
        method: "post",
        authen: true,
    });
};

//积分商城>确认收货
export const confirmOrderApi = (data) => {
    return $http({
        url: "/api/shop/confirm",
        data,
        method: "post",
        authen: true,
    });
};

//积分商城>查询物流
export const expressApi = (data) => {
    return $http({
        url: "/api/shop/express",
        data,
        method: "post",
        authen: true,
    });
};

//tabber 课包列表
export const CoursePackageListsApi = (data) => {
    return $http({
        url: "/api/CoursePackage/index",
        data,
        method: "post",
        authen: true,
    });
};

//课包详情
export const CoursePackageListsxqApi = (data) => {
    return $http({
        url: "/api/CoursePackage/detail",
        data,
        method: "post",
        authen: true,
    });
};

//课包详情>讲师详情
export const teacherDetailApi = (data) => {
    return $http({
        url: "/api/CoursePackage/teacherDetail",
        data,
        method: "post",
        authen: true,
    });
};

//课包详情>获取购买详情
export const buyDetailApi = (data) => {
    return $http({
        url: "/api/CoursePackage/buyDetail",
        data,
        method: "post",
        authen: true,
    });
};

//课包详情>购买课包
export const kbbuySubApi = (data) => {
    return $http({
        url: "/api/CoursePackage/buy",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心>我的课包
export const myPackageApi = (data) => {
    return $http({
        url: "/api/CoursePackage/myPackage",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心>我的课包详情
export const myPackagexqApi = (data) => {
    return $http({
        url: "/api/CoursePackage/myPackageDetail",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心>我的课包详情视频观看
export const videoViewApi = (data) => {
    return $http({
        url: "/api/CoursePackage/video",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心>我的会员卡
export const myCardApi = (data) => {
    return $http({
        url: "/api/card/my",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心>我的会员卡详情
export const myCardxqApi = (data) => {
    return $http({
        url: "/api/card/detail",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心>排行榜
export const myRankApi = (data) => {
    return $http({
        url: "/api/user/rank",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心>请假>确认请假
export const confirmAskForLeaveApi = (data) => {
    return $http({
        url: "/api/AskForLeave/confirmAskForLeave",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心>请假>请假记录
export const askForLeaveRecordApi = (data) => {
    return $http({
        url: "/api/AskForLeave/askForLeaveRecord",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心>请假>提前销假
export const cancelAskForLeaveApi = (data) => {
    return $http({
        url: "/api/AskForLeave/cancelAskForLeave",
        data,
        method: "post",
        authen: true,
    });
};

//tabber 会员卡
export const cardsApi = (data) => {
    return $http({
        url: "/api/card/index",
        data,
        method: "post",
        authen: true,
    });
};

//tabber 开通会员卡
export const buyCardsApi = (data) => {
    return $http({
        url: "/api/card/buy",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心-邀请有奖
export const inviteApi = (data) => {
    return $http({
        url: "/api/user/invite",
        data,
        method: "post",
        authen: true,
    });
};

//老师列表
export const TeachersIntroductionApi = (data) => {
    return $http({
        url: "/api/teacher/TeachersIntroduction",
        data,
        method: "post",
        authen: true,
    });
};

//通过老师-筛选门店
export const searchStoreApi = (data) => {
    return $http({
        url: "/api/teacher/teacherStore",
        data,
        method: "post",
        authen: true,
    });
};

//课程详情
export const myCourseXqApi = (data) => {
    return $http({
        url: "/api/course/detail",
        data,
        method: "post",
        authen: true,
    });
};

//我的课程
export const myCourseApi = (data) => {
    return $http({
        url: "/api/course/myCourse",
        data,
        method: "post",
        authen: true,
    });
};

//预约课程
export const myCourseyuyueApi = (data) => {
    return $http({
        url: "/api/course/order",
        data,
        method: "post",
        authen: true,
    });
};

//取消预约
export const cancelCourseApi = (data) => {
    return $http({
        url: "/api/course/cancel",
        data,
        method: "post",
        authen: true,
    });
};

//客服-转人工
export const kefuzrgApi = (data) => {
    return $http({
        url: "/api/mobile/service/turn_artificial",
        data,
        method: "post",
        authen: true,
        kefu: 1,
    });
};

//客服-获取聊天记录
export const kefuzRecordApi = (data) => {
    return $http({
        url: "/api/mobile/user/record",
        data,
        method: "get",
        authen: true,
        kefu: 1,
    });
};

//客服-发送消息
export const sendMessageApi = (data) => {
    return $http({
        url: "/api/mobile/service/send_message",
        data,
        method: "post",
        authen: true,
        kefu: 1,
    });
};

//个人中心-用户报告-月报
export const monthUserReportApi = (data) => {
    return $http({
        url: "/api/user/report",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心-用户报告-年报
export const yearReportApi = (data) => {
    return $http({
        url: "/api/user/yearReport",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心-用户报告-周报
export const weekReportApi = (data) => {
    return $http({
        url: "/api/user/weekReport",
        data,
        method: "post",
        authen: true,
    });
};

//个人中心-消息中心
export const messageApi = (data) => {
    return $http({
        url: "/api/user/message",
        data,
        method: "post",
        authen: true,
    });
};

//激活会员卡
export const jhcardApi = (data) => {
    return $http({
        url: "/api/card/active",
        data,
        method: "post",
        authen: true,
    });
};

//设置默认会员卡
export const setcardApi = (data) => {
    return $http({
        url: "/api/card/setDefault",
        data,
        method: "post",
        authen: true,
    });
};

//奖池列表
export const prizedrawApi = (data) => {
    return $http({
        url: "/api/prize_draw/index",
        data,
        method: "post",
        authen: true,
    });
};

//抽奖
export const drawSubApi = (data) => {
    return $http({
        url: "/api/prize_draw/draw",
        data,
        method: "post",
        authen: true,
    });
};

//中奖记录
export const drawrecordsApi = (data) => {
    return $http({
        url: "/api/prize_draw/records",
        data,
        method: "post",
        authen: true,
    });
};

//更改邀请人
export const changePidApi = (data) => {
    return $http({
        url: "/api/user/changePid",
        data,
        method: "post",
        authen: true,
    });
};

//兑换商品
export const exchangeGoodsApi = (data) => {
    return $http({
        url: "/api/prize_draw/exchangeGoods",
        data,
        method: "post",
        authen: true,
    });
};

//兑换会员卡
export const exchangeMemberCardApi = (data) => {
    return $http({
        url: "/api/prize_draw/exchangeMemberCard",
        data,
        method: "post",
        authen: true,
    });
};

//兑换红包
export const exchangeRedPackApi = (data) => {
    return $http({
        url: "/api/prize_draw/exchangeRedPack",
        data,
        method: "post",
        authen: true,
    });
};

// 修改收款账户
export const paymentcodeApi = (data) => {
    return $http({
        url: "/api/user/payment_code",
        data,
        method: "post",
        authen: true,
    });
};

// 等级奖励
export const LevelRewardsApi = (data) => {
    return $http({
        url: "/api/level/LevelRewards",
        data,
        method: "post",
        authen: true,
    });
};

// 兑换商品
export const exchangeGoodsLevelApi = (data) => {
    return $http({
        url: "/api/level/exchangeGoods",
        data,
        method: "post",
        authen: true,
    });
};

// 兑换红包
export const exchangeRedPackLevelApi = (data) => {
    return $http({
        url: "/api/level/exchangeRedPack",
        data,
        method: "post",
        authen: true,
    });
};

// 兑换会员卡
export const exchangeMemberCardLevelApi = (data) => {
    return $http({
        url: "/api/level/exchangeMemberCard",
        data,
        method: "post",
        authen: true,
    });
};

// 无门槛代金券兑换
export const wmkyhqdhApi = (data) => {
    return $http({
        url: "/api/level/exchangeCoupon",
        data,
        method: "post",
        authen: true,
    });
};

// 报告列表
export const reportListApi = (data) => {
    return $http({
        url: "/api/user/reportList",
        data,
        method: "post",
        authen: true,
    });
};

// 获取某个门店会员卡
export const getCardApi = (data) => {
    return $http({
        url: "/api/course/getCard",
        data,
        method: "post",
        authen: true,
    });
};

// 消息推送测试
export const pushTestingApi = (data) => {
    return $http({
        url: "/api/message/PushTesting",
        data,
        method: "post",
        authen: true,
    });
};

// 通知管理二维码
export const noticeManageApi = (data) => {
    return $http({
        url: "/api/message/noticeManage",
        data,
        method: "post",
        authen: true,
    });
};

//通知管理>详细设置
export const detailSettingApi = (data) => {
    return $http({
        url: "/api/message/detailSetting",
        data,
        method: "post",
        authen: true,
    });
};

//通知管理>详细设置>设置通知状态
export const setStatusApi = (data) => {
    return $http({
        url: "/api/message/setStatus",
        data,
        method: "post",
        authen: true,
    });
};

//解绑微信公众号
export const jcBindApi = (data) => {
    return $http({
        url: "/api/message/unbind",
        data,
        method: "post",
        authen: true,
    });
};