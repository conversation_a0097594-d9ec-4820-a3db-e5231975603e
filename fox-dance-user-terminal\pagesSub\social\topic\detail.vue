<template>
  <view class="topic-detail-container">
    <!-- 话题信息 -->
    <view class="topic-header" :style="{ backgroundImage: `url(${topicInfo.cover})` }">
      <view class="topic-overlay">
        <view class="topic-content">
          <text class="topic-name">#{{ topicInfo.name }}</text>
          <text class="topic-desc">{{ topicInfo.description }}</text>
          <view class="topic-stats">
            <text class="stat-item">{{ topicInfo.postCount }}条帖子</text>
            <text class="stat-item">{{ topicInfo.followCount }}人关注</text>
          </view>
          <view class="topic-actions">
            <u-button
              :type="topicInfo.isFollowed ? 'default' : 'primary'"
              size="default"
              :text="topicInfo.isFollowed ? '已关注' : '关注话题'"
              @click="toggleFollow"
            >{{ topicInfo.isFollowed ? '已关注' : '关注话题' }}</u-button>
            <u-button type="primary" plain size="default" text="发布帖子" @click="publishPost">发布帖子</u-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 帖子列表 -->
    <scroll-view
      class="posts-container"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <!-- 排序筛选 -->
      <view class="filter-bar">
        <u-tabs
          :list="filterTabs"
          :current="selectedFilterIndex"
          @change="selectFilter"
          :is-scroll="false"
          active-color="#2979ff"
          inactive-color="#666"
          :bar-width="40"
          :bar-height="4"
        ></u-tabs>
      </view>

      <!-- 帖子列表 -->
      <view class="posts-list">
        <PostCard
          v-for="post in posts"
          :key="post.id"
          :post="post"
          @like="onPostLike"
          @comment="onPostComment"
          @share="onPostShare"
          @click="goPostDetail"
        />
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <u-icon name="loading" v-if="loading" size="16" color="#999"></u-icon>
        <text class="load-text">{{ loading ? '加载中...' : '上拉加载更多' }}</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!hasMore && posts.length > 0">
        <text class="no-more-text">没有更多帖子了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="posts.length === 0 && !loading">
        <u-icon name="file-text" size="60" color="#ccc"></u-icon>
        <text class="empty-text">暂无帖子</text>
        <text class="empty-desc">成为第一个发布帖子的人吧</text>
        <u-button
          type="primary"
          size="default"
          text="发布帖子"
          @click="publishPost"
          style="margin-top: 20rpx;"
        >发布帖子</u-button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import PostCard from "../components/PostCard.vue";
import { getTagDetail, getTagPosts } from "@/utils/socialApi.js";

export default {
  name: "TopicDetail",
  components: {
    PostCard
  },
  data() {
    return {
      topicId: "",
      topicInfo: {},
      posts: [],
      refreshing: false,
      loading: false,
      hasMore: true,
      page: 1,
      pageSize: 10,
      selectedFilter: "latest",
      filterTabs: [
        { name: "最新", value: "latest" },
        { name: "最热", value: "hot" },
        { name: "精华", value: "featured" }
      ]
    };
  },
  computed: {
    selectedFilterIndex() {
      return this.filterTabs.findIndex(
        tab => tab.value === this.selectedFilter
      );
    }
  },
  onLoad(options) {
    this.topicId = options.id || "1";
    this.loadTopicInfo();
    this.loadPosts();
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    showMore() {
      uni.showActionSheet({
        itemList: ["举报话题", "分享话题"],
        success: res => {
          if (res.tapIndex === 0) {
            this.$u.toast("举报成功");
          } else if (res.tapIndex === 1) {
            this.shareTopic();
          }
        }
      });
    },

    async loadTopicInfo() {
      try {
        const result = await getTagDetail(this.topicId);
        console.log("话题详情", result);
        if (result && result.code === 0 && result.data) {
          const tag = result.data;
          this.topicInfo = {
            id: tag.id,
            name: tag.tagName,
            description: tag.description || "暂无描述",
            cover:
              tag.coverImage +
              "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85",
            postCount: tag.useCount || 0,
            followCount: tag.followCount || 0,
            isFollowed: tag.isFollowed || false
          };
        } else {
          console.error("获取话题详情失败:", result);
          this.$u.toast("获取话题详情失败");
        }
      } catch (error) {
        console.error("获取话题详情异常:", error);
        this.$u.toast("获取话题详情失败");
      }
    },

    async loadPosts() {
      this.loading = true;

      try {
        const params = {
          current: this.page,
          size: this.pageSize,
          sortBy: this.selectedFilter
        };

        const result = await getTagPosts(this.topicId, params);
        console.log("话题帖子", result);

        if (result && result.code === 0 && result.data) {
          const newPosts = result.data.records || [];

          // 转换数据格式，将nickname转换为username
          const processedPosts = newPosts.map(post => ({
            ...post,
            username: post.nickname || "无名氏",
            userAvatar:
              "https://file.foxdance.com.cn" +
              post.avatar +
              "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85"
          }));

          if (this.page === 1) {
            this.posts = processedPosts;
          } else {
            this.posts = [...this.posts, ...processedPosts];
          }

          // 检查是否还有更多数据
          this.hasMore = newPosts.length === this.pageSize;
        } else {
          console.error("获取话题帖子失败:", result);
          if (this.page === 1) {
            this.$u.toast("获取帖子失败");
          }
        }
      } catch (error) {
        console.error("获取话题帖子异常:", error);
        if (this.page === 1) {
          this.$u.toast("获取帖子失败");
        }
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    onRefresh() {
      this.refreshing = true;
      this.page = 1;
      this.hasMore = true;
      this.loadPosts();
    },

    loadMore() {
      if (!this.loading && this.hasMore) {
        this.page++;
        this.loadPosts();
      }
    },

    selectFilter(index) {
      this.selectedFilter = this.filterTabs[index].value;
      this.page = 1;
      this.hasMore = true;
      this.loadPosts();
    },

    toggleFollow() {
      this.topicInfo.isFollowed = !this.topicInfo.isFollowed;
      if (this.topicInfo.isFollowed) {
        this.topicInfo.followCount++;
        this.$u.toast("关注成功");
      } else {
        this.topicInfo.followCount--;
        this.$u.toast("取消关注");
      }
    },

    publishPost() {
      uni.navigateTo({
        url: `/pagesSub/social/publish/index?topicId=${this.topicId}&topicName=${this.topicInfo.name}`
      });
    },

    shareTopic() {
      this.$u.toast("分享成功");
    },

    onPostLike(post) {
      post.isLiked = !post.isLiked;
      if (post.isLiked) {
        post.likeCount++;
      } else {
        post.likeCount--;
      }
    },

    onPostComment(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      });
    },

    onPostShare(post) {
      this.$u.toast("分享成功");
    },

    goPostDetail(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.topic-detail-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: transparent;
  padding: var(--status-bar-height) 32rpx 0;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

.topic-header {
  height: 500rpx;
  background-size: cover;
  background-position: center;
  position: relative;
}

.topic-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 80rpx 32rpx 40rpx;
}

.topic-content {
  color: #fff;
}

.topic-name {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  display: block;
}

.topic-desc {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 24rpx;
  opacity: 0.9;
  display: block;
}

.topic-stats {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  font-size: 24rpx;
  opacity: 0.8;
}

.topic-actions {
  display: flex;
  gap: 24rpx;
}

.posts-container {
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  min-height: calc(100vh - 500rpx);
}

.filter-bar {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.posts-list {
  padding: 0 32rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  gap: 16rpx;
}

.load-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 28rpx;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin: 32rpx 0 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #ccc;
  margin-bottom: 40rpx;
}
</style>
