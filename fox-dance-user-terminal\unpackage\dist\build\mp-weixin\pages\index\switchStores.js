(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/switchStores"],{2372:function(t,e,n){"use strict";n.r(e);var i=n("4718"),s=n("bef3");for(var a in s)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(a);n("a61d");var o=n("828b"),d=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=d.exports},4718:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=(this._self._c,this.loding?this.storesLists.length:null);this.$mp.data=Object.assign({},{$root:{g0:e}})},s=[]},a1de:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("cff9");i(n("3240"));var s=i(n("2372"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(s.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},a3a5:function(t,e,n){},a61d:function(t,e,n){"use strict";var i=n("a3a5"),s=n.n(i);s.a},b5ba:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n("6061"),s={data:function(){return{isLogined:!0,loding:!1,storesLists:[],name:"",keywords:"",tabIndex:0,rzIndex:0,jlIndex:0,imgbaseUrl:"",storeInfo:{address:""},mdlist:!1}},onLoad:function(e){this.mdlist=!!e.mdlist,t.setNavigationBarTitle({title:e.mdlist?"门店列表":"切换门店"})},onShow:function(){this.imgbaseUrl=this.$baseUrl,this.storeData(),this.storeInfo=t.getStorageSync("storeInfo")},methods:{qhmdTap:function(e){if(console.log(e),e.id==t.getStorageSync("storeInfo").id)return t.showToast({title:"当前门店，无需切换",icon:"none",duration:1e3}),!1;t.setStorageSync("qhwc",1),t.setStorageSync("storeInfo",{address:e.address,id:e.id,name:e.name}),t.showToast({title:"切换成功",icon:"success",duration:1e3}),setTimeout((function(){t.navigateBack()}),1e3)},searchTap:function(){this.storeData()},tabTap:function(t){if(this.tabIndex=t,1==t){var e=this.rzIndex;e++,this.rzIndex=e++,3==this.rzIndex&&(this.rzIndex=1),this.jlIndex=0}if(2==t){var n=this.jlIndex;n++,this.jlIndex=n++,3==this.jlIndex&&(this.jlIndex=1),this.rzIndex=0}this.storeData()},storeData:function(){t.showLoading({title:"加载中"});var e=this;if(0==this.tabIndex)var n=1;else if(1==this.tabIndex)n=0==this.rzIndex?1:1==this.rzIndex?2:2==this.rzIndex?3:1;else if(2==this.tabIndex)n=0==this.jlIndex?1:1==this.jlIndex?4:2==this.jlIndex?5:1;(0,i.storeListsApi)({name:e.keywords,type:n,longitude:t.getStorageSync("postion").longitude,latitude:t.getStorageSync("postion").latitude,limit:9999}).then((function(n){console.log("门店列表",n),1==n.code&&(t.hideLoading(),e.loding=!0,e.storesLists=n.data.data)}))},dhTap:function(e){t.openLocation({name:e.address,latitude:1*e.latitude,longitude:1*e.longitude,success:function(){console.log("success")}})},navTo:function(e){t.navigateTo({url:e})}}};e.default=s}).call(this,n("df3c")["default"])},bef3:function(t,e,n){"use strict";n.r(e);var i=n("b5ba"),s=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=s.a}},[["a1de","common/runtime","common/vendor"]]]);