import config from './http.api'
import auth from './auth-fresh'

export default function $http(options) {
	// console.log('baseUrl:', config.apis.baseUrl)
	// console.log('options配置:', options)
	const {
		authen,
		url,
		data,
		method = 'post',
		contentType = 'application/json'
	} = options

	let header = {}
	if(options.kefu){
		const server_token = uni.getStorageSync('server_token')
		header = {
			'Content-Type': contentType,
			'server':1,
			'authori-zation':'Bearer ' + server_token,
			// 'authorization': '18236974973:+FYvbPp3q4V2Iql43ExeTY4s1GE1FTQtu7bxPI+qado=',
		}
	}else{
		if (authen == false) {
			header = {
				'Content-Type': contentType,
				'server':1
				// 'authorization': '18236974973:+FYvbPp3q4V2Iql43ExeTY4s1GE1FTQtu7bxPI+qado=',
			}
			// const token = auth.authFresh.token(url)
			// const token = uni.getStorageSync('token')
			// if(token){
			// 	header.authorization = token
			// }
		} else {
			// const token = auth.authFresh.token(url)
			// uni.setStorageSync('token',token)
			const token = uni.getStorageSync('token')
			header = {
				'Content-Type': contentType,
				'server':1,
				// #ifdef H5
				'Authorization': 'Bearer ' + token, // H5环境使用标准Authorization头
				// #endif
				// #ifndef H5
				'bausertoken': token, // 非H5环境使用自定义头
				// #endif
				// 'authorization': '18236974973:+FYvbPp3q4V2Iql43ExeTY4s1GE1FTQtu7bxPI+qado=',
			}
		}
	}
	
	return new Promise((resolve, reject) => {
		uni.request({
			url: options.kefu ? `${config.apis.kefu_baseUrl}${url}` : options.ymgh ? `${config.apis.htym_baseUrl}${url}` : `${config.apis.baseUrl}${url}`,
			data: data,
			method: method,
			header: header,
			success: function(res) {
				var urlapi = options.kefu ? `${config.apis.kefu_baseUrl}${url}` : options.ymgh ? `${config.apis.htym_baseUrl}${url}` : `${config.apis.baseUrl}${url}`
				handle(res.data, resolve, reject,urlapi)
			},
			fail: function(e) {
				var urlapi = options.kefu ? `${config.apis.kefu_baseUrl}${url}` : options.ymgh ? `${config.apis.htym_baseUrl}${url}` : `${config.apis.baseUrl}${url}`
				handle({
					code: 0,
					msg: '网络请求失败'
				}, resolve, reject, urlapi)
			},
			complete: function(e) {}
		})
	})
}

export function $upload(options) {
	// console.log('baseUrl:', config.apis.baseUrl)
	console.log('options11111:', options)
	const {
		authen,
		url,
		filePath,
		name,
		formData,
		ymgh
	} = options

	let header = {}
	if (authen == true) {
		// const token = auth.authFresh.token(url)
		const token = uni.getStorageSync('token')
		// console.log('token:', token)
		header = {
			'server':1,
			'bausertoken': token,
		}
	}
	return new Promise((resolve, reject) => {
		uni.uploadFile({
			url:options.ymgh ? `${config.apis.htym_baseUrl}${url}` : `${config.apis.baseUrl}${url}`,
			filePath: filePath,
			name: name,
			formData: formData,
			header: header,
			success: function(res) {
				var urlapi = options.ymgh ? `${config.apis.htym_baseUrl}${url}` : `${config.apis.baseUrl}${url}`
				handle(JSON.parse(res.data), resolve, reject, urlapi)
			},
			fail: function(e) {
				var urlapi = options.ymgh ? `${config.apis.htym_baseUrl}${url}` : `${config.apis.baseUrl}${url}`
				handle({
					code: 0,
					msg: '网络请求失败'
				}, resolve, reject, urlapi)
			},
			complete: function(e) {}
		})
	})
}

function handle(result, resolve, reject,urlapi) {
	// console.log(result,'resultresult111112222');
	// console.log(urlapi.split('api')[1],'urlapi');
	
	if (result.code == 409 || result.code == 303) { // 登录失效
		uni.removeStorageSync('token');
		uni.removeStorageSync('userid');
		uni.setStorageSync('tokenwx',1);
		uni.showModal({
			title: '提示',
			content: '登录超时，请重新登录',
			confirmText: '去登录',
			success: function(res) {
				if (res.confirm) {
					uni.reLaunch({
						url:'/pages/login/login'
					})
					// 跳转登录界面
					resolve(result)
				} else {
					reject(result)
				}
			}
		})
		uni.removeStorage({
			key: 'user'
		})
	} else if (result.code == 1 || result.status == 200 ||  result.status == 400) { // 请求成功
		resolve(result)
	} else if (result.code == 0) { // 请求成功
		// 修复urlapi可能为undefined的问题
		if(urlapi && urlapi.split && urlapi.split('api')[1] != '/message/PushTesting'){
			uni.showToast({
				title:result.msg,
				icon:"none"
			})
		}
		resolve(result)
	}else { // 其他
		reject(result)
	}
}