(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/mine"],{"21cb":function(n,t,e){},"65c1":function(n,t,e){"use strict";var o=e("21cb"),a=e.n(o);a.a},"738b":function(n,t,e){"use strict";(function(n,t){var o=e("47a9");e("cff9");o(e("3240"));var a=o(e("7c1f"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"7c1f":function(n,t,e){"use strict";e.r(t);var o=e("9385"),a=e("a27b");for(var i in a)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(i);e("65c1");var r=e("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"77706f4e",null,!1,o["a"],void 0);t["default"]=s.exports},9385:function(n,t,e){"use strict";e.d(t,"b",(function(){return a})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){return o}));var o={uNavbar:function(){return e.e("components/uview-ui/components/u-navbar/u-navbar").then(e.bind(null,"8564"))}},a=function(){var n=this,t=n.$createElement,e=(n._self._c,n.loding?{background:"rgba(255,255, 255,"+n.navBg+")"}:null);n._isMounted||(n.e0=function(t){n.zsewmToggle=!n.zsewmToggle},n.e1=function(t){n.zsewmToggle=!n.zsewmToggle}),n.$mp.data=Object.assign({},{$root:{a0:e}})},i=[]},"947c":function(n,t,e){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=e("6061"),a={components:{tabbar:function(){e.e("components/tabbar").then(function(){return resolve(e("eeb7"))}.bind(null,e)).catch(e.oe)}},data:function(){return{loding:!1,isLogined:!0,navBg:"",zsewmToggle:!1,userInfo:{avatar:"",train_count:0,train_day:0,train_time:0,score:0},imgbaseUrl:"",qjbutton:"#131315"}},onPageScroll:function(t){var e=n.upx2px(100),o=t.scrollTop,a=o/e>1?1:o/e;this.navBg=a},onLoad:function(){this.qjbutton=n.getStorageSync("storeInfo").button,n.hideTabBar()},onShow:function(){n.hideTabBar(),this.imgbaseUrl=this.$baseUrl,this.isLogined=!!n.getStorageSync("token"),this.isLogined?(this.userData(),console.log(this.$baseUrl,"this.$baseUrl")):this.loding=!0,n.getStorageSync("selectCards")&&n.removeStorageSync("selectCards"),this.$refs.tabbar&&this.$refs.tabbar.setColor()},methods:{userData:function(){n.showLoading({title:"加载中"});var t=this;(0,o.userInfoApi)({}).then((function(e){console.log("个人中心",e),1==e.code&&(t.loding=!0,t.userInfo=e.data,n.hideLoading())}))},imgUploadTap:function(){n.showLoading({title:"加载中"}),n.downloadFile({url:this.userInfo.share,success:function(t){n.saveImageToPhotosAlbum({filePath:t.tempFilePath,success:function(){n.hideLoading(),n.showToast({title:"保存成功"})},fail:function(t){n.hideLoading(),console.log(t,"保存失败"),n.showToast({icon:"none",mask:!0,title:"保存失败",duration:3e3})}})}})},userReportTap:function(){if(""==n.getStorageSync("token")||void 0==n.getStorageSync("token")||!n.getStorageSync("token"))return n.showToast({icon:"none",title:"请先登录"}),n.navigateTo({url:"/pages/login/login"}),!1;n.navigateTo({url:"/pages/mine/userReport/userReport"})},navTo:function(t,e){if(e)return n.navigateTo({url:t}),!1;""!=n.getStorageSync("token")&&void 0!=n.getStorageSync("token")&&n.getStorageSync("token")?n.navigateTo({url:t}):(n.showToast({icon:"none",title:"请先登录"}),n.navigateTo({url:"/pages/login/login"}))}}};t.default=a}).call(this,e("df3c")["default"])},a27b:function(n,t,e){"use strict";e.r(t);var o=e("947c"),a=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(i);t["default"]=a.a}},[["738b","common/runtime","common/vendor"]]]);