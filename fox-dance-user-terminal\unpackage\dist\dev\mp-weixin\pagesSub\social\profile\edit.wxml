<view class="edit-profile-container data-v-10e92e6d"><block wx:if="{{loading}}"><view class="loading-container data-v-10e92e6d"><view class="loading-spinner data-v-10e92e6d"></view><text class="loading-text data-v-10e92e6d">加载中...</text></view></block><block wx:else><scroll-view class="content data-v-10e92e6d" scroll-y="{{true}}"><view class="edit-section data-v-10e92e6d"><view class="edit-item data-v-10e92e6d"><text class="edit-label data-v-10e92e6d">头像</text><view data-event-opts="{{[['tap',[['editAvatar',['$event']]]]]}}" class="avatar-edit data-v-10e92e6d" bindtap="__e"><u-avatar vue-id="0c3d2039-1" src="{{userInfo.avatar}}" size="80" class="data-v-10e92e6d" bind:__l="__l"></u-avatar><u-icon class="camera-icon data-v-10e92e6d" vue-id="0c3d2039-2" name="camera" color="#999" size="32rpx" bind:__l="__l"></u-icon></view></view></view><view class="edit-section data-v-10e92e6d"><view class="edit-item data-v-10e92e6d"><text class="edit-label data-v-10e92e6d">昵称</text><u-input bind:input="__e" class="edit-input data-v-10e92e6d" vue-id="0c3d2039-3" placeholder="请输入昵称" border="none" value="{{userInfo.nickname}}" data-event-opts="{{[['^input',[['__set_model',['$0','nickname','$event',[]],['userInfo']]]]]}}" bind:__l="__l"></u-input></view><view class="edit-item data-v-10e92e6d"><text class="edit-label data-v-10e92e6d">舞种</text><u-input bind:input="__e" class="edit-input data-v-10e92e6d" vue-id="0c3d2039-4" placeholder="请输入舞种" border="none" value="{{userInfo.danceType}}" data-event-opts="{{[['^input',[['__set_model',['$0','danceType','$event',[]],['userInfo']]]]]}}" bind:__l="__l"></u-input></view><view class="edit-item data-v-10e92e6d"><text class="edit-label data-v-10e92e6d">个人简介</text><u-input bind:input="__e" class="edit-textarea data-v-10e92e6d" vue-id="0c3d2039-5" type="textarea" placeholder="请输入个人简介" border="none" autoHeight="{{true}}" value="{{userInfo.bio}}" data-event-opts="{{[['^input',[['__set_model',['$0','bio','$event',[]],['userInfo']]]]]}}" bind:__l="__l"></u-input></view></view><view class="button-section data-v-10e92e6d"><u-button class="save-button data-v-10e92e6d" vue-id="0c3d2039-6" type="primary" loading="{{saving}}" disabled="{{saving}}" data-event-opts="{{[['^click',[['saveProfile']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">{{saving?'保存中...':'保存'}}</u-button></view></scroll-view></block></view>