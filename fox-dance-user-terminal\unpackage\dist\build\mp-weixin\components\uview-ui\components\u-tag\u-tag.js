(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uview-ui/components/u-tag/u-tag"],{1369:function(t,o,e){"use strict";e.r(o);var n=e("cf51"),i=e.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(r);o["default"]=i.a},"1e91":function(t,o,e){},"24b8":function(t,o,e){"use strict";e.r(o);var n=e("8ff0"),i=e("1369");for(var r in i)["default"].indexOf(r)<0&&function(t){e.d(o,t,(function(){return i[t]}))}(r);e("2760");var l=e("828b"),u=Object(l["a"])(i["default"],n["b"],n["c"],!1,null,"67b7ee20",null,!1,n["a"],void 0);o["default"]=u.exports},2760:function(t,o,e){"use strict";var n=e("1e91"),i=e.n(n);i.a},"8ff0":function(t,o,e){"use strict";e.d(o,"b",(function(){return i})),e.d(o,"c",(function(){return r})),e.d(o,"a",(function(){return n}));var n={uIcon:function(){return e.e("components/uview-ui/components/u-icon/u-icon").then(e.bind(null,"8398"))}},i=function(){var t=this,o=t.$createElement,e=(t._self._c,t.show?t.__get_style([t.customStyle]):null),n=t.show&&t.closeable?t.__get_style([t.iconStyle]):null;t.$mp.data=Object.assign({},{$root:{s0:e,s1:n}})},r=[]},cf51:function(t,o,e){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var n={name:"u-tag",props:{type:{type:String,default:"primary"},disabled:{type:[Boolean,String],default:!1},size:{type:String,default:"default"},shape:{type:String,default:"square"},text:{type:[String,Number],default:""},bgColor:{type:String,default:""},color:{type:String,default:""},borderColor:{type:String,default:""},closeColor:{type:String,default:""},index:{type:[Number,String],default:""},mode:{type:String,default:"light"},closeable:{type:Boolean,default:!1},show:{type:Boolean,default:!0}},data:function(){return{}},computed:{customStyle:function(){var t={};return this.color&&(t.color=this.color),this.bgColor&&(t.backgroundColor=this.bgColor),"plain"==this.mode&&this.color&&!this.borderColor?t.borderColor=this.color:t.borderColor=this.borderColor,t},iconStyle:function(){if(this.closeable){var t={};return"mini"==this.size?t.fontSize="20rpx":t.fontSize="22rpx","plain"==this.mode||"light"==this.mode?t.color=this.type:"dark"==this.mode&&(t.color="#ffffff"),this.closeColor&&(t.color=this.closeColor),t}},closeIconColor:function(){return this.closeColor?this.closeColor:this.color?this.color:"dark"==this.mode?"#ffffff":this.type}},methods:{clickTag:function(){this.disabled||this.$emit("click",this.index)},close:function(){this.$emit("close",this.index)}}};o.default=n}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uview-ui/components/u-tag/u-tag-create-component',
    {
        'components/uview-ui/components/u-tag/u-tag-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("24b8"))
        })
    },
    [['components/uview-ui/components/u-tag/u-tag-create-component']]
]);
