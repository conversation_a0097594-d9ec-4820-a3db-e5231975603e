(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/profile/index"],{3427:function(e,t,r){"use strict";var n=r("f33b"),a=r.n(n);a.a},"7b6c":function(e,t,r){"use strict";(function(e){var n=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r("7eb4")),o=n(r("7ca3")),s=n(r("3b2d")),u=n(r("ee10")),i=r("41ad");function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var d={name:"SocialProfile",components:{PostCard:function(){r.e("pagesSub/social/components/PostCard").then(function(){return resolve(r("390e"))}.bind(null,r)).catch(r.oe)},FollowButton:function(){r.e("pagesSub/social/components/FollowButton").then(function(){return resolve(r("607e"))}.bind(null,r)).catch(r.oe)}},data:function(){return{userInfo:{userId:"",nickname:"",avatar:"",bio:"",danceType:"",postCount:0,privatePostCount:0,followingCount:0,followersCount:0,likeCount:0,draftCount:0},loading:!0,currentTab:0,isInitialized:!1,tabs:[{name:"作品",data:[],loading:!1},{name:"私密作品",data:[],loading:!1},{name:"关注",data:[],loading:!1},{name:"粉丝",data:[],loading:!1},{name:"喜欢",data:[],loading:!1}]}},onLoad:function(){var e=this;console.log("Profile页面 onLoad"),this.$nextTick((function(){setTimeout((function(){e.initializeData()}),100)}))},onShow:function(){console.log("Profile页面 onShow"),this.isInitialized&&this.loadUserInfo()},methods:{initializeData:function(){var e=this;return(0,u.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("初始化Profile页面数据..."),t.prev=1,t.next=4,e.loadUserInfo();case 4:e.isInitialized=!0,t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](1),console.error("初始化数据失败:",t.t0);case 10:case"end":return t.stop()}}),t,null,[[1,7]])})))()},loadUserInfo:function(){var t=this;return(0,u.default)(a.default.mark((function r(){var n,o,s;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(r.prev=0,n=t.getCurrentUserId(),n){r.next=6;break}return console.error("用户未登录，无法加载用户信息"),e.showToast({title:"请先登录",icon:"none"}),r.abrupt("return");case 6:return console.log("开始加载用户信息 - userId:",n),t.loading=!0,r.next=10,(0,i.getUserProfile)(n);case 10:if(o=r.sent,console.log("用户信息API返回:",o),!o||0!==o.code||!o.data){r.next=21;break}return s=o.data,t.userInfo={userId:s.id||s.userId||n,nickname:s.nickname||"舞蹈爱好者",avatar:t.formatAvatarUrl(s.avatar),bio:s.bio||s.userProfile||"热爱舞蹈，享受生活",danceType:s.danceType||"街舞",postCount:s.postCount||0,privatePostCount:0,followingCount:s.followingCount||0,followersCount:s.followersCount||s.followerCount||0,likeCount:s.likeReceivedCount||s.likeCount||0,draftCount:s.draftCount||0},console.log("用户信息加载成功:",t.userInfo),r.next=18,t.loadPrivatePostCount(n);case 18:t.loadTabData(t.currentTab),r.next=23;break;case 21:console.error("用户信息API返回格式不正确:",o),e.showToast({title:"加载用户信息失败",icon:"none"});case 23:r.next=29;break;case 25:r.prev=25,r.t0=r["catch"](0),console.error("加载用户信息失败:",r.t0),e.showToast({title:"加载失败，请重试",icon:"none"});case 29:return r.prev=29,t.loading=!1,r.finish(29);case 32:case"end":return r.stop()}}),r,null,[[0,25,29,32]])})))()},loadPrivatePostCount:function(e){var t=this;return(0,u.default)(a.default.mark((function r(){var n;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.getPostList)({current:1,size:1,userId:e,isPublic:0});case 3:n=r.sent,n&&0===n.code&&n.data&&(void 0!==n.data.total?t.userInfo.privatePostCount=n.data.total:Array.isArray(n.data)&&(t.userInfo.privatePostCount=n.data.length)),console.log("私密作品数量:",t.userInfo.privatePostCount),r.next=12;break;case 8:r.prev=8,r.t0=r["catch"](0),console.error("加载私密作品数量失败:",r.t0),t.userInfo.privatePostCount=0;case 12:case"end":return r.stop()}}),r,null,[[0,8]])})))()},getCurrentUserId:function(){var t=e.getStorageSync("userid");return t?Number(t):null},formatAvatarUrl:function(e){return e?e.startsWith("http")||e.startsWith("/")?e:"/".concat(e):"/static/images/toux.png"},loadTabData:function(e){var t=this;return(0,u.default)(a.default.mark((function r(){var n;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.tabs[e].loading){r.next=2;break}return r.abrupt("return");case 2:t.$set(t.tabs[e],"loading",!0),r.prev=3,n=[],r.t0=e,r.next=0===r.t0?8:1===r.t0?12:2===r.t0?16:3===r.t0?20:4===r.t0?24:28;break;case 8:return r.next=10,t.loadUserPosts();case 10:return n=r.sent,r.abrupt("break",28);case 12:return r.next=14,t.loadPrivatePosts();case 14:return n=r.sent,r.abrupt("break",28);case 16:return r.next=18,t.loadFollowingUsers();case 18:return n=r.sent,r.abrupt("break",28);case 20:return r.next=22,t.loadFollowersUsers();case 22:return n=r.sent,r.abrupt("break",28);case 24:return r.next=26,t.loadLikedPosts();case 26:return n=r.sent,r.abrupt("break",28);case 28:t.$set(t.tabs[e],"data",n),r.next=35;break;case 31:r.prev=31,r.t1=r["catch"](3),console.error("加载标签页".concat(e,"数据失败:"),r.t1),t.$set(t.tabs[e],"data",[]);case 35:return r.prev=35,t.$set(t.tabs[e],"loading",!1),r.finish(35);case 38:case"end":return r.stop()}}),r,null,[[3,31,35,38]])})))()},loadUserPosts:function(){var e=this;return(0,u.default)(a.default.mark((function t(){var r,n,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,r=e.getCurrentUserId(),r){t.next=5;break}return console.error("用户未登录，无法加载帖子"),t.abrupt("return",[]);case 5:return t.next=7,(0,i.getPostList)({current:1,size:20,userId:r,isPublic:1,sortField:"createTime",sortOrder:"desc"});case 7:if(n=t.sent,console.log("用户帖子API返回:",n),!n||0!==n.code||!n.data){t.next=14;break}return o=Array.isArray(n.data)?n.data:n.data.records||[],t.abrupt("return",o.map((function(t){var r;return{id:t.id,title:t.title||"",coverImage:e.formatImageUrl((null===(r=t.images)||void 0===r?void 0:r[0])||t.coverImage),username:t.nickname||e.userInfo.nickname,userAvatar:e.formatAvatarUrl(t.avatar||e.userInfo.avatar),content:t.content,likeCount:t.likeCount||0,commentCount:t.commentCount||0,isLiked:t.isLiked||!1,isPublic:t.isPublic,status:t.status,createTime:new Date(t.createTime)}})));case 14:return console.log("用户帖子API返回格式不正确:",n),t.abrupt("return",[]);case 16:t.next=22;break;case 18:return t.prev=18,t.t0=t["catch"](0),console.error("加载用户帖子失败:",t.t0),t.abrupt("return",[]);case 22:case"end":return t.stop()}}),t,null,[[0,18]])})))()},loadPrivatePosts:function(){var e=this;return(0,u.default)(a.default.mark((function t(){var r,n,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,r=e.getCurrentUserId(),r){t.next=5;break}return console.error("用户未登录，无法加载私密帖子"),t.abrupt("return",[]);case 5:return t.next=7,(0,i.getPostList)({current:1,size:20,userId:r,isPublic:0,sortField:"createTime",sortOrder:"desc"});case 7:if(n=t.sent,console.log("用户私密帖子API返回:",n),!n||0!==n.code||!n.data){t.next=14;break}return o=Array.isArray(n.data)?n.data:n.data.records||[],t.abrupt("return",o.map((function(t){var r;return{id:t.id,title:t.title||"",coverImage:e.formatImageUrl((null===(r=t.images)||void 0===r?void 0:r[0])||t.coverImage),username:t.nickname||e.userInfo.nickname,userAvatar:e.formatAvatarUrl(t.avatar||e.userInfo.avatar),content:t.content,likeCount:t.likeCount||0,commentCount:t.commentCount||0,isLiked:t.isLiked||!1,isPublic:t.isPublic,status:t.status,createTime:new Date(t.createTime)}})));case 14:return console.log("用户私密帖子API返回格式不正确:",n),t.abrupt("return",[]);case 16:t.next=22;break;case 18:return t.prev=18,t.t0=t["catch"](0),console.error("加载用户私密帖子失败:",t.t0),t.abrupt("return",[]);case 22:case"end":return t.stop()}}),t,null,[[0,18]])})))()},loadLikedPosts:function(){var e=this;return(0,u.default)(a.default.mark((function t(){var r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,r=e.getCurrentUserId(),r){t.next=5;break}return console.error("用户未登录，无法加载点赞帖子"),t.abrupt("return",[]);case 5:return console.log("加载用户点赞的帖子 - userId:",r),t.abrupt("return",[]);case 9:return t.prev=9,t.t0=t["catch"](0),console.error("加载点赞帖子失败:",t.t0),t.abrupt("return",[]);case 13:case"end":return t.stop()}}),t,null,[[0,9]])})))()},loadFollowingUsers:function(){var e=this;return(0,u.default)(a.default.mark((function t(){var r,n,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,r=e.getCurrentUserId(),r){t.next=5;break}return console.error("用户未登录，无法加载关注列表"),t.abrupt("return",[]);case 5:return console.log("开始加载关注用户列表..."),t.next=8,(0,i.getFollowingList)(r,{current:1,size:50});case 8:if(n=t.sent,console.log("关注用户API返回:",n),!n||0!==n.code||!n.data){t.next=15;break}return o=Array.isArray(n.data)?n.data:n.data.records||[],t.abrupt("return",o.map((function(t){return{id:t.userId||t.id,userId:t.userId||t.id,nickname:t.nickname||"用户",avatar:"https://file.foxdance.com.cn"+e.formatAvatarUrl(t.avatar),bio:t.bio||"暂无简介",danceType:t.danceType||"",followersCount:t.followerCount||0,isFollowed:!0,type:"user"}})));case 15:return console.error("关注用户API返回格式不正确:",n),t.abrupt("return",[]);case 17:t.next=23;break;case 19:return t.prev=19,t.t0=t["catch"](0),console.error("加载关注用户失败:",t.t0),t.abrupt("return",[]);case 23:case"end":return t.stop()}}),t,null,[[0,19]])})))()},loadFollowersUsers:function(){var e=this;return(0,u.default)(a.default.mark((function t(){var r,n,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,r=e.getCurrentUserId(),r){t.next=5;break}return console.error("用户未登录，无法加载粉丝列表"),t.abrupt("return",[]);case 5:return console.log("开始加载粉丝用户列表..."),t.next=8,(0,i.getFollowersList)(r,{current:1,size:50});case 8:if(n=t.sent,console.log("粉丝用户API返回:",n),!n||0!==n.code||!n.data){t.next=15;break}return o=Array.isArray(n.data)?n.data:n.data.records||[],t.abrupt("return",o.map((function(t){return{id:t.userId||t.id,userId:t.userId||t.id,nickname:t.nickname||"用户",avatar:"https://file.foxdance.com.cn"+e.formatAvatarUrl(t.avatar),bio:t.bio||"暂无简介",danceType:t.danceType||"",followersCount:t.followerCount||0,isFollowed:t.isFollowed||!1,type:"user"}})));case 15:return console.error("粉丝用户API返回格式不正确:",n),t.abrupt("return",[]);case 17:t.next=23;break;case 19:return t.prev=19,t.t0=t["catch"](0),console.error("加载粉丝用户失败:",t.t0),t.abrupt("return",[]);case 23:case"end":return t.stop()}}),t,null,[[0,19]])})))()},formatImageUrl:function(e){return e?e.startsWith("http")||e.startsWith("/")?e:"/".concat(e):"/static/images/default-cover.png"},switchTab:function(e){var t="object"===(0,s.default)(e)?e.index:e;this.currentTab!==t&&(this.currentTab=t,this.loadTabData(t))},scanCode:function(){e.scanCode({success:function(e){console.log("扫码结果:",e)}})},goSettings:function(){e.navigateTo({url:"/pagesSub/social/settings/index"})},editAvatar:function(){var t=this;e.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:function(e){t.userInfo.avatar=e.tempFilePaths[0]}})},editProfile:function(){e.navigateTo({url:"/pagesSub/social/profile/edit"})},goLikeList:function(){e.navigateTo({url:"/pagesSub/social/like/list"})},viewPost:function(t){e.navigateTo({url:"/pagesSub/social/post/detail?id=".concat(t.id)})},goPostDetail:function(t){e.navigateTo({url:"/pagesSub/social/post/detail?id=".concat(t.id)})},goUserProfile:function(t){if("user"===t.type){var r=t.id||t.userId;r&&e.navigateTo({url:"/pagesSub/social/user/profile?userId=".concat(r,"&name=").concat(t.nickname)})}else{if(t.username===this.userInfo.nickname)return;e.navigateTo({url:"/pagesSub/social/user/profile?id=".concat(t.userId||t.id)})}},onPostLike:function(t){var r=this;return(0,u.default)(a.default.mark((function n(){var o,s;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,!t.isLiked){n.next=9;break}return n.next=4,(0,i.unlikePost)(t.id);case 4:t.isLiked=!1,t.likeCount=Math.max(0,t.likeCount-1),e.showToast({title:"取消点赞",icon:"none",duration:1e3}),n.next=14;break;case 9:return n.next=11,(0,i.likePost)(t.id);case 11:t.isLiked=!0,t.likeCount+=1,e.showToast({title:"点赞成功",icon:"success",duration:1e3});case 14:o=r.tabs[r.currentTab].data,s=o.findIndex((function(e){return e.id===t.id})),-1!==s&&r.$set(o,s,l({},t)),n.next=23;break;case 19:n.prev=19,n.t0=n["catch"](0),console.error("点赞操作失败:",n.t0),e.showToast({title:"操作失败",icon:"none"});case 23:case"end":return n.stop()}}),n,null,[[0,19]])})))()},forceRefresh:function(){console.log("强制刷新个人资料页面数据..."),this.isInitialized=!1,this.loading=!0,this.userInfo={userId:"",nickname:"",avatar:"",bio:"",danceType:"",postCount:0,followingCount:0,followersCount:0,likeCount:0,draftCount:0},this.tabs.forEach((function(e){e.data=[],e.loading=!1})),this.initializeData()},getEmptyText:function(){return["暂无作品","暂无私密作品","暂无关注","暂无粉丝","暂无喜欢"][this.currentTab]||"暂无内容"},onUserFollow:function(e){console.log("用户关注成功:",e),this.updateUserFollowStatus(e.user.id,!0),this.userInfo.followingCount+=1},onUserUnfollow:function(e){console.log("用户取消关注成功:",e),this.updateUserFollowStatus(e.user.id,!1),this.userInfo.followingCount>0&&(this.userInfo.followingCount-=1)},onFollowChange:function(e){console.log("关注状态变化:",e),this.updateUserFollowStatus(e.user.id,e.isFollowed)},updateUserFollowStatus:function(e,t){if(this.tabs[2]&&this.tabs[2].data){var r=this.tabs[2].data.find((function(t){return t.id===e}));r&&(r.isFollowed=t)}if(this.tabs[3]&&this.tabs[3].data){var n=this.tabs[3].data.find((function(t){return t.id===e}));n&&(n.isFollowed=t)}}}};t.default=d}).call(this,r("df3c")["default"])},afd0:function(e,t,r){"use strict";(function(e,t){var n=r("47a9");r("cff9");n(r("3240"));var a=n(r("bea8"));e.__webpack_require_UNI_MP_PLUGIN__=r,t(a.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},bea8:function(e,t,r){"use strict";r.r(t);var n=r("c106"),a=r("ef21");for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);r("3427");var s=r("828b"),u=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"7bb4fb94",null,!1,n["a"],void 0);t["default"]=u.exports},c106:function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return o})),r.d(t,"a",(function(){return n}));var n={uIcon:function(){return r.e("components/uview-ui/components/u-icon/u-icon").then(r.bind(null,"8398"))},uAvatar:function(){return r.e("components/uview-ui/components/u-avatar/u-avatar").then(r.bind(null,"8d35"))},uTabs:function(){return r.e("components/uview-ui/components/u-tabs/u-tabs").then(r.bind(null,"6916"))},uEmpty:function(){return r.e("components/uview-ui/components/u-empty/u-empty").then(r.bind(null,"8fa1"))}},a=function(){var e=this,t=e.$createElement,r=(e._self._c,e.tabs[e.currentTab]&&e.tabs[e.currentTab].data.length>0),n=!r||0===e.currentTab||1===e.currentTab||4===e.currentTab||2!==e.currentTab&&3!==e.currentTab?null:e.__map(e.tabs[e.currentTab].data,(function(t,r){var n=e.__get_orig(t),a={id:t.id,nickname:t.nickname};return{$orig:n,a0:a}})),a=r?null:e.getEmptyText();e.$mp.data=Object.assign({},{$root:{g0:r,l0:n,m0:a}})},o=[]},ef21:function(e,t,r){"use strict";r.r(t);var n=r("7b6c"),a=r.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},f33b:function(e,t,r){}},[["afd0","common/runtime","common/vendor"]]]);