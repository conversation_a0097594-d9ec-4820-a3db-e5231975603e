<template>
  <view class="topic-list-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <!-- 搜索栏 -->
        <view class="search-section">
          <view class="search-bar">
            <u-icon name="search" size="16" color="#999"></u-icon>
            <input
              class="search-input"
              placeholder="搜索话题..."
              v-model="searchKeyword"
              @input="onSearchInput"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 话题列表 -->
    <scroll-view
      class="content"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="topic-grid">
        <view
          v-for="topic in filteredTopics"
          :key="topic.id"
          class="topic-card"
          @click="goTopicDetail(topic)"
        >
          <image :src="topic.cover" class="topic-cover" mode="aspectFill" />
          <view class="topic-overlay">
            <view class="topic-info">
              <text class="topic-name">#{{ topic.name }}</text>
              <text class="topic-stats">{{ topic.postCount }}条帖子 · {{ topic.followCount }}关注</text>
            </view>
            <view class="topic-actions">
              <u-button
                :type="topic.isFollowed ? 'default' : 'primary'"
                size="mini"
                :text="topic.isFollowed ? '已关注' : '关注'"
                @click.stop="toggleFollow(topic)"
              >{{ topic.isFollowed ? '已关注' : '关注' }}</u-button>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <u-icon name="loading" v-if="loading" size="16" color="#999"></u-icon>
        <text class="load-text">{{ loading ? '加载中...' : '上拉加载更多' }}</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!hasMore && allTopics.length > 0">
        <text class="no-more-text">没有更多话题了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredTopics.length === 0 && !loading">
        <u-icon name="search" size="60" color="#ccc"></u-icon>
        <text class="empty-text">暂无相关话题</text>
        <text class="empty-desc">试试其他关键词或分类</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getHotTags } from "@/utils/socialApi.js";

export default {
  name: "TopicList",
  data() {
    return {
      searchKeyword: "",
      refreshing: false,
      loading: false,
      hasMore: true,
      page: 1,
      pageSize: 20,
      allTopics: []
    };
  },
  computed: {
    filteredTopics() {
      let topics = this.allTopics;

      // 搜索筛选
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase();
        topics = topics.filter(
          topic =>
            topic.name.toLowerCase().includes(keyword) ||
            topic.description.toLowerCase().includes(keyword)
        );
      }

      return topics;
    }
  },
  onLoad() {
    this.loadTopics();
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    async loadTopics() {
      if (this.loading) return;

      this.loading = true;

      try {
        console.log("开始加载话题列表 - page:", this.page);

        // 计算实际需要的数量，第一页多加载一些
        const limit = this.page === 1 ? 50 : this.pageSize;

        // 调用API获取热门标签
        const result = await getHotTags(limit);
        console.log("话题列表API返回:", result);

        if (result && result.code === 0 && result.data) {
          console.log("🔥 话题列表API返回的原始数据:", result.data);
          result.data.forEach(tag => {
            console.log(`🔥 标签 ${tag.name} - coverImage: ${tag.coverImage}`);
          });

          const newTopics = result.data.map(tag => ({
            id: tag.id,
            name: tag.name,
            description: tag.description || `关于${tag.name}的精彩内容分享`,
            cover:
              tag.coverImage +
              "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85",
            category: this.getTagCategory(tag.name),
            postCount: tag.useCount || 0,
            followCount: Math.floor((tag.useCount || 0) * 0.3), // 模拟关注数
            isFollowed: false // 暂时设为false，后续可以调用API检查
          }));

          if (this.page === 1) {
            this.allTopics = newTopics;
          } else {
            this.allTopics = [...this.allTopics, ...newTopics];
          }

          // 判断是否还有更多数据
          this.hasMore = newTopics.length >= this.pageSize;

          console.log("话题列表加载成功 - 总数:", this.allTopics.length);
        } else {
          console.error("话题列表API返回格式不正确:", result);
          // 使用默认数据作为后备
          if (this.page === 1) {
            this.allTopics = this.getDefaultTopics();
            this.hasMore = false;
          }
        }
      } catch (error) {
        console.error("加载话题列表失败:", error);
        // 使用默认数据作为后备
        if (this.page === 1) {
          this.allTopics = this.getDefaultTopics();
          this.hasMore = false;
        }
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    // 获取标签分类
    getTagCategory(tagName) {
      const categoryMap = {
        街舞: "dance",
        爵士舞: "dance",
        芭蕾: "dance",
        现代舞: "dance",
        拉丁舞: "dance",
        民族舞: "dance",
        古典舞: "dance",
        舞蹈: "dance"
      };
      return categoryMap[tagName] || "other";
    },

    // 默认话题数据（后备方案）
    getDefaultTopics() {
      return [
        {
          id: 1,
          name: "街舞",
          description: "关于街舞的精彩内容分享",
          cover: "https://picsum.photos/300/200?random=1",
          category: "dance",
          postCount: 1234,
          followCount: 456,
          isFollowed: false
        },
        {
          id: 2,
          name: "爵士舞",
          description: "关于爵士舞的精彩内容分享",
          cover: "https://picsum.photos/300/200?random=2",
          category: "dance",
          postCount: 856,
          followCount: 234,
          isFollowed: false
        },
        {
          id: 3,
          name: "芭蕾",
          description: "关于芭蕾的精彩内容分享",
          cover: "https://picsum.photos/300/200?random=3",
          category: "dance",
          postCount: 642,
          followCount: 189,
          isFollowed: false
        },
        {
          id: 4,
          name: "现代舞",
          description: "关于现代舞的精彩内容分享",
          cover: "https://picsum.photos/300/200?random=4",
          category: "dance",
          postCount: 789,
          followCount: 267,
          isFollowed: false
        }
      ];
    },

    onRefresh() {
      this.refreshing = true;
      this.page = 1;
      this.hasMore = true;
      this.loadTopics();
    },

    loadMore() {
      if (!this.loading && this.hasMore) {
        this.page++;
        this.loadTopics();
      }
    },

    onSearchInput() {
      // 搜索防抖可以在这里实现
    },

    toggleFollow(topic) {
      topic.isFollowed = !topic.isFollowed;
      if (topic.isFollowed) {
        topic.followCount++;
        this.$u.toast("关注成功");
      } else {
        topic.followCount--;
        this.$u.toast("取消关注");
      }
    },

    goTopicDetail(topic) {
      uni.navigateTo({
        url: `/pagesSub/social/topic/detail?id=${topic.id}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.topic-list-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  padding: var(--status-bar-height) 0 0;
  border-bottom: 1rpx solid #e4e7ed;
}

.header-content {
  padding: 0 32rpx;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.nav-right {
  width: 40rpx;
}

.search-section {
  padding: 24rpx 0;
}

.search-bar {
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}

.search-input {
  flex: 1;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.content {
  margin-top: calc(140rpx + var(--status-bar-height));
  padding: 32rpx;
  width: auto;
}

.topic-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 23rpx;
}

.topic-card {
  width: calc(50% - 12rpx);
  height: 240rpx;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.topic-cover {
  width: 100%;
  height: 100%;
}

.topic-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 24rpx 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.topic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.topic-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
  display: block;
}

.topic-stats {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

.topic-actions {
  margin-top: 16rpx;
  display: flex;
  justify-content: flex-end;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  gap: 16rpx;
}

.load-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 28rpx;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin: 32rpx 0 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #ccc;
}
</style>
