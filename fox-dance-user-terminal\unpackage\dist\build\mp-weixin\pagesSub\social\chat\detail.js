(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/chat/detail"],{3277:function(e,t,n){"use strict";var o=n("c96d"),s=n.n(o);s.a},"69e4":function(e,t,n){"use strict";n.r(t);var o=n("9b1a"),s=n("a5bc");for(var i in s)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(i);n("3277");var a=n("828b"),c=Object(a["a"])(s["default"],o["b"],o["c"],!1,null,"9b674610",null,!1,o["a"],void 0);t["default"]=c.exports},"6c98":function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=o(n("7eb4")),i=o(n("ee10")),a=n("41ad"),c={name:"ChatDetail",data:function(){return{chatId:"",chatName:"",otherUserAvatar:"https://picsum.photos/100/100?random=800",isOnline:!0,messageList:[],inputText:"",scrollTop:0,showExtensions:!1,showEmojis:!1,voiceMode:!1,isRecording:!1,inputFocused:!1,isSending:!1,isTyping:!1,typingTimer:null,currentUser:{avatar:""},emojiList:["😀","😃","😄","😁","😆","😅","😂","🤣","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏"],inputCustomStyle:{backgroundColor:"transparent",fontSize:"32rpx",lineHeight:"1.4",minHeight:"40rpx",maxHeight:"200rpx",padding:"0",color:"#333"},placeholderStyle:"color: #999; font-size: 32rpx;",socketTask:null,isConnected:!1,reconnectTimer:null,reconnectCount:0,maxReconnectCount:5,heartbeatTimer:null}},onLoad:function(e){console.log("聊天页面参数:",e),e.userId?(this.chatId=e.userId,this.chatName=decodeURIComponent(e.nickname||"用户"),this.otherUserAvatar=e.avatar?decodeURIComponent(e.avatar):"https://picsum.photos/100/100?random=800"):(this.chatId=e.id,this.chatName=e.name||"聊天"),console.log("聊天对象信息:",{chatId:this.chatId,chatName:this.chatName,otherUserAvatar:this.otherUserAvatar}),this.loadCurrentUserInfo(),this.loadMessages(),this.connectWebSocket()},computed:{canSendMessage:function(){return this.inputText.trim().length>0&&this.inputText.length<=500&&!this.isSending}},onUnload:function(){this.disconnectWebSocket()},onHide:function(){this.disconnectWebSocket()},onShow:function(){this.isConnected||this.connectWebSocket()},methods:{connectWebSocket:function(){var t=this;if(!this.isConnected&&!this.socketTask){console.log("开始连接WebSocket...");var n=e.getStorageSync("userid"),o=e.getStorageSync("token");if(n&&o){console.log("📱 当前环境信息:"),console.log("- 平台:",e.getSystemInfoSync().platform),console.log("- 环境:","production");var s="".concat(this.getWebSocketUrl(),"?userId=").concat(n,"&token=").concat(o);console.log("🔗 尝试连接WebSocket:",s),this.socketTask=e.connectSocket({url:s,protocols:[],success:function(){console.log("✅ WebSocket连接请求发送成功")},fail:function(i){console.error("❌ WebSocket连接失败:",i),console.error("连接URL:",s),console.error("用户ID:",n),console.error("Token:",o),console.error("错误详情:",JSON.stringify(i)),t.testHttpConnection(),e.showToast({title:"WebSocket连接失败",icon:"none",duration:3e3}),t.scheduleReconnect()}}),this.socketTask.onOpen((function(){console.log("🎉 WebSocket连接已打开"),t.isConnected=!0,t.reconnectCount=0,t.startHeartbeat(),e.showToast({title:"连接成功",icon:"success",duration:2e3}),t.joinChatRoom()})),this.socketTask.onMessage((function(e){console.log("收到WebSocket消息:",e.data),t.handleWebSocketMessage(e.data)})),this.socketTask.onClose((function(){console.log("WebSocket连接已关闭"),t.isConnected=!1,t.stopHeartbeat(),t.scheduleReconnect()})),this.socketTask.onError((function(e){console.error("WebSocket错误:",e),t.isConnected=!1,t.scheduleReconnect()}))}else console.error("缺少用户ID或token，无法连接WebSocket")}},simulateWebSocketConnection:function(){var t=this;console.log("🔧 模拟WebSocket连接成功"),this.isConnected=!0,this.reconnectCount=0,e.showToast({title:"连接成功（模拟）",icon:"success",duration:2e3}),setTimeout((function(){t.simulateReceiveMessage()}),3e3)},simulateReceiveMessage:function(){var t={id:Date.now(),senderId:this.chatId,receiverId:e.getStorageSync("userid"),messageType:1,content:"这是一条模拟接收的消息，用于测试聊天界面功能！",createTime:(new Date).toISOString(),isRead:!1};this.handleNewMessage(t),console.log("📨 模拟接收到消息:",t)},simulateReply:function(t){var n=["收到你的消息了！","好的，我知道了","谢谢你的分享","这个想法不错","我也是这么想的",'关于"'.concat(t,'"，我觉得很有意思'),"让我想想...","同意你的观点"],o=n[Math.floor(Math.random()*n.length)],s={id:Date.now(),senderId:this.chatId,receiverId:e.getStorageSync("userid"),messageType:1,content:o,createTime:(new Date).toISOString(),isRead:!1};this.handleNewMessage(s),console.log("🤖 模拟对方回复:",o)},testHttpConnection:function(){var t="http://192.168.1.21:8101/api/";console.log("🔍 测试HTTP连接:",t),e.request({url:t,method:"GET",success:function(e){console.log("✅ HTTP连接成功:",e)},fail:function(e){console.error("❌ HTTP连接失败:",e)}})},disconnectWebSocket:function(){console.log("断开WebSocket连接"),this.isConnected=!1,this.stopHeartbeat(),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.socketTask&&(this.socketTask.close(),this.socketTask=null)},scheduleReconnect:function(){var e=this;if(this.reconnectCount>=this.maxReconnectCount)console.log("达到最大重连次数，停止重连");else{this.reconnectTimer&&clearTimeout(this.reconnectTimer);var t=Math.min(1e3*Math.pow(2,this.reconnectCount),3e4);console.log("".concat(t,"ms后尝试重连 (第").concat(this.reconnectCount+1,"次)")),this.reconnectTimer=setTimeout((function(){e.reconnectCount++,e.connectWebSocket()}),t)}},startHeartbeat:function(){var e=this;this.stopHeartbeat(),this.heartbeatTimer=setInterval((function(){e.isConnected&&e.socketTask&&e.socketTask.send({data:JSON.stringify({type:"heartbeat",timestamp:Date.now()})})}),3e4)},stopHeartbeat:function(){this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null)},getWebSocketUrl:function(){return"wss://vote.foxdance.com.cn/api/ws/chat"},joinChatRoom:function(){this.isConnected&&this.socketTask&&this.socketTask.send({data:JSON.stringify({type:"join",chatId:this.chatId,userId:e.getStorageSync("userid")})})},handleWebSocketMessage:function(e){try{var t=JSON.parse(e);switch(console.log("处理WebSocket消息:",t),t.type){case"message":this.handleNewMessage(t.data);break;case"typing":this.handleTypingStatus(t.data);break;case"read":this.handleMessageRead(t.data);break;case"online":this.handleOnlineStatus(t.data);break;default:console.log("未知消息类型:",t.type)}}catch(n){console.error("解析WebSocket消息失败:",n)}},handleNewMessage:function(e){if(e.senderId==this.chatId){var t={id:e.id,type:this.getMessageTypeString(e.messageType),content:e.content,isMine:!1,avatar:this.otherUserAvatar,timestamp:new Date(e.createTime),showTime:this.shouldShowTime(e.createTime),status:"sent"};this.messageList.push(t),this.scrollToBottom(),this.playMessageSound()}},handleTypingStatus:function(e){console.log("对方正在输入:",e)},handleMessageRead:function(e){var t=this.messageList.findIndex((function(t){return t.id===e.messageId}));-1!==t&&(this.messageList[t].status="read")},handleOnlineStatus:function(e){this.isOnline=e.isOnline},shouldShowTime:function(e){if(0===this.messageList.length)return!0;var t=this.messageList[this.messageList.length-1],n=new Date(e)-t.timestamp;return n>3e5},playMessageSound:function(){try{var t=e.createInnerAudioContext();t.src="/static/sounds/message.mp3",t.play()}catch(n){console.log("播放提示音失败:",n)}},loadCurrentUserInfo:function(){var t=e.getStorageSync("user");t&&t.data?this.currentUser.avatar=t.data.avatar?"https://file.foxdance.com.cn"+t.data.avatar:"/static/images/toux.png":this.currentUser.avatar="/static/images/toux.png",console.log("当前用户头像:",this.currentUser.avatar)},loadMessages:function(){var t=this;return(0,i.default)(s.default.mark((function n(){var o,i,c;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(console.log("开始加载聊天消息，对方用户ID:",t.chatId),n.prev=1,o=t.getCurrentUserId(),o){n.next=7;break}return console.error("当前用户ID无效，无法加载消息"),e.showToast({title:"请先登录",icon:"none"}),n.abrupt("return");case 7:return console.log("当前用户ID:",o,"对方用户ID:",t.chatId),n.next=10,(0,a.getConversationMessages)(t.chatId,{current:1,size:50});case 10:if(i=n.sent,console.log("聊天消息API返回:",i),c=[],i&&0===i.code&&i.data&&Array.isArray(i.data)?c=i.data:i&&Array.isArray(i)?c=i:(console.log("没有找到聊天消息或格式不正确"),c=[]),!(c.length>0)){n.next=21;break}return t.messageList=c.map((function(e,n){var s=c[n-1],i=!s||new Date(e.createTime)-new Date(s.createTime)>3e5;return{id:e.id,type:t.getMessageTypeString(e.messageType),content:e.content,isMine:e.senderId===o,avatar:e.senderId===o?t.currentUser.avatar:t.otherUserAvatar,timestamp:new Date(e.createTime),showTime:i,status:1===e.isRead?"read":"sent"}})).reverse(),console.log("消息列表处理完成，消息数量:",t.messageList.length),n.next=19,t.markAllMessagesAsRead();case 19:n.next=23;break;case 21:console.log("没有历史消息，显示空聊天界面"),t.messageList=[];case 23:n.next=30;break;case 25:n.prev=25,n.t0=n["catch"](1),console.error("加载消息失败:",n.t0),e.showToast({title:"消息加载失败",icon:"none"}),t.messageList=[];case 30:t.$nextTick((function(){t.scrollToBottom()}));case 31:case"end":return n.stop()}}),n,null,[[1,25]])})))()},getCurrentUserId:function(){var t=e.getStorageSync("userid");return t?Number(t):null},getMessageTypeString:function(e){return{1:"text",2:"image",3:"voice",4:"video"}[e]||"text"},markAllMessagesAsRead:function(){var e=this;return(0,i.default)(s.default.mark((function t(){var n;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.getCurrentUserId(),n){t.next=4;break}return t.abrupt("return");case 4:return t.next=6,(0,a.markMessageAsRead)({senderId:e.chatId,receiverId:n});case 6:console.log("消息已标记为已读"),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0),console.error("标记消息已读失败:",t.t0);case 12:case"end":return t.stop()}}),t,null,[[0,9]])})))()},formatMessageTime:function(e){var t=new Date(e),n=new Date,o=n-t;return o<864e5?"".concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0")):"".concat(t.getMonth()+1,"月").concat(t.getDate(),"日 ").concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))},scrollToBottom:function(){var e=this;this.$nextTick((function(){e.scrollTop=999999}))},sendMessage:function(){var t=this;return(0,i.default)(s.default.mark((function n(){var o,i,c,r,u;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.canSendMessage){n.next=2;break}return n.abrupt("return");case 2:return o=t.inputText.trim(),i=Date.now(),t.isSending=!0,t.stopTypingIndicator(),c={id:i,type:"text",content:o,isMine:!0,timestamp:new Date,showTime:t.shouldShowTime(new Date),status:"sending"},t.messageList.push(c),t.inputText="",t.scrollToBottom(),n.prev=10,r=e.getStorageSync("userid"),n.next=14,(0,a.sendMessage)({receiverId:Number(t.chatId),messageType:1,content:o});case 14:u=n.sent,console.log("发送消息API返回:",u),u&&0===u.code?(c.id=u.data.id||i,c.status="sent",t.isConnected&&t.socketTask&&(t.socketTask.send({data:JSON.stringify({type:"message",data:{id:c.id,senderId:r,receiverId:t.chatId,messageType:1,content:o,createTime:(new Date).toISOString()}})}),console.log("消息已通过WebSocket发送")),console.log("消息发送成功:",u.data)):(c.status="failed",console.error("消息发送失败:",u),e.showToast({title:(null===u||void 0===u?void 0:u.message)||"发送失败",icon:"none"})),n.next=24;break;case 19:n.prev=19,n.t0=n["catch"](10),console.error("发送消息失败:",n.t0),c.status="failed",e.showToast({title:"发送失败",icon:"none"});case 24:return n.prev=24,t.isSending=!1,n.finish(24);case 27:case"end":return n.stop()}}),n,null,[[10,19,24,27]])})))()},chooseImage:function(){var t=this;e.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:function(e){var n={id:Date.now(),type:"image",content:e.tempFilePaths[0],isMine:!0,timestamp:new Date,showTime:!1,status:"sending"};t.messageList.push(n),t.showExtensions=!1,t.scrollToBottom(),setTimeout((function(){n.status="sent"}),1e3)}})},previewImage:function(t){e.previewImage({urls:[t],current:t})},startVoiceRecord:function(){this.voiceMode=!0,this.showExtensions=!1},toggleVoiceMode:function(){this.voiceMode=!this.voiceMode},startRecord:function(){this.isRecording=!0},stopRecord:function(){if(this.isRecording){this.isRecording=!1;var e={id:Date.now(),type:"voice",content:"",duration:Math.floor(10*Math.random())+1,isMine:!0,timestamp:new Date,showTime:!1,status:"sending",isPlaying:!1};this.messageList.push(e),this.scrollToBottom(),setTimeout((function(){e.status="sent"}),500)}},cancelRecord:function(){this.isRecording=!1},playVoice:function(e){this.messageList.forEach((function(e){"voice"===e.type&&(e.isPlaying=!1)})),e.isPlaying=!0,setTimeout((function(){e.isPlaying=!1}),1e3*e.duration)},toggleEmojis:function(){this.showEmojis=!this.showEmojis,this.showExtensions=!1},toggleExtensions:function(){this.showExtensions=!this.showExtensions,this.showEmojis=!1},insertEmoji:function(e){this.inputText+=e},onInputFocus:function(){var e=this;console.log("输入框获得焦点"),this.inputFocused=!0,this.showEmojis=!1,this.showExtensions=!1,this.$nextTick((function(){e.scrollToBottom()}))},onInputBlur:function(){console.log("输入框失去焦点"),this.inputFocused=!1,this.stopTypingIndicator()},onInputChange:function(t){var n=t.detail.value;this.inputText=n,this.sendTypingIndicator(),n.length>500&&e.showToast({title:"消息长度不能超过500字",icon:"none",duration:1500})},onLineChange:function(e){console.log("输入框行数变化:",e.detail)},onInputConfirm:function(){this.canSendMessage&&this.sendMessage()},sendTypingIndicator:function(){var t=this;this.isTyping||(this.isTyping=!0,this.isConnected&&this.socketTask&&this.socketTask.send({data:JSON.stringify({type:"typing",data:{userId:e.getStorageSync("userid"),chatId:this.chatId,isTyping:!0}})}),this.typingTimer&&clearTimeout(this.typingTimer),this.typingTimer=setTimeout((function(){t.stopTypingIndicator()}),3e3))},stopTypingIndicator:function(){this.isTyping&&(this.isTyping=!1,this.typingTimer&&(clearTimeout(this.typingTimer),this.typingTimer=null),this.isConnected&&this.socketTask&&this.socketTask.send({data:JSON.stringify({type:"typing",data:{userId:e.getStorageSync("userid"),chatId:this.chatId,isTyping:!1}})}))},goBack:function(){e.navigateBack()},makeCall:function(){e.makePhoneCall({phoneNumber:"10086"})},showMoreActions:function(){var t=this;e.showActionSheet({itemList:["查看资料","清空聊天记录","举报"],success:function(e){switch(e.tapIndex){case 0:t.viewUserProfile();break;case 1:t.clearChatHistory();break;case 2:t.reportUser();break}}})},viewUserProfile:function(){e.navigateTo({url:"/pagesSub/social/user/profile?userId=".concat(this.chatId,"&name=").concat(this.chatName)})},clearChatHistory:function(){var t=this;e.showModal({title:"清空聊天记录",content:"确定要清空与该用户的所有聊天记录吗？此操作不可恢复。",confirmText:"清空",confirmColor:"#ff4757",success:function(n){n.confirm&&(t.messageList=[],e.showToast({title:"聊天记录已清空",icon:"success",duration:2e3}),t.clearChatHistoryFromServer())}})},clearChatHistoryFromServer:function(){var t=this;return(0,i.default)(s.default.mark((function n(){var o;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,(0,a.clearChatHistory)(t.chatId);case 3:o=n.sent,o&&0===o.code?console.log("服务器聊天记录已清空"):(console.error("清空聊天记录失败:",(null===o||void 0===o?void 0:o.message)||"未知错误"),e.showToast({title:(null===o||void 0===o?void 0:o.message)||"清空失败，请重试",icon:"error",duration:2e3})),n.next=11;break;case 7:n.prev=7,n.t0=n["catch"](0),console.error("清空聊天记录失败:",n.t0),e.showToast({title:"清空失败，请重试",icon:"error",duration:2e3});case 11:case"end":return n.stop()}}),n,null,[[0,7]])})))()},reportUser:function(){var t=this,n=["发送垃圾信息","发送不当内容","骚扰他人","诈骗行为","其他违规行为"];e.showActionSheet({itemList:n,success:function(e){var o=n[e.tapIndex];t.confirmReport(o)}})},confirmReport:function(t){var n=this;e.showModal({title:"举报用户",content:'确定要举报该用户"'.concat(t,'"吗？我们会认真处理您的举报。'),confirmText:"举报",confirmColor:"#ff4757",success:function(e){e.confirm&&n.submitReport(t)}})},submitReport:function(t){var n=this;return(0,i.default)(s.default.mark((function o(){var i,c;return s.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return e.showLoading({title:"提交中..."}),o.prev=1,i={reportedUserId:n.chatId,reportedUserName:n.chatName,reason:t,reportType:"chat",description:"在聊天中举报用户：".concat(t),timestamp:(new Date).toISOString()},o.next=5,(0,a.reportUser)(i);case 5:c=o.sent,e.hideLoading(),c&&0===c.code?(e.showToast({title:"举报已提交",icon:"success",duration:2e3}),console.log("举报提交成功:",c)):(console.error("举报提交失败:",(null===c||void 0===c?void 0:c.message)||"未知错误"),e.showToast({title:(null===c||void 0===c?void 0:c.message)||"提交失败，请重试",icon:"error",duration:2e3})),o.next=15;break;case 10:o.prev=10,o.t0=o["catch"](1),e.hideLoading(),console.error("举报提交失败:",o.t0),e.showToast({title:"提交失败，请重试",icon:"error",duration:2e3});case 15:case"end":return o.stop()}}),o,null,[[1,10]])})))()},loadMoreMessages:function(){},resendMessage:function(e){e.status="sending",setTimeout((function(){e.status="sent"}),500)},chooseLocation:function(){this.showExtensions=!1,this.$u.toast("位置功能开发中")},chooseFile:function(){this.showExtensions=!1,this.$u.toast("文件功能开发中")}}};t.default=c}).call(this,n("df3c")["default"])},"9b1a":function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o}));var o={uAvatar:function(){return n.e("components/uview-ui/components/u-avatar/u-avatar").then(n.bind(null,"8d35"))},uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))},uLoading:function(){return n.e("components/uview-ui/components/u-loading/u-loading").then(n.bind(null,"2bf0"))}},s=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.messageList,(function(t,n){var o=e.__get_orig(t),s=t.showTime?e.formatMessageTime(t.timestamp):null;return{$orig:o,m0:s}}))),o=e.voiceMode?null:e.inputText&&e.inputText.length>400,s=!e.voiceMode&&o?e.inputText.length:null,i=!e.voiceMode&&o?e.inputText.length:null;e.$mp.data=Object.assign({},{$root:{l0:n,g0:o,g1:s,g2:i}})},i=[]},a5bc:function(e,t,n){"use strict";n.r(t);var o=n("6c98"),s=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=s.a},c96d:function(e,t,n){},f1df:function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("cff9");o(n("3240"));var s=o(n("69e4"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(s.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["f1df","common/runtime","common/vendor"]]]);