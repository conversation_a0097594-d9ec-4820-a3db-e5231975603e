(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/editinformation"],{"076c":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n("6061"),o={data:function(){return{loding:!1,isLogined:!1,isH5:!1,avatar:"",nickname:"",rek:"",imgbaseUrl:"",baseUrl_admin:"",qjbutton:"#131315",is_store:0}},created:function(){},onLoad:function(e){this.qjbutton=t.getStorageSync("storeInfo").button,this.userData(),this.imgbaseUrl=this.$baseUrl,this.baseUrl_admin=this.$baseUrl_admin},methods:{getuserinfo:function(t){console.log(t,"获取头像昵称")},chooseAvatarsc:function(e){var n=this,o=e.detail.avatarUrl;t.showLoading({title:"加载中"}),(0,a.upImg)(o,"file",{driver:"cos"}).then((function(e){console.log("上传图片",e),1==e.code&&(t.hideLoading(),n.avatar=e.data.file.url)}))},logoutTap:function(){t.showModal({title:"温馨提示",content:"确定要退出登录吗？",success:function(e){e.confirm?(t.removeStorageSync("token"),t.removeStorageSync("userid"),t.showToast({icon:"none",title:"退出成功",duration:2e3}),setTimeout((function(){t.navigateBack()}),1e3)):e.cancel&&console.log("用户点击取消")}})},userData:function(){t.showLoading({title:"加载中"});var e=this;(0,a.userInfoApi)({}).then((function(n){1==n.code&&(console.log("个人信息",n),e.loding=!0,e.is_store=n.data.is_store,e.avatar=n.data.avatar,e.nickname=""==n.data.nickname?"微信昵称":n.data.nickname,e.rek=n.data.introduction?n.data.introduction:"",t.hideLoading())}))},UploadImg:function(){var e=this;t.chooseImage({count:1,sizeType:["original","compressed"],sourceType:["album","camera"],success:function(n){var o=n.tempFilePaths[0];t.showLoading({title:"加载中"}),(0,a.upImg)(o,"file").then((function(n){console.log("上传图片",n),1==n.code&&(t.hideLoading(),e.avatar=n.data.file.url)}))}})},sctxTap:function(){var e=this;t.chooseImage({count:1,success:function(n){var a=n.tempFilePaths;t.uploadFile({url:e.imgbaseUrl+"/api/ajax/upload",filePath:a[0],header:{server:1,bausertoken:t.getStorageSync("token")},name:"file",formData:{user:"test"},success:function(t){console.log(t);var n=JSON.parse(t.data);(n.code=1)&&(e.avatar=n.data.fullurl,console.log(n,"嘻嘻嘻哈哈123"))}})}})},tjxxTap:function(){if(0==this.nickname.split(" ").join("").length)return t.showToast({icon:"none",title:"请输入昵称",duration:2e3}),!1;t.showLoading({title:"加载中"}),(0,a.toeditUserApi)({avatar:this.avatar,nickname:this.nickname,introduction:this.rek}).then((function(e){1==e.code&&(t.hideLoading(),t.showToast({title:"修改成功",duration:2e3}),setTimeout((function(){t.navigateBack({})}),1e3))}))}}};e.default=o}).call(this,n("df3c")["default"])},"0aa4":function(t,e,n){"use strict";var a=n("6dcc"),o=n.n(a);o.a},"32d5":function(t,e,n){"use strict";n.r(e);var a=n("87b8"),o=n("8837");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("0aa4");var c=n("828b"),r=Object(c["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=r.exports},"6dcc":function(t,e,n){},"87b8":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},o=[]},8837:function(t,e,n){"use strict";n.r(e);var a=n("076c"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},e636:function(t,e,n){"use strict";(function(t,e){var a=n("47a9");n("cff9");a(n("3240"));var o=a(n("32d5"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["e636","common/runtime","common/vendor"]]]);