package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.dto.user.UserProfileUpdateRequest;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.model.entity.UserStats;
import com.yupi.springbootinit.model.vo.UserProfileVO;
import com.yupi.springbootinit.service.BaUserService;
import com.yupi.springbootinit.service.UserStatsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 用户资料接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/user")
@Slf4j
@Api(tags = "用户资料接口")
public class UserProfileController {

    @Resource
    private BaUserService baUserService;

    @Resource
    private UserStatsService userStatsService;

    /**
     * 获取用户详情
     */
    @GetMapping("/profile/{userId}")
    @ApiOperation(value = "获取用户详情")
    public BaseResponse<UserProfileVO> getUserProfile(@PathVariable Long userId) {
        if (userId == null || userId <= 0) {
            log.warn("获取用户详情请求参数错误 - userId无效: {}", userId);
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "无效的用户ID");
        }

        try {
            // 查询用户基本信息
            BaUser user = baUserService.getById(userId);
            if (user == null) {
                log.warn("用户不存在 - userId: {}", userId);
                return ResultUtils.error(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
            }

            // 查询用户统计信息
            UserStats userStats = userStatsService.getById(userId);
            if (userStats == null) {
                // 如果统计信息不存在，创建默认的
                userStats = new UserStats();
                userStats.setUserId(userId);
                userStats.setFollowingCount(0);
                userStats.setFollowerCount(0);
                userStats.setPostCount(0);
                userStats.setLikeReceivedCount(0);
                userStatsService.save(userStats);
            }

            // 构建返回对象
            UserProfileVO userProfileVO = new UserProfileVO();
            userProfileVO.setUserId(userId);
            userProfileVO.setUsername(user.getUsername());
            userProfileVO.setNickname(user.getNickname());
            userProfileVO.setSocialId(user.getSocial_id());
            userProfileVO.setAvatar(user.getAvatar());
            userProfileVO.setBio(user.getBio());
            userProfileVO.setDanceType(user.getDance_type());
            userProfileVO.setLevel(user.getLevel());
            userProfileVO.setFollowingCount(userStats.getFollowingCount());
            userProfileVO.setFollowerCount(userStats.getFollowerCount());
            userProfileVO.setPostCount(userStats.getPostCount());
            userProfileVO.setLikeReceivedCount(userStats.getLikeReceivedCount());

            log.info("获取用户详情成功 - userId: {}", userId);
            return ResultUtils.success(userProfileVO);

        } catch (Exception e) {
            log.error("获取用户详情失败 - userId: {}, error: {}", userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取用户详情失败");
        }
    }

    /**
     * 获取当前用户资料
     */
    @GetMapping("/profile")
    @ApiOperation(value = "获取当前用户资料")
    public BaseResponse<UserProfileVO> getCurrentUserProfile(HttpServletRequest request) {
        try {
            // 从请求头获取用户ID
            Long currentUserId = getCurrentUserId(request);
            if (currentUserId == null) {
                log.warn("获取当前用户资料失败 - 用户未登录");
                return ResultUtils.error(ErrorCode.NOT_LOGIN_ERROR, "用户未登录");
            }

            return getUserProfile(currentUserId);
        } catch (Exception e) {
            log.error("获取当前用户资料异常: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取用户资料失败");
        }
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/profile")
    @ApiOperation(value = "更新用户资料")
    public BaseResponse<Boolean> updateUserProfile(@RequestBody UserProfileUpdateRequest updateRequest,
            HttpServletRequest request) {
        if (updateRequest == null) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "请求参数不能为空");
        }

        try {
            // 从请求头获取用户ID
            Long currentUserId = getCurrentUserId(request);
            if (currentUserId == null) {
                log.warn("更新用户资料失败 - 用户未登录");
                return ResultUtils.error(ErrorCode.NOT_LOGIN_ERROR, "用户未登录");
            }

            log.info("开始更新用户资料 - userId: {}, updateRequest: {}", currentUserId, updateRequest);

            // 查询用户是否存在
            BaUser user = baUserService.getById(currentUserId);
            if (user == null) {
                log.warn("更新用户资料失败 - 用户不存在: {}", currentUserId);
                return ResultUtils.error(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
            }

            // 使用UpdateWrapper只更新需要更新的字段
            boolean result = baUserService.updateUserProfile(currentUserId, updateRequest);

            if (result) {
                log.info("更新用户资料成功 - userId: {}, updateFields: {}", currentUserId, getUpdateFields(updateRequest));
                return ResultUtils.success(true);
            } else {
                log.warn("更新用户资料失败 - userId: {}", currentUserId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "更新用户资料失败");
            }

        } catch (Exception e) {
            log.error("更新用户资料异常 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "更新用户资料失败");
        }
    }

    /**
     * 获取更新字段信息（用于日志）
     */
    private String getUpdateFields(UserProfileUpdateRequest updateRequest) {
        StringBuilder fields = new StringBuilder();
        if (updateRequest.getNickname() != null)
            fields.append("nickname,");
        if (updateRequest.getSocialId() != null)
            fields.append("socialId,");
        if (updateRequest.getBio() != null)
            fields.append("bio,");
        if (updateRequest.getDanceType() != null)
            fields.append("danceType,");
        if (updateRequest.getAvatar() != null)
            fields.append("avatar,");
        return fields.length() > 0 ? fields.substring(0, fields.length() - 1) : "none";
    }

    /**
     * 获取用户统计数据
     */
    @GetMapping("/stats/{userId}")
    @ApiOperation(value = "获取用户统计数据")
    public BaseResponse<UserStats> getUserStats(@PathVariable Long userId) {
        if (userId == null || userId <= 0) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "无效的用户ID");
        }

        try {
            UserStats userStats = userStatsService.getById(userId);
            if (userStats == null) {
                // 如果统计信息不存在，返回默认值
                userStats = new UserStats();
                userStats.setUserId(userId);
                userStats.setFollowingCount(0);
                userStats.setFollowerCount(0);
                userStats.setPostCount(0);
                userStats.setLikeReceivedCount(0);
            }

            return ResultUtils.success(userStats);

        } catch (Exception e) {
            log.error("获取用户统计数据失败 - userId: {}, error: {}", userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取用户统计数据失败");
        }
    }

    /**
     * 从请求头获取当前用户ID
     *
     * @param request HTTP请求
     * @return 用户ID，未登录返回null
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        try {
            // 从请求头中获取用户ID
            String userIdStr = request.getHeader("userid");
            if (userIdStr != null && !userIdStr.trim().isEmpty()) {
                try {
                    Long userId = Long.parseLong(userIdStr);
                    log.debug("从请求头获取用户ID成功: {}", userId);
                    return userId;
                } catch (NumberFormatException e) {
                    log.warn("解析用户ID失败: {}", userIdStr);
                }
            }

            // 如果请求头中没有userid，尝试从token中获取（这里暂时返回null）
            String token = request.getHeader("bausertoken");
            if (token != null && !token.trim().isEmpty()) {
                // TODO: 实际项目中应该解析token获取用户ID
                log.warn("请求头中有token但没有userid，需要实现token解析逻辑");
            }

            log.warn("无法获取用户ID - 请求头中没有有效的userid");
            return null;
        } catch (Exception e) {
            log.error("获取当前用户ID失败: {}", e.getMessage());
            return null;
        }
    }
}
