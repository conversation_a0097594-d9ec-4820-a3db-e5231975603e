<template>
  <view class="profile-container">
    <!-- 蓝色渐变背景头部 -->
    <view class="header-section">
      <view class="header-bg"></view>

      <!-- 顶部操作按钮 -->
      <view class="header-actions">
        <u-icon name="scan" color="#fff" size="48rpx" @click="scanCode"></u-icon>
        <u-icon name="setting" color="#fff" size="48rpx" @click="goSettings"></u-icon>
      </view>

      <!-- 用户信息 -->
      <view class="user-info-section">
        <view class="user-avatar-container">
          <u-avatar :src="userInfo.avatar" size="120" @click="editAvatar"></u-avatar>
        </view>

        <!-- 用户信息内容容器 -->
        <view class="user-info-content">
          <!-- 加载状态 -->
          <view v-if="loading" class="loading-container">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
          </view>

          <!-- 用户信息 -->
          <view v-else class="user-info-row">
            <!-- 左侧用户信息 -->
            <view class="user-details">
              <text class="nickname">{{ userInfo.nickname || '加载中...' }}</text>
              <text class="social-id">社区ID：{{ userInfo.socialId || '--' }}</text>
              <text class="dance-type">舞种：{{ userInfo.danceType || '--' }}</text>
              <text class="bio">{{ userInfo.bio || '暂无个人简介' }}</text>
            </view>

            <!-- 右侧编辑链接 -->
            <view class="edit-section">
              <text class="edit-link" @click="editProfile">编辑资料</text>
            </view>
          </view>

          <!-- 数据统计 -->
          <view class="stats-row">
            <view class="stat-item" @click="switchTab(0)">
              <text class="stat-number">{{ userInfo.postCount }}</text>
              <text class="stat-label">帖子</text>
            </view>
            <view class="stat-item" @click="switchTab(2)">
              <text class="stat-number">{{ userInfo.followingCount }}</text>
              <text class="stat-label">关注</text>
            </view>
            <view class="stat-item" @click="switchTab(3)">
              <text class="stat-number">{{ userInfo.followersCount }}</text>
              <text class="stat-label">粉丝</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ userInfo.likeCount }}</text>
              <text class="stat-label">获赞</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <scroll-view class="content" scroll-y>
      <!-- 操作按钮 -->
      <view class="tabs-container">
        <u-tabs
          :list="tabs"
          :current="currentTab"
          @change="switchTab"
          lineWidth="30"
          lineColor="#303133"
          :activeStyle="{ color: '#303133', fontWeight: 'bold' }"
          :inactiveStyle="{ color: '#606266' }"
        ></u-tabs>
      </view>

      <!-- 内容列表 -->
      <view class="posts-content">
        <view v-if="tabs[currentTab] && tabs[currentTab].data.length > 0">
          <!-- 帖子列表 (作品、私密作品、喜欢) -->
          <view v-if="currentTab === 0 || currentTab === 1 || currentTab === 4" class="post-grid">
            <PostCard
              v-for="post in tabs[currentTab].data"
              :key="post.id"
              :post="post"
              class="post-card-item"
              @click="goPostDetail"
              @user-click="goUserProfile"
              @like="onPostLike"
            />
          </view>

          <!-- 用户列表 (关注、粉丝) -->
          <view v-else-if="currentTab === 2 || currentTab === 3" class="user-list">
            <view
              v-for="user in tabs[currentTab].data"
              :key="user.id"
              class="user-item"
              @click="goUserProfile(user)"
            >
              <u-avatar :src="user.avatar" size="80" class="user-avatar"></u-avatar>
              <view class="user-info">
                <text class="user-nickname">{{ user.nickname }}</text>
                <text class="user-bio">{{ user.bio }}</text>
                <text class="user-stats">{{ user.followersCount }} 粉丝</text>
              </view>
              <view class="user-action">
                <FollowButton
                  :user="{ id: user.id, nickname: user.nickname }"
                  :followed="user.isFollowed"
                  size="mini"
                  @follow="onUserFollow"
                  @unfollow="onUserUnfollow"
                  @change="onFollowChange"
                  @click.stop
                />
              </view>
            </view>
          </view>
        </view>

        <view v-else class="empty-state">
          <u-empty mode="list" :text="getEmptyText()"></u-empty>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import PostCard from "../components/PostCard.vue";
import FollowButton from "../components/FollowButton.vue";
import {
  getUserProfile,
  updateUserProfile,
  getPostList,
  likePost,
  unlikePost,
  getFollowingList,
  getFollowersList,
  getUserPostStats
} from "@/utils/socialApi.js";

export default {
  name: "SocialProfile",
  components: {
    PostCard,
    FollowButton
  },
  data() {
    return {
      userInfo: {
        userId: "",
        nickname: "",
        avatar: "",
        bio: "",
        danceType: "",
        postCount: 0,
        privatePostCount: 0, // 私密作品数
        followingCount: 0,
        followersCount: 0,
        likeCount: 0,
        draftCount: 0
      },
      loading: true,
      currentTab: 0,
      isInitialized: false,
      tabs: [
        { name: "作品", data: [], loading: false },
        { name: "私密作品", data: [], loading: false },
        { name: "关注", data: [], loading: false },
        { name: "粉丝", data: [], loading: false },
        { name: "喜欢", data: [], loading: false }
      ]
    };
  },
  onLoad() {
    console.log("Profile页面 onLoad");
    // 延迟加载，确保页面完全初始化
    this.$nextTick(() => {
      setTimeout(() => {
        this.initializeData();
      }, 100);
    });
  },
  onShow() {
    console.log("Profile页面 onShow");
    // 页面显示时刷新数据
    if (this.isInitialized) {
      this.loadUserInfo();
    }
  },
  methods: {
    // 初始化数据
    async initializeData() {
      console.log("初始化Profile页面数据...");
      try {
        await this.loadUserInfo();
        this.isInitialized = true;
      } catch (error) {
        console.error("初始化数据失败:", error);
      }
    },

    async loadUserInfo() {
      try {
        // 获取当前用户ID
        const currentUserId = this.getCurrentUserId();
        if (!currentUserId) {
          console.error("用户未登录，无法加载用户信息");
          uni.showToast({
            title: "请先登录",
            icon: "none"
          });
          return;
        }

        console.log("开始加载用户信息 - userId:", currentUserId);
        this.loading = true;

        const result = await getUserProfile(currentUserId);
        console.log("用户信息API返回:", result);

        if (result && result.code === 0 && result.data) {
          const userProfile = result.data;
          this.userInfo = {
            userId: userProfile.id || userProfile.userId || currentUserId,
            nickname: userProfile.nickname || "舞蹈爱好者",
            avatar: this.formatAvatarUrl(userProfile.avatar),
            bio:
              userProfile.bio ||
              userProfile.userProfile ||
              "热爱舞蹈，享受生活",
            danceType: userProfile.danceType || "街舞",
            postCount: userProfile.postCount || 0,
            privatePostCount: 0, // 先设为0，后面会更新
            followingCount: userProfile.followingCount || 0,
            followersCount:
              userProfile.followersCount || userProfile.followerCount || 0,
            likeCount:
              userProfile.likeReceivedCount || userProfile.likeCount || 0,
            draftCount: userProfile.draftCount || 0
          };

          console.log("用户信息加载成功:", this.userInfo);

          // 加载私密作品数量
          await this.loadPrivatePostCount(currentUserId);

          // 加载用户帖子数据
          this.loadTabData(this.currentTab);
        } else {
          console.error("用户信息API返回格式不正确:", result);
          uni.showToast({
            title: "加载用户信息失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("加载用户信息失败:", error);
        uni.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载私密作品数量
    async loadPrivatePostCount(userId) {
      try {
        // 使用getPostList API获取私密作品数量
        const result = await getPostList({
          current: 1,
          size: 1, // 只需要获取数量，不需要具体数据
          userId: userId,
          isPublic: 0 // 只查询私密作品
        });

        if (result && result.code === 0 && result.data) {
          // 如果返回的是分页对象，获取total字段
          if (result.data.total !== undefined) {
            this.userInfo.privatePostCount = result.data.total;
          } else if (Array.isArray(result.data)) {
            // 如果返回的是数组，需要再次查询获取准确数量
            // 这里暂时设为数组长度，实际应该有total字段
            this.userInfo.privatePostCount = result.data.length;
          }
        }

        console.log("私密作品数量:", this.userInfo.privatePostCount);
      } catch (error) {
        console.error("加载私密作品数量失败:", error);
        this.userInfo.privatePostCount = 0;
      }
    },

    // 获取当前用户ID
    getCurrentUserId() {
      const userId = uni.getStorageSync("userid");
      return userId ? Number(userId) : null;
    },

    // 格式化头像URL
    formatAvatarUrl(avatar) {
      if (avatar) {
        return (
          "https://file.foxdance.com.cn" +
          avatar +
          "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85"
        );
      }
      return "static/images/toux.png";
    },

    async loadTabData(tabIndex) {
      if (this.tabs[tabIndex].loading) return;

      this.$set(this.tabs[tabIndex], "loading", true);

      try {
        let data = [];

        switch (tabIndex) {
          case 0: // 作品
            data = await this.loadUserPosts();
            break;
          case 1: // 私密作品
            data = await this.loadPrivatePosts();
            break;
          case 2: // 关注
            data = await this.loadFollowingUsers();
            break;
          case 3: // 粉丝
            data = await this.loadFollowersUsers();
            break;
          case 4: // 喜欢
            data = await this.loadLikedPosts();
            break;
        }

        this.$set(this.tabs[tabIndex], "data", data);
      } catch (error) {
        console.error(`加载标签页${tabIndex}数据失败:`, error);
        this.$set(this.tabs[tabIndex], "data", []);
      } finally {
        this.$set(this.tabs[tabIndex], "loading", false);
      }
    },

    // 加载用户发布的公开帖子
    async loadUserPosts() {
      try {
        const currentUserId = this.getCurrentUserId();
        if (!currentUserId) {
          console.error("用户未登录，无法加载帖子");
          return [];
        }

        const result = await getPostList({
          current: 1,
          size: 20,
          userId: currentUserId,
          isPublic: 1, // 只加载公开帖子
          sortField: "createTime",
          sortOrder: "desc"
        });

        console.log("用户帖子API返回:", result);

        if (result && result.code === 0 && result.data) {
          const posts = Array.isArray(result.data)
            ? result.data
            : result.data.records || [];
          return posts.map(post => ({
            id: post.id,
            title: post.title || "",
            coverImage: post.coverImage,
            username: post.nickname || this.userInfo.nickname,
            userAvatar:
              "https://file.foxdance.com.cn" +
              post.avatar +
              "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85",
            content: post.content,
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            isLiked: post.isLiked || false,
            isPublic: post.isPublic, // 添加私密状态字段
            status: post.status, // 添加帖子状态字段
            createTime: new Date(post.createTime)
          }));
        } else {
          console.log("用户帖子API返回格式不正确:", result);
          return [];
        }
      } catch (error) {
        console.error("加载用户帖子失败:", error);
        return [];
      }
    },

    // 加载用户发布的私密帖子
    async loadPrivatePosts() {
      try {
        const currentUserId = this.getCurrentUserId();
        if (!currentUserId) {
          console.error("用户未登录，无法加载私密帖子");
          return [];
        }

        const result = await getPostList({
          current: 1,
          size: 20,
          userId: currentUserId,
          isPublic: 0, // 只加载私密帖子
          sortField: "createTime",
          sortOrder: "desc"
        });

        console.log("用户私密帖子API返回:", result);

        if (result && result.code === 0 && result.data) {
          const posts = Array.isArray(result.data)
            ? result.data
            : result.data.records || [];
          return posts.map(post => ({
            id: post.id,
            title: post.title || "",
            coverImage: post.coverImage,
            username: post.nickname || this.userInfo.nickname,
            userAvatar: "https://file.foxdance.com.cn" + post.avatar,
            content: post.content,
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            isLiked: post.isLiked || false,
            isPublic: post.isPublic, // 私密帖子标识
            status: post.status,
            createTime: new Date(post.createTime)
          }));
        } else {
          console.log("用户私密帖子API返回格式不正确:", result);
          return [];
        }
      } catch (error) {
        console.error("加载用户私密帖子失败:", error);
        return [];
      }
    },

    // 加载用户点赞的帖子
    async loadLikedPosts() {
      try {
        const currentUserId = this.getCurrentUserId();
        if (!currentUserId) {
          console.error("用户未登录，无法加载点赞帖子");
          return [];
        }

        // TODO: 实现获取用户点赞帖子的API
        console.log("加载用户点赞的帖子 - userId:", currentUserId);
        // 暂时返回空数组，等待后端API实现
        return [];
      } catch (error) {
        console.error("加载点赞帖子失败:", error);
        return [];
      }
    },

    // 加载关注的用户
    async loadFollowingUsers() {
      try {
        const currentUserId = this.getCurrentUserId();
        if (!currentUserId) {
          console.error("用户未登录，无法加载关注列表");
          return [];
        }

        console.log("开始加载关注用户列表...");
        const result = await getFollowingList(currentUserId, {
          current: 1,
          size: 50
        });
        console.log("关注用户API返回:", result);

        if (result && result.code === 0 && result.data) {
          const users = Array.isArray(result.data)
            ? result.data
            : result.data.records || [];
          return users.map(user => ({
            id: user.userId || user.id,
            userId: user.userId || user.id, // 确保有userId字段
            nickname: user.nickname || "用户",
            avatar: this.formatAvatarUrl(user.avatar),
            bio: user.bio || "暂无简介",
            danceType: user.danceType || "",
            followersCount: user.followerCount || 0,
            isFollowed: true, // 关注列表中的用户都是已关注的
            type: "user" // 标识这是用户类型数据
          }));
        } else {
          console.error("关注用户API返回格式不正确:", result);
          return [];
        }
      } catch (error) {
        console.error("加载关注用户失败:", error);
        return [];
      }
    },

    // 加载粉丝用户
    async loadFollowersUsers() {
      try {
        const currentUserId = this.getCurrentUserId();
        if (!currentUserId) {
          console.error("用户未登录，无法加载粉丝列表");
          return [];
        }

        console.log("开始加载粉丝用户列表...");
        const result = await getFollowersList(currentUserId, {
          current: 1,
          size: 50
        });
        console.log("粉丝用户API返回:", result);

        if (result && result.code === 0 && result.data) {
          const users = Array.isArray(result.data)
            ? result.data
            : result.data.records || [];
          return users.map(user => ({
            id: user.userId || user.id,
            userId: user.userId || user.id, // 确保有userId字段
            nickname: user.nickname || "用户",
            avatar: this.formatAvatarUrl(user.avatar),
            bio: user.bio || "暂无简介",
            danceType: user.danceType || "",
            followersCount: user.followerCount || 0,
            isFollowed: user.isFollowed || false, // 需要检查是否互相关注
            type: "user" // 标识这是用户类型数据
          }));
        } else {
          console.error("粉丝用户API返回格式不正确:", result);
          return [];
        }
      } catch (error) {
        console.error("加载粉丝用户失败:", error);
        return [];
      }
    },

    switchTab(item) {
      const index = typeof item === "object" ? item.index : item;
      if (this.currentTab === index) return;
      this.currentTab = index;
      this.loadTabData(index);
    },

    scanCode() {
      uni.scanCode({
        success: res => {
          console.log("扫码结果:", res);
        }
      });
    },

    goSettings() {
      uni.navigateTo({
        url: "/pagesSub/social/settings/index"
      });
    },

    editAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: res => {
          // 上传头像
          this.userInfo.avatar = res.tempFilePaths[0];
        }
      });
    },

    editProfile() {
      uni.navigateTo({
        url: "/pagesSub/social/profile/edit"
      });
    },

    goLikeList() {
      uni.navigateTo({
        url: "/pagesSub/social/like/list"
      });
    },

    viewPost(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      });
    },

    // PostCard组件需要的方法
    goPostDetail(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      });
    },

    goUserProfile(data) {
      // 判断是帖子数据还是用户数据
      if (data.type === "user") {
        // 用户数据，直接跳转
        const userId = data.id || data.userId;
        if (userId) {
          uni.navigateTo({
            url: `/pagesSub/social/user/profile?userId=${userId}&name=${data.nickname}`
          });
        }
      } else {
        // 帖子数据，跳转到帖子作者页面
        // 如果是自己的帖子，不需要跳转
        if (data.username === this.userInfo.nickname) return;

        uni.navigateTo({
          url: `/pagesSub/social/user/profile?id=${data.userId || data.id}`
        });
      }
    },

    async onPostLike(post) {
      try {
        if (post.isLiked) {
          // 取消点赞
          await unlikePost(post.id);
          post.isLiked = false;
          post.likeCount = Math.max(0, post.likeCount - 1);
          uni.showToast({
            title: "取消点赞",
            icon: "none",
            duration: 1000
          });
        } else {
          // 点赞
          await likePost(post.id);
          post.isLiked = true;
          post.likeCount += 1;
          uni.showToast({
            title: "点赞成功",
            icon: "success",
            duration: 1000
          });
        }

        // 更新对应标签页中的帖子数据
        const currentTabData = this.tabs[this.currentTab].data;
        const index = currentTabData.findIndex(p => p.id === post.id);
        if (index !== -1) {
          this.$set(currentTabData, index, { ...post });
        }
      } catch (error) {
        console.error("点赞操作失败:", error);
        uni.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    },

    // 强制刷新页面数据（供父组件调用）
    forceRefresh() {
      console.log("强制刷新个人资料页面数据...");
      // 重置数据状态
      this.isInitialized = false;
      this.loading = true;

      // 清空当前数据
      this.userInfo = {
        userId: "",
        nickname: "",
        avatar: "",
        bio: "",
        danceType: "",
        postCount: 0,
        followingCount: 0,
        followersCount: 0,
        likeCount: 0,
        draftCount: 0
      };

      // 清空tabs数据
      this.tabs.forEach(tab => {
        tab.data = [];
        tab.loading = false;
      });

      // 重新初始化数据
      this.initializeData();
    },

    // 获取空状态文本
    getEmptyText() {
      const tabNames = [
        "暂无作品",
        "暂无私密作品",
        "暂无关注",
        "暂无粉丝",
        "暂无喜欢"
      ];
      return tabNames[this.currentTab] || "暂无内容";
    },

    // 用户关注成功事件
    onUserFollow(data) {
      console.log("用户关注成功:", data);
      // 更新本地数据
      this.updateUserFollowStatus(data.user.id, true);

      // 更新关注数统计
      this.userInfo.followingCount += 1;
    },

    // 用户取消关注成功事件
    onUserUnfollow(data) {
      console.log("用户取消关注成功:", data);
      // 更新本地数据
      this.updateUserFollowStatus(data.user.id, false);

      // 更新关注数统计
      if (this.userInfo.followingCount > 0) {
        this.userInfo.followingCount -= 1;
      }
    },

    // 关注状态变化事件
    onFollowChange(data) {
      console.log("关注状态变化:", data);
      // 更新本地数据
      this.updateUserFollowStatus(data.user.id, data.isFollowed);
    },

    // 更新用户关注状态的辅助方法
    updateUserFollowStatus(userId, isFollowed) {
      // 更新关注列表中的状态
      if (this.tabs[2] && this.tabs[2].data) {
        const followingUser = this.tabs[2].data.find(u => u.id === userId);
        if (followingUser) {
          followingUser.isFollowed = isFollowed;
        }
      }

      // 更新粉丝列表中的状态
      if (this.tabs[3] && this.tabs[3].data) {
        const followerUser = this.tabs[3].data.find(u => u.id === userId);
        if (followerUser) {
          followerUser.isFollowed = isFollowed;
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 100rpx;
}

.header-section {
  position: relative;
  background: #fff;
}

.header-bg {
  height: 400rpx;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
}

.header-actions {
  position: absolute;
  top: 60rpx;
  right: 32rpx;
  display: flex;
  gap: 32rpx;
  z-index: 10;
}

.user-info-section {
  padding: 40rpx 50rpx;
  background: #f8f9fa;
}

.user-info-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 50rpx;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.user-avatar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
  position: absolute;
  top: 340rpx;
  left: 12%;
}

.user-info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 20rpx 40rpx;
}

.user-details {
  flex: 1;
  text-align: left;
}

.edit-section {
  flex-shrink: 0;
  margin-left: 20rpx;
  display: flex;
  align-items: flex-start;
}

.edit-link {
  font-size: 28rpx;
  font-weight: 500;
  border: 1rpx solid #2979ff;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  margin: 10rpx;
  color: #2979ff;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}



.social-id {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.dance-type {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.bio {
  font-size: 28rpx;
  color: #999;
  line-height: 1.4;
  display: block;
}

.stats-row {
  display: flex;
  justify-content: center;
  gap: 80rpx;
  margin-bottom: 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.tabs-container {
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.content {
  background: #fff;
  min-height: 60vh;
}

.posts-content {
  padding: 32rpx;
}

.post-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 14rpx;
}

.post-card-item {
  width: calc(50% - 8rpx);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

/* Loading 相关样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 用户列表样式 */
.user-list {
  padding: 0 32rpx;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  margin-right: 24rpx;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-nickname {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.user-bio {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-stats {
  font-size: 24rpx;
  color: #999;
}

.user-action {
  margin-left: 24rpx;
}
</style>
