<template>
  <view class="message-detail-container">

    <!-- 消息内容 -->
    <scroll-view class="content-scroll" scroll-y>
      <view class="message-detail">
        <!-- 消息头部 -->
        <view class="message-header">
          <view class="message-icon" :class="'icon-' + messageDetail.type">
            <u-icon :name="getIconName(messageDetail.type)" size="24" color="#fff"></u-icon>
          </view>
          <view class="header-info">
            <text class="message-title">{{ messageDetail.title }}</text>
            <text class="message-time">{{ formatTime(messageDetail.createTime) }}</text>
          </view>
        </view>

        <!-- 消息正文 -->
        <view class="message-body">
          <rich-text :nodes="messageDetail.htmlContent || messageDetail.content"></rich-text>
        </view>

        <!-- 相关操作按钮 -->
        <view v-if="messageDetail.actions && messageDetail.actions.length > 0" class="action-buttons">
          <view 
            v-for="action in messageDetail.actions" 
            :key="action.id"
            class="action-btn"
            :class="action.type"
            @click="handleAction(action)"
          >
            <text class="btn-text">{{ action.text }}</text>
          </view>
        </view>

        <!-- 附件列表 -->
        <view v-if="messageDetail.attachments && messageDetail.attachments.length > 0" class="attachments">
          <text class="section-title">相关附件</text>
          <view 
            v-for="attachment in messageDetail.attachments" 
            :key="attachment.id"
            class="attachment-item"
            @click="openAttachment(attachment)"
          >
            <u-icon name="file-text" size="20" color="#2979ff"></u-icon>
            <text class="attachment-name">{{ attachment.name }}</text>
            <u-icon name="arrow-right" size="16" color="#999"></u-icon>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'MessageDetail',
  data() {
    return {
      messageId: '',
      messageDetail: {
        id: 1,
        type: 'system',
        title: '系统维护通知',
        content: '系统将于今晚23:00-次日1:00进行维护升级，期间可能影响部分功能使用，请您提前做好准备。',
        htmlContent: `
          <div style="line-height: 1.6; color: #333;">
            <p>尊敬的用户：</p>
            <p>为了给您提供更好的服务体验，我们将对系统进行维护升级。</p>
            <p><strong>维护时间：</strong>今晚23:00-次日1:00</p>
            <p><strong>影响范围：</strong></p>
            <ul>
              <li>视频上传功能暂时不可用</li>
              <li>消息推送可能延迟</li>
              <li>部分页面可能无法正常访问</li>
            </ul>
            <p><strong>建议：</strong></p>
            <ul>
              <li>请提前保存您的创作内容</li>
              <li>避免在维护期间进行重要操作</li>
            </ul>
            <p>感谢您的理解与支持！</p>
            <p style="text-align: right; margin-top: 20px;">Fox Dance 团队<br/>2024年7月16日</p>
          </div>
        `,
        createTime: new Date(Date.now() - 3600000),
        isRead: true,
        actions: [
          {
            id: 1,
            type: 'primary',
            text: '我知道了',
            action: 'confirm'
          },
          {
            id: 2,
            type: 'secondary',
            text: '查看更多',
            action: 'more',
            url: 'https://help.foxdance.com'
          }
        ],
        attachments: [
          {
            id: 1,
            name: '维护详情说明.pdf',
            url: 'https://example.com/maintenance.pdf',
            size: '2.5MB'
          }
        ]
      }
    }
  },
  onLoad(options) {
    this.messageId = options.id
    this.loadMessageDetail()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },

    getIconName(type) {
      const iconMap = {
        system: 'setting',
        activity: 'volume',
        security: 'shield'
      }
      return iconMap[type] || 'bell'
    },

    formatTime(time) {
      const date = new Date(time)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    async loadMessageDetail() {
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 标记消息为已读
        this.markAsRead()
      } catch (error) {
        console.error('加载消息详情失败:', error)
        this.$u.toast('加载失败，请重试')
      }
    },

    markAsRead() {
      // 调用API标记消息为已读
      console.log('标记消息已读:', this.messageId)
    },

    handleAction(action) {
      switch (action.action) {
        case 'confirm':
          this.$u.toast('已确认')
          setTimeout(() => {
            this.goBack()
          }, 1000)
          break
        case 'more':
          if (action.url) {
            // 在小程序中打开网页
            uni.navigateTo({
              url: `/pages/webview/index?url=${encodeURIComponent(action.url)}`
            })
          }
          break
        default:
          console.log('未知操作:', action)
      }
    },

    openAttachment(attachment) {
      // 下载或预览附件
      uni.downloadFile({
        url: attachment.url,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log('打开文档成功')
              },
              fail: (err) => {
                console.error('打开文档失败:', err)
                this.$u.toast('无法打开此文件')
              }
            })
          }
        },
        fail: (err) => {
          console.error('下载失败:', err)
          this.$u.toast('下载失败')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.message-detail-container {
  //height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header {
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.header-left, .header-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-start;
}

.header-title {
  flex: 1;
  display: flex;
  justify-content: center;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.content-scroll {
  flex: 1;
}

.message-detail {
  background: #fff;
  margin: 32rpx;
  border-radius: 24rpx;
  overflow: hidden;
}

.message-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.message-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.icon-system {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-activity {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.icon-security {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.header-info {
  flex: 1;
}

.message-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.message-time {
  font-size: 24rpx;
  color: #999;
}

.message-body {
  padding: 32rpx;
  line-height: 1.6;
  font-size: 28rpx;
  color: #333;
}

.action-buttons {
  padding: 0 32rpx 32rpx;
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: #2979ff;
}

.action-btn.primary .btn-text {
  color: #fff;
}

.action-btn.secondary {
  background: #f5f5f5;
  border: 2rpx solid #e4e7ed;
}

.action-btn.secondary .btn-text {
  color: #666;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

.attachments {
  padding: 32rpx;
  border-top: 2rpx solid #f5f5f5;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}

.attachment-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
}
</style>
