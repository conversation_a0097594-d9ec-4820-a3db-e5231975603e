(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/main/index"],{"032f":function(e,t,n){"use strict";var o=n("3797"),a=n.n(o);a.a},"0440":function(e,t,n){"use strict";var o=n("f323"),a=n.n(o);a.a},"161b":function(e,t,n){"use strict";n.r(t);var o=n("8276"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},"25cf":function(e,t,n){},3427:function(e,t,n){"use strict";var o=n("f33b"),a=n.n(o);a.a},3797:function(e,t,n){},4173:function(e,t,n){"use strict";n.r(t);var o=n("6e3f"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},5129:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o}));var o={uAvatar:function(){return n.e("components/uview-ui/components/u-avatar/u-avatar").then(n.bind(null,"8d35"))},uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))},uPopup:function(){return n.e("components/uview-ui/components/u-popup/u-popup").then(n.bind(null,"b525"))},uInput:function(){return Promise.all([n.e("common/vendor"),n.e("components/uview-ui/components/u-input/u-input")]).then(n.bind(null,"d67d"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.postTitle.length),o=e.postContent.length,a=e.selectedImages.length,r=e.selectedTopics.length,s=r?e.selectedTopics.map((function(e){return"#"+e})).join(" "):null,i=e.__map(e.filteredTopics,(function(t,n){var o=e.__get_orig(t),a=e.selectedTopics.includes(t.name);return{$orig:o,g5:a}}));e._isMounted||(e.e0=function(t){e.showTopicModal=!1},e.e1=function(t){e.showLocationModal=!1}),e.$mp.data=Object.assign({},{$root:{g0:n,g1:o,g2:a,g3:r,g4:s,l0:i}})},r=[]},5180:function(e,t,n){"use strict";n.r(t);var o=n("8527"),a=n("b913");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("032f");var s=n("828b"),i=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"1392d4d6",null,!1,o["a"],void 0);t["default"]=i.exports},5899:function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a,r=o(n("7eb4")),s=o(n("7ca3")),i=o(n("ee10")),c=n("41ad"),u={name:"SocialDiscover",components:{FollowButton:function(){n.e("pagesSub/social/components/FollowButton").then(function(){return resolve(n("607e"))}.bind(null,n)).catch(n.oe)}},data:function(){return{hotTopics:[],recommendUsers:[],hotPosts:[],featuredContent:[]}},onLoad:function(){this.loadDiscoverData()},onShow:function(){this.hotTopics&&0!==this.hotTopics.length||(console.log("发现页显示时重新加载数据"),this.loadDiscoverData())},activated:function(){this.hotTopics&&0!==this.hotTopics.length||(console.log("发现页激活时重新加载数据"),this.loadDiscoverData())},methods:(a={loadDiscoverData:function(){this.loadHotTopics(),this.loadRecommendUsers(),this.loadHotPosts(),this.loadFeaturedContent()},loadHotTopics:function(){var e=this;return(0,i.default)(r.default.mark((function t(){var n;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,console.log("开始加载热门话题..."),t.next=4,(0,c.getHotTopics)({limit:4});case 4:n=t.sent,console.log("热门话题API返回:",n),n&&0===n.code&&n.data?(console.log("🔥 API返回的原始标签数据:",n.data),n.data.forEach((function(e){console.log("🔥 标签 ".concat(e.name," - coverImage: ").concat(e.coverImage))})),e.hotTopics=n.data.map((function(t){return{id:t.id,name:t.name,cover:t.coverImage||e.getDefaultCoverImage(t),postCount:t.useCount||0,description:t.description}})),console.log("🔥 处理后的热门话题:",e.hotTopics)):(console.error("热门话题API返回格式不正确:",n),e.hotTopics=e.getDefaultTopics()),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),console.error("加载热门话题失败:",t.t0),e.hotTopics=e.getDefaultTopics();case 13:case"end":return t.stop()}}),t,null,[[0,9]])})))()},getDefaultCoverImage:function(e){return{"街舞":"https://file.foxdance.com.cn/tags/street-dance.jpg","爵士舞":"https://file.foxdance.com.cn/tags/jazz-dance.jpg","芭蕾":"https://file.foxdance.com.cn/tags/ballet.jpg","现代舞":"https://file.foxdance.com.cn/tags/modern-dance.jpg","拉丁舞":"https://file.foxdance.com.cn/tags/latin-dance.jpg","民族舞":"https://file.foxdance.com.cn/tags/folk-dance.jpg"}[e.name]||"https://picsum.photos/200/120?random=".concat(e.id)},getDefaultTopics:function(){return[{id:1,name:"街舞",cover:"https://picsum.photos/200/120?random=1",postCount:1234},{id:2,name:"爵士舞",cover:"https://picsum.photos/200/120?random=2",postCount:856},{id:3,name:"芭蕾",cover:"https://picsum.photos/200/120?random=3",postCount:642},{id:4,name:"现代舞",cover:"https://picsum.photos/200/120?random=4",postCount:789}]},loadRecommendUsers:function(){var e=this;return(0,i.default)(r.default.mark((function t(){var n;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,console.log("开始加载推荐用户..."),t.next=4,(0,c.getRecommendUsers)({limit:10});case 4:n=t.sent,console.log("推荐用户API返回:",n),n&&0===n.code&&n.data?(e.recommendUsers=n.data.map((function(t){return{id:t.userId||t.id,nickname:t.nickname||"用户",avatar:e.formatAvatarUrl(t.avatar),description:t.bio||t.recommendReason||"暂无简介",isFollowed:t.isFollowed||!1,followersCount:t.followerCount||0,postCount:t.postCount||0}})),console.log("推荐用户加载成功:",e.recommendUsers),e.batchCheckFollowStatus()):(console.error("推荐用户API返回格式不正确:",n),e.recommendUsers=e.getDefaultRecommendUsers()),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),console.error("加载推荐用户失败:",t.t0),e.recommendUsers=e.getDefaultRecommendUsers();case 13:case"end":return t.stop()}}),t,null,[[0,9]])})))()},getDefaultRecommendUsers:function(){return[{id:18,nickname:"舞蹈达人",avatar:"https://picsum.photos/100/100?random=18",description:"分享舞蹈技巧和心得",isFollowed:!1,followersCount:1024,postCount:128}]},loadHotPosts:function(){var e=this;return(0,i.default)(r.default.mark((function t(){var n,o;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,console.log("开始加载热门帖子..."),t.next=4,(0,c.getHotPosts)({limit:4,timeRange:"7d"});case 4:n=t.sent,console.log("热门帖子API返回:",n),n&&0===n.code&&n.data?(o=Array.isArray(n.data)?n.data:n.data.records||[],e.hotPosts=o.map((function(t){var n;return{id:t.id,username:t.nickname||t.username||"用户",userAvatar:e.formatAvatarUrl(t.avatar||t.userAvatar),title:t.title||"",content:e.truncateContent(t.title||t.content||""),coverImage:e.formatImageUrl((null===(n=t.images)||void 0===n?void 0:n[0])||t.coverImage),likeCount:t.likeCount||0,commentCount:t.commentCount||0,createTime:t.createTime}})),console.log("热门帖子加载成功:",e.hotPosts)):(console.error("热门帖子API返回格式不正确:",n),e.hotPosts=e.getDefaultPosts()),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),console.error("加载热门帖子失败:",t.t0),e.hotPosts=e.getDefaultPosts();case 13:case"end":return t.stop()}}),t,null,[[0,9]])})))()},formatAvatarUrl:function(e){return e?"https://file.foxdance.com.cn"+e:"/static/images/toux.png"},formatImageUrl:function(e){return e?e.startsWith("http")||e.startsWith("/")?e:"/".concat(e):"https://picsum.photos/120/120?random=30"},truncateContent:function(e){return e?e.length>50?e.substring(0,50)+"...":e:"暂无内容"},getDefaultPosts:function(){return[{id:1,username:"舞蹈达人",userAvatar:"https://picsum.photos/100/100?random=20",content:"今天的舞蹈练习分享，基础动作很重要...",coverImage:"https://picsum.photos/120/120?random=30",likeCount:234,commentCount:45},{id:2,username:"街舞教练",userAvatar:"https://picsum.photos/100/100?random=21",content:"新手入门街舞的几个要点，记得收藏！",coverImage:"https://picsum.photos/120/120?random=31",likeCount:189,commentCount:32}]},loadFeaturedContent:function(){this.featuredContent=[{id:1,title:"春日穿搭指南",subtitle:"时尚达人教你搭配",cover:"https://picsum.photos/300/200?random=40"},{id:2,title:"周末好去处",subtitle:"城市探索攻略",cover:"https://picsum.photos/300/200?random=41"}]},onUserFollow:function(e){console.log("关注操作:",e)}},(0,s.default)(a,"onUserFollow",(function(e){console.log("用户关注成功:",e);var t=this.recommendUsers.find((function(t){return t.id===e.user.id}));t&&(t.isFollowed=!0,void 0!==t.followersCount&&(t.followersCount+=1))})),(0,s.default)(a,"onUserUnfollow",(function(e){console.log("用户取消关注成功:",e);var t=this.recommendUsers.find((function(t){return t.id===e.user.id}));t&&(t.isFollowed=!1,void 0!==t.followersCount&&t.followersCount>0&&(t.followersCount-=1))})),(0,s.default)(a,"onFollowChange",(function(e){var t=this.recommendUsers.find((function(t){return t.id===e.user.id}));t&&(t.isFollowed=e.isFollowed),console.log("关注状态变化:",e)})),(0,s.default)(a,"goSearch",(function(){e.navigateTo({url:"/pagesSub/social/search/index"})})),(0,s.default)(a,"goTopic",(function(t){e.navigateTo({url:"/pagesSub/social/topic/detail?id=".concat(t.id)})})),(0,s.default)(a,"goTopicList",(function(){e.navigateTo({url:"/pagesSub/social/topic/list"})})),(0,s.default)(a,"forceRefresh",(function(){console.log("强制刷新发现页数据..."),this.hotTopics=[],this.recommendUsers=[],this.hotPosts=[],this.featuredContent=[],this.loadDiscoverData()})),(0,s.default)(a,"goUserProfile",(function(t){e.navigateTo({url:"/pagesSub/social/user/profile?id=".concat(t.id)})})),(0,s.default)(a,"goPostDetail",(function(t){e.navigateTo({url:"/pagesSub/social/post/detail?id=".concat(t.id)})})),(0,s.default)(a,"goFeaturedDetail",(function(t){e.navigateTo({url:"/pagesSub/social/featured/detail?id=".concat(t.id)})})),(0,s.default)(a,"batchCheckFollowStatus",(function(){var t=this;return(0,i.default)(r.default.mark((function n(){var o,a,s;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.recommendUsers&&0!==t.recommendUsers.length){n.next=2;break}return n.abrupt("return");case 2:if(o=e.getStorageSync("userid"),o){n.next=5;break}return n.abrupt("return");case 5:if(n.prev=5,a=t.recommendUsers.map((function(e){return e.id})).filter((function(e){return e!=o})),0!==a.length){n.next=9;break}return n.abrupt("return");case 9:return n.next=11,(0,c.batchCheckFollowStatus)(a);case 11:s=n.sent,console.log("批量检查关注状态结果:",s),s&&0===s.code&&s.data&&t.recommendUsers.forEach((function(e){void 0!==s.data[e.id]&&(e.isFollowed=s.data[e.id])})),n.next=19;break;case 16:n.prev=16,n.t0=n["catch"](5),console.error("批量检查关注状态失败:",n.t0);case 19:case"end":return n.stop()}}),n,null,[[5,16]])})))()})),(0,s.default)(a,"forceRefresh",(function(){console.log("强制刷新发现页面数据..."),this.loadDiscoverData()})),a)};t.default=u}).call(this,n("df3c")["default"])},"60de":function(e,t,n){"use strict";n.r(t);var o=n("9be7"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},"6e3f":function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n("7eb4")),r=o(n("ee10")),s=n("41ad"),i={name:"SocialPublish",data:function(){return{userInfo:{avatar:"/static/images/toux.png",nickname:"加载中..."},postTitle:"",postContent:"",selectedImages:[],selectedTopics:[],selectedLocation:null,visibility:"public",publishing:!1,showTopicModal:!1,showLocationModal:!1,topicKeyword:"",allTopics:[{id:1,name:"街舞",postCount:1234},{id:2,name:"现代舞",postCount:856},{id:3,name:"芭蕾",postCount:642},{id:4,name:"拉丁舞",postCount:789},{id:5,name:"爵士舞",postCount:456},{id:6,name:"民族舞",postCount:321},{id:7,name:"古典舞",postCount:298},{id:8,name:"舞蹈教学",postCount:567},{id:9,name:"舞蹈比赛",postCount:234},{id:10,name:"舞蹈培训",postCount:189}],nearbyLocations:[{id:1,name:"星巴克咖啡",address:"北京市朝阳区三里屯太古里"},{id:2,name:"三里屯太古里",address:"北京市朝阳区三里屯路19号"},{id:3,name:"朝阳公园",address:"北京市朝阳区朝阳公园南路1号"}]}},computed:{canPublish:function(){return this.postTitle.trim().length>0||this.postContent.trim().length>0||this.selectedImages.length>0},visibilityText:function(){return{public:"公开",friends:"仅朋友可见",private:"仅自己可见"}[this.visibility]},filteredTopics:function(){var e=this;return this.topicKeyword?this.allTopics.filter((function(t){return t.name.includes(e.topicKeyword)})):this.allTopics}},onLoad:function(){console.log("=== 发布页面加载 ==="),console.log("onLoad方法被调用了"),this.checkUserLogin()&&(e.showToast({title:"onLoad被调用",icon:"none",duration:2e3}),this.testApiConnection(),this.loadUserInfo(),this.loadHotTopics())},mounted:function(){console.log("=== mounted生命周期被调用 ==="),"加载中..."===this.userInfo.nickname&&(console.log("onLoad可能没有执行，在mounted中重新执行"),this.testApiConnection(),this.loadUserInfo(),this.loadHotTopics())},methods:{checkUserLogin:function(){var t=e.getStorageSync("token"),n=e.getStorageSync("userid");return!(!t||!n)||(console.log("用户未登录，跳转到登录页"),e.showToast({title:"请先登录",icon:"none",duration:2e3}),setTimeout((function(){e.navigateTo({url:"/pages/login/login"})}),1500),!1)},testApiConnection:function(){return(0,r.default)(a.default.mark((function t(){var n;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("测试API连接..."),t.prev=1,t.next=4,e.request({url:"http://localhost:8101/api/health",method:"GET",timeout:5e3});case 4:n=t.sent,console.log("API连接测试结果:",n),200===n.statusCode?console.log("✅ 后端服务连接正常"):console.log("❌ 后端服务响应异常:",n.statusCode),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](1),console.error("❌ 后端服务连接失败:",t.t0),e.showToast({title:"后端服务连接失败",icon:"none",duration:3e3});case 13:case"end":return t.stop()}}),t,null,[[1,9]])})))()},loadUserInfo:function(){var t=this;return(0,r.default)(a.default.mark((function n(){var o,r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return console.log("开始加载用户信息..."),n.prev=1,o=e.getStorageSync("userid"),console.log("调用getUserProfile，用户ID:",o),n.next=6,(0,s.getUserProfile)(o);case 6:r=n.sent,console.log("获取用户信息结果:",r),r&&0===r.code&&r.data?t.userInfo={avatar:r.data.avatar?r.data.avatar.startsWith("http")?r.data.avatar:"https://file.foxdance.com.cn"+r.data.avatar:"/static/images/toux.png",nickname:r.data.nickname||"用户"}:(console.warn("获取用户信息失败:",r),t.userInfo={avatar:"/static/images/toux.png",nickname:"用户"}),n.next=17;break;case 11:n.prev=11,n.t0=n["catch"](1),console.error("加载用户信息失败:",n.t0),console.error("错误详情:",n.t0.message||n.t0),e.showToast({title:"用户信息加载失败",icon:"none",duration:2e3}),t.userInfo={avatar:"/static/images/toux.png",nickname:"用户"};case 17:case"end":return n.stop()}}),n,null,[[1,11]])})))()},loadHotTopics:function(){var t=this;return(0,r.default)(a.default.mark((function n(){var o;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return console.log("开始加载热门话题..."),n.prev=1,n.next=4,(0,s.getHotTags)(20);case 4:o=n.sent,console.log("获取热门话题结果:",o),o&&0===o.code&&o.data&&o.data.length>0?(t.allTopics=o.data.map((function(e){return{id:e.id,name:e.name,postCount:e.useCount||0}})),console.log("话题加载成功，数量:",t.allTopics.length)):o&&o.length>0?(t.allTopics=o.map((function(e){return{id:e.id,name:e.name,postCount:e.useCount||0}})),console.log("话题加载成功（数组格式），数量:",t.allTopics.length)):console.log("没有获取到话题数据，hotTags:",o),n.next=16;break;case 9:n.prev=9,n.t0=n["catch"](1),console.error("加载热门话题失败:",n.t0),console.error("错误详情:",n.t0.message||n.t0),e.showToast({title:"话题加载失败",icon:"none",duration:2e3}),t.allTopics=[{id:1,name:"生活",postCount:0},{id:2,name:"美食",postCount:0},{id:3,name:"旅行",postCount:0},{id:4,name:"摄影",postCount:0},{id:5,name:"时尚",postCount:0}],console.log("使用默认话题列表");case 16:case"end":return n.stop()}}),n,null,[[1,9]])})))()},goBack:function(){this.postTitle||this.postContent||this.selectedImages.length?e.showModal({title:"提示",content:"确定要放弃编辑吗？",success:function(t){t.confirm&&e.navigateBack()}}):e.navigateBack()},chooseImage:function(){var t=this;return(0,r.default)(a.default.mark((function n(){var o;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:o=9-t.selectedImages.length,e.chooseImage({count:o,sizeType:["compressed"],sourceType:["album","camera"],success:function(){var n=(0,r.default)(a.default.mark((function n(o){var r,i,c,u,l,d,f;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:r=o.tempFilePaths,console.log("🔥 选择图片成功，开始上传到COS:",r),e.showLoading({title:"上传图片中...",mask:!0}),n.prev=3,i=0,c=0,u=0;case 7:if(!(u<r.length)){n.next=25;break}return l=r[u],console.log("🔥 上传第".concat(u+1,"张图片:"),l),n.prev=10,n.next=13,(0,s.uploadPostImage)(l,"file",{driver:"cos"});case 13:d=n.sent,console.log("🔥 第".concat(u+1,"张图片上传结果:"),d),0===d.code&&d.data?(f=d.data,t.selectedImages.push(f),i++,console.log("✅ 图片上传成功:",f)):(c++,console.error("❌ 图片上传失败，响应格式错误:",d)),n.next=22;break;case 18:n.prev=18,n.t0=n["catch"](10),c++,console.error("❌ 第".concat(u+1,"张图片上传异常:"),n.t0);case 22:u++,n.next=7;break;case 25:e.hideLoading(),i>0&&t.$u.toast("成功上传".concat(i,"张图片")),c>0&&t.$u.toast("".concat(c,"张图片上传失败")),n.next=35;break;case 30:n.prev=30,n.t1=n["catch"](3),e.hideLoading(),console.error("❌ 图片上传过程异常:",n.t1),t.$u.toast("图片上传失败");case 35:case"end":return n.stop()}}),n,null,[[3,30],[10,18]])})));return function(e){return n.apply(this,arguments)}}(),fail:function(e){console.error("❌ 选择图片失败:",e),t.$u.toast("选择图片失败")}});case 2:case"end":return n.stop()}}),n)})))()},removeImage:function(e){this.selectedImages.splice(e,1)},selectTopic:function(){this.showTopicModal=!0},toggleTopic:function(e){var t=this.selectedTopics.indexOf(e.name);t>-1?this.selectedTopics.splice(t,1):this.selectedTopics.length<3?this.selectedTopics.push(e.name):this.$u.toast("最多选择3个话题")},searchTopics:function(){},selectLocation:function(){var t=this;console.log("打开位置选择..."),e.chooseLocation({success:function(n){console.log("位置选择成功:",n),t.selectedLocation={name:n.name||n.address,address:n.address,latitude:n.latitude,longitude:n.longitude},e.showToast({title:"位置选择成功",icon:"success",duration:1500})},fail:function(n){console.error("位置选择失败:",n),n.errMsg&&n.errMsg.includes("cancel")||(e.showToast({title:"位置选择失败",icon:"none",duration:2e3}),t.showLocationModal=!0)}})},selectLocationItem:function(e){this.selectedLocation=e,this.showLocationModal=!1},clearLocation:function(){this.selectedLocation=null,e.showToast({title:"已清除位置",icon:"success",duration:1e3})},setVisibility:function(){var t=this;e.showActionSheet({itemList:["公开","仅朋友可见","仅自己可见"],success:function(e){t.visibility=["public","friends","private"][e.tapIndex]}})},publishPost:function(){var t=this;return(0,r.default)(a.default.mark((function n(){var o,r,i,c,u,l,d;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.canPublish&&!t.publishing){n.next=2;break}return n.abrupt("return");case 2:if(o=e.getStorageSync("userid"),o){n.next=7;break}return e.showToast({title:"请先登录",icon:"none",duration:2e3}),setTimeout((function(){e.navigateTo({url:"/pages/login/login"})}),1500),n.abrupt("return");case 7:return t.publishing=!0,n.prev=8,l={userId:Number(o),title:t.postTitle.trim()||null,content:t.postContent.trim(),images:t.selectedImages,tags:t.selectedTopics.map((function(e){return e.name||e})),locationName:(null===(r=t.selectedLocation)||void 0===r?void 0:r.name)||"",locationLatitude:(null===(i=t.selectedLocation)||void 0===i?void 0:i.latitude)||null,locationLongitude:(null===(c=t.selectedLocation)||void 0===c?void 0:c.longitude)||null,locationAddress:(null===(u=t.selectedLocation)||void 0===u?void 0:u.address)||"",isPublic:"public"===t.visibility?1:0,status:1},console.log("发布帖子数据:",l),n.next=13,(0,s.createPost)(l);case 13:d=n.sent,console.log("发布API返回结果:",d),d&&0===d.code?t.$u.toast("发布成功"):(t.$u.toast((null===d||void 0===d?void 0:d.message)||"发布失败，请重试"),t.publishing=!1),n.next=23;break;case 18:n.prev=18,n.t0=n["catch"](8),console.error("发布帖子失败:",n.t0),t.$u.toast("网络错误，请重试"),t.publishing=!1;case 23:case"end":return n.stop()}}),n,null,[[8,18]])})))()}}};t.default=i}).call(this,n("df3c")["default"])},"79c8":function(e,t,n){"use strict";n.r(t);var o=n("5899"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},"7b6c":function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n("7eb4")),r=o(n("7ca3")),s=o(n("3b2d")),i=o(n("ee10")),c=n("41ad");function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,r.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d={name:"SocialProfile",components:{PostCard:function(){n.e("pagesSub/social/components/PostCard").then(function(){return resolve(n("390e"))}.bind(null,n)).catch(n.oe)},FollowButton:function(){n.e("pagesSub/social/components/FollowButton").then(function(){return resolve(n("607e"))}.bind(null,n)).catch(n.oe)}},data:function(){return{userInfo:{userId:"",nickname:"",avatar:"",bio:"",danceType:"",postCount:0,privatePostCount:0,followingCount:0,followersCount:0,likeCount:0,draftCount:0},loading:!0,currentTab:0,isInitialized:!1,tabs:[{name:"作品",data:[],loading:!1},{name:"私密作品",data:[],loading:!1},{name:"关注",data:[],loading:!1},{name:"粉丝",data:[],loading:!1},{name:"喜欢",data:[],loading:!1}]}},onLoad:function(){var e=this;console.log("Profile页面 onLoad"),this.$nextTick((function(){setTimeout((function(){e.initializeData()}),100)}))},onShow:function(){console.log("Profile页面 onShow"),this.isInitialized&&this.loadUserInfo()},methods:{initializeData:function(){var e=this;return(0,i.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("初始化Profile页面数据..."),t.prev=1,t.next=4,e.loadUserInfo();case 4:e.isInitialized=!0,t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](1),console.error("初始化数据失败:",t.t0);case 10:case"end":return t.stop()}}),t,null,[[1,7]])})))()},loadUserInfo:function(){var t=this;return(0,i.default)(a.default.mark((function n(){var o,r,s;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,o=t.getCurrentUserId(),o){n.next=6;break}return console.error("用户未登录，无法加载用户信息"),e.showToast({title:"请先登录",icon:"none"}),n.abrupt("return");case 6:return console.log("开始加载用户信息 - userId:",o),t.loading=!0,n.next=10,(0,c.getUserProfile)(o);case 10:if(r=n.sent,console.log("用户信息API返回:",r),!r||0!==r.code||!r.data){n.next=21;break}return s=r.data,t.userInfo={userId:s.id||s.userId||o,nickname:s.nickname||"舞蹈爱好者",avatar:t.formatAvatarUrl(s.avatar),bio:s.bio||s.userProfile||"热爱舞蹈，享受生活",danceType:s.danceType||"街舞",postCount:s.postCount||0,privatePostCount:0,followingCount:s.followingCount||0,followersCount:s.followersCount||s.followerCount||0,likeCount:s.likeReceivedCount||s.likeCount||0,draftCount:s.draftCount||0},console.log("用户信息加载成功:",t.userInfo),n.next=18,t.loadPrivatePostCount(o);case 18:t.loadTabData(t.currentTab),n.next=23;break;case 21:console.error("用户信息API返回格式不正确:",r),e.showToast({title:"加载用户信息失败",icon:"none"});case 23:n.next=29;break;case 25:n.prev=25,n.t0=n["catch"](0),console.error("加载用户信息失败:",n.t0),e.showToast({title:"加载失败，请重试",icon:"none"});case 29:return n.prev=29,t.loading=!1,n.finish(29);case 32:case"end":return n.stop()}}),n,null,[[0,25,29,32]])})))()},loadPrivatePostCount:function(e){var t=this;return(0,i.default)(a.default.mark((function n(){var o;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,(0,c.getPostList)({current:1,size:1,userId:e,isPublic:0});case 3:o=n.sent,o&&0===o.code&&o.data&&(void 0!==o.data.total?t.userInfo.privatePostCount=o.data.total:Array.isArray(o.data)&&(t.userInfo.privatePostCount=o.data.length)),console.log("私密作品数量:",t.userInfo.privatePostCount),n.next=12;break;case 8:n.prev=8,n.t0=n["catch"](0),console.error("加载私密作品数量失败:",n.t0),t.userInfo.privatePostCount=0;case 12:case"end":return n.stop()}}),n,null,[[0,8]])})))()},getCurrentUserId:function(){var t=e.getStorageSync("userid");return t?Number(t):null},formatAvatarUrl:function(e){return e?e.startsWith("http")||e.startsWith("/")?e:"/".concat(e):"/static/images/toux.png"},loadTabData:function(e){var t=this;return(0,i.default)(a.default.mark((function n(){var o;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!t.tabs[e].loading){n.next=2;break}return n.abrupt("return");case 2:t.$set(t.tabs[e],"loading",!0),n.prev=3,o=[],n.t0=e,n.next=0===n.t0?8:1===n.t0?12:2===n.t0?16:3===n.t0?20:4===n.t0?24:28;break;case 8:return n.next=10,t.loadUserPosts();case 10:return o=n.sent,n.abrupt("break",28);case 12:return n.next=14,t.loadPrivatePosts();case 14:return o=n.sent,n.abrupt("break",28);case 16:return n.next=18,t.loadFollowingUsers();case 18:return o=n.sent,n.abrupt("break",28);case 20:return n.next=22,t.loadFollowersUsers();case 22:return o=n.sent,n.abrupt("break",28);case 24:return n.next=26,t.loadLikedPosts();case 26:return o=n.sent,n.abrupt("break",28);case 28:t.$set(t.tabs[e],"data",o),n.next=35;break;case 31:n.prev=31,n.t1=n["catch"](3),console.error("加载标签页".concat(e,"数据失败:"),n.t1),t.$set(t.tabs[e],"data",[]);case 35:return n.prev=35,t.$set(t.tabs[e],"loading",!1),n.finish(35);case 38:case"end":return n.stop()}}),n,null,[[3,31,35,38]])})))()},loadUserPosts:function(){var e=this;return(0,i.default)(a.default.mark((function t(){var n,o,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.getCurrentUserId(),n){t.next=5;break}return console.error("用户未登录，无法加载帖子"),t.abrupt("return",[]);case 5:return t.next=7,(0,c.getPostList)({current:1,size:20,userId:n,isPublic:1,sortField:"createTime",sortOrder:"desc"});case 7:if(o=t.sent,console.log("用户帖子API返回:",o),!o||0!==o.code||!o.data){t.next=14;break}return r=Array.isArray(o.data)?o.data:o.data.records||[],t.abrupt("return",r.map((function(t){var n;return{id:t.id,title:t.title||"",coverImage:e.formatImageUrl((null===(n=t.images)||void 0===n?void 0:n[0])||t.coverImage),username:t.nickname||e.userInfo.nickname,userAvatar:e.formatAvatarUrl(t.avatar||e.userInfo.avatar),content:t.content,likeCount:t.likeCount||0,commentCount:t.commentCount||0,isLiked:t.isLiked||!1,isPublic:t.isPublic,status:t.status,createTime:new Date(t.createTime)}})));case 14:return console.log("用户帖子API返回格式不正确:",o),t.abrupt("return",[]);case 16:t.next=22;break;case 18:return t.prev=18,t.t0=t["catch"](0),console.error("加载用户帖子失败:",t.t0),t.abrupt("return",[]);case 22:case"end":return t.stop()}}),t,null,[[0,18]])})))()},loadPrivatePosts:function(){var e=this;return(0,i.default)(a.default.mark((function t(){var n,o,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.getCurrentUserId(),n){t.next=5;break}return console.error("用户未登录，无法加载私密帖子"),t.abrupt("return",[]);case 5:return t.next=7,(0,c.getPostList)({current:1,size:20,userId:n,isPublic:0,sortField:"createTime",sortOrder:"desc"});case 7:if(o=t.sent,console.log("用户私密帖子API返回:",o),!o||0!==o.code||!o.data){t.next=14;break}return r=Array.isArray(o.data)?o.data:o.data.records||[],t.abrupt("return",r.map((function(t){var n;return{id:t.id,title:t.title||"",coverImage:e.formatImageUrl((null===(n=t.images)||void 0===n?void 0:n[0])||t.coverImage),username:t.nickname||e.userInfo.nickname,userAvatar:e.formatAvatarUrl(t.avatar||e.userInfo.avatar),content:t.content,likeCount:t.likeCount||0,commentCount:t.commentCount||0,isLiked:t.isLiked||!1,isPublic:t.isPublic,status:t.status,createTime:new Date(t.createTime)}})));case 14:return console.log("用户私密帖子API返回格式不正确:",o),t.abrupt("return",[]);case 16:t.next=22;break;case 18:return t.prev=18,t.t0=t["catch"](0),console.error("加载用户私密帖子失败:",t.t0),t.abrupt("return",[]);case 22:case"end":return t.stop()}}),t,null,[[0,18]])})))()},loadLikedPosts:function(){var e=this;return(0,i.default)(a.default.mark((function t(){var n;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.getCurrentUserId(),n){t.next=5;break}return console.error("用户未登录，无法加载点赞帖子"),t.abrupt("return",[]);case 5:return console.log("加载用户点赞的帖子 - userId:",n),t.abrupt("return",[]);case 9:return t.prev=9,t.t0=t["catch"](0),console.error("加载点赞帖子失败:",t.t0),t.abrupt("return",[]);case 13:case"end":return t.stop()}}),t,null,[[0,9]])})))()},loadFollowingUsers:function(){var e=this;return(0,i.default)(a.default.mark((function t(){var n,o,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.getCurrentUserId(),n){t.next=5;break}return console.error("用户未登录，无法加载关注列表"),t.abrupt("return",[]);case 5:return console.log("开始加载关注用户列表..."),t.next=8,(0,c.getFollowingList)(n,{current:1,size:50});case 8:if(o=t.sent,console.log("关注用户API返回:",o),!o||0!==o.code||!o.data){t.next=15;break}return r=Array.isArray(o.data)?o.data:o.data.records||[],t.abrupt("return",r.map((function(t){return{id:t.userId||t.id,userId:t.userId||t.id,nickname:t.nickname||"用户",avatar:"https://file.foxdance.com.cn"+e.formatAvatarUrl(t.avatar),bio:t.bio||"暂无简介",danceType:t.danceType||"",followersCount:t.followerCount||0,isFollowed:!0,type:"user"}})));case 15:return console.error("关注用户API返回格式不正确:",o),t.abrupt("return",[]);case 17:t.next=23;break;case 19:return t.prev=19,t.t0=t["catch"](0),console.error("加载关注用户失败:",t.t0),t.abrupt("return",[]);case 23:case"end":return t.stop()}}),t,null,[[0,19]])})))()},loadFollowersUsers:function(){var e=this;return(0,i.default)(a.default.mark((function t(){var n,o,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,n=e.getCurrentUserId(),n){t.next=5;break}return console.error("用户未登录，无法加载粉丝列表"),t.abrupt("return",[]);case 5:return console.log("开始加载粉丝用户列表..."),t.next=8,(0,c.getFollowersList)(n,{current:1,size:50});case 8:if(o=t.sent,console.log("粉丝用户API返回:",o),!o||0!==o.code||!o.data){t.next=15;break}return r=Array.isArray(o.data)?o.data:o.data.records||[],t.abrupt("return",r.map((function(t){return{id:t.userId||t.id,userId:t.userId||t.id,nickname:t.nickname||"用户",avatar:"https://file.foxdance.com.cn"+e.formatAvatarUrl(t.avatar),bio:t.bio||"暂无简介",danceType:t.danceType||"",followersCount:t.followerCount||0,isFollowed:t.isFollowed||!1,type:"user"}})));case 15:return console.error("粉丝用户API返回格式不正确:",o),t.abrupt("return",[]);case 17:t.next=23;break;case 19:return t.prev=19,t.t0=t["catch"](0),console.error("加载粉丝用户失败:",t.t0),t.abrupt("return",[]);case 23:case"end":return t.stop()}}),t,null,[[0,19]])})))()},formatImageUrl:function(e){return e?e.startsWith("http")||e.startsWith("/")?e:"/".concat(e):"/static/images/default-cover.png"},switchTab:function(e){var t="object"===(0,s.default)(e)?e.index:e;this.currentTab!==t&&(this.currentTab=t,this.loadTabData(t))},scanCode:function(){e.scanCode({success:function(e){console.log("扫码结果:",e)}})},goSettings:function(){e.navigateTo({url:"/pagesSub/social/settings/index"})},editAvatar:function(){var t=this;e.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:function(e){t.userInfo.avatar=e.tempFilePaths[0]}})},editProfile:function(){e.navigateTo({url:"/pagesSub/social/profile/edit"})},goLikeList:function(){e.navigateTo({url:"/pagesSub/social/like/list"})},viewPost:function(t){e.navigateTo({url:"/pagesSub/social/post/detail?id=".concat(t.id)})},goPostDetail:function(t){e.navigateTo({url:"/pagesSub/social/post/detail?id=".concat(t.id)})},goUserProfile:function(t){if("user"===t.type){var n=t.id||t.userId;n&&e.navigateTo({url:"/pagesSub/social/user/profile?userId=".concat(n,"&name=").concat(t.nickname)})}else{if(t.username===this.userInfo.nickname)return;e.navigateTo({url:"/pagesSub/social/user/profile?id=".concat(t.userId||t.id)})}},onPostLike:function(t){var n=this;return(0,i.default)(a.default.mark((function o(){var r,s;return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(o.prev=0,!t.isLiked){o.next=9;break}return o.next=4,(0,c.unlikePost)(t.id);case 4:t.isLiked=!1,t.likeCount=Math.max(0,t.likeCount-1),e.showToast({title:"取消点赞",icon:"none",duration:1e3}),o.next=14;break;case 9:return o.next=11,(0,c.likePost)(t.id);case 11:t.isLiked=!0,t.likeCount+=1,e.showToast({title:"点赞成功",icon:"success",duration:1e3});case 14:r=n.tabs[n.currentTab].data,s=r.findIndex((function(e){return e.id===t.id})),-1!==s&&n.$set(r,s,l({},t)),o.next=23;break;case 19:o.prev=19,o.t0=o["catch"](0),console.error("点赞操作失败:",o.t0),e.showToast({title:"操作失败",icon:"none"});case 23:case"end":return o.stop()}}),o,null,[[0,19]])})))()},forceRefresh:function(){console.log("强制刷新个人资料页面数据..."),this.isInitialized=!1,this.loading=!0,this.userInfo={userId:"",nickname:"",avatar:"",bio:"",danceType:"",postCount:0,followingCount:0,followersCount:0,likeCount:0,draftCount:0},this.tabs.forEach((function(e){e.data=[],e.loading=!1})),this.initializeData()},getEmptyText:function(){return["暂无作品","暂无私密作品","暂无关注","暂无粉丝","暂无喜欢"][this.currentTab]||"暂无内容"},onUserFollow:function(e){console.log("用户关注成功:",e),this.updateUserFollowStatus(e.user.id,!0),this.userInfo.followingCount+=1},onUserUnfollow:function(e){console.log("用户取消关注成功:",e),this.updateUserFollowStatus(e.user.id,!1),this.userInfo.followingCount>0&&(this.userInfo.followingCount-=1)},onFollowChange:function(e){console.log("关注状态变化:",e),this.updateUserFollowStatus(e.user.id,e.isFollowed)},updateUserFollowStatus:function(e,t){if(this.tabs[2]&&this.tabs[2].data){var n=this.tabs[2].data.find((function(t){return t.id===e}));n&&(n.isFollowed=t)}if(this.tabs[3]&&this.tabs[3].data){var o=this.tabs[3].data.find((function(t){return t.id===e}));o&&(o.isFollowed=t)}}}};t.default=d}).call(this,n("df3c")["default"])},"808e":function(e,t,n){"use strict";n.r(t);var o=n("99b8"),a=n("161b");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("ffde");var s=n("828b"),i=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"2cde35c9",null,!1,o["a"],void 0);t["default"]=i.exports},8276:function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a,r=o(n("7eb4")),s=o(n("7ca3")),i=o(n("af34")),c=o(n("ee10")),u=n("41ad");function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){(0,s.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f={name:"SocialHome",components:{PostCard:function(){n.e("pagesSub/social/components/PostCard").then(function(){return resolve(n("390e"))}.bind(null,n)).catch(n.oe)}},data:function(){return{postList:[],loading:!1,refreshing:!1,page:1,pageSize:10,currentTopic:"all",currentTopicIndex:0,topicList:[{name:"全部",id:"all"},{name:"街舞",id:"street-dance"},{name:"现代舞",id:"modern-dance"},{name:"芭蕾",id:"ballet"},{name:"拉丁舞",id:"latin-dance"},{name:"爵士舞",id:"jazz-dance"},{name:"民族舞",id:"folk-dance"},{name:"古典舞",id:"classical-dance"},{name:"舞蹈教学",id:"dance-teaching"},{name:"舞蹈比赛",id:"dance-competition"}],hasMore:!0,isInitialized:!1}},onLoad:function(){this.initializeData()},onShow:function(){this.isInitialized&&this.postList&&0!==this.postList.length||this.initializeData()},activated:function(){this.isInitialized&&this.postList&&0!==this.postList.length||this.initializeData()},methods:(a={initializeData:function(){var e=this;return(0,c.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("初始化首页数据..."),t.prev=1,t.next=4,e.loadHotTags();case 4:return t.next=6,e.loadPosts(!0);case 6:e.isInitialized=!0,t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),console.error("初始化数据失败:",t.t0);case 12:case"end":return t.stop()}}),t,null,[[1,9]])})))()},loadHotTags:function(){var e=this;return(0,c.default)(r.default.mark((function t(){var n,o;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,u.getHotTags)(10);case 3:n=t.sent,console.log("热门标签API返回:",n),n&&0===n.code&&n.data&&n.data.length>0&&(o=e.topicList[0],e.topicList=[o].concat((0,i.default)(n.data.map((function(e){return{name:e.name,id:e.id}})))),console.log("话题列表更新:",e.topicList)),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("加载热门话题失败:",t.t0);case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))()},loadPosts:function(){var t=arguments,n=this;return(0,c.default)(r.default.mark((function o(){var a,s,c,l,d;return r.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(a=t.length>0&&void 0!==t[0]&&t[0],!n.loading){o.next=4;break}return console.log("正在加载中，跳过重复请求"),o.abrupt("return");case 4:return console.log("开始加载帖子数据，refresh:",a),n.loading=!0,o.prev=6,s={current:a?1:n.page,size:n.pageSize,sortField:"createTime",sortOrder:"desc"},"all"!==n.currentTopic&&(c=n.topicList.find((function(e){return e.id===n.currentTopic})),c&&(s.tags=[c.name])),console.log("API请求参数:",s),o.next=12,(0,u.getPostList)(s);case 12:l=o.sent,console.log("API返回结果:",l),0==l.code?(d=l.data.records.map((function(e){return{id:e.id,userId:e.userId,title:e.title||"",username:e.nickname||"无名氏",userAvatar:"https://file.foxdance.com.cn"+e.avatar||!1,content:e.content,coverImage:e.images&&e.images[0]||"/static/images/wusj.png",images:e.images||[],topics:e.tags||[],topicId:n.currentTopic,likeCount:e.likeCount||0,commentCount:e.commentCount||0,isLiked:e.isLiked||!1,createTime:new Date(e.createTime)}})),console.log("posts:",d),a?(n.postList=d,n.page=2):(n.postList=[].concat((0,i.default)(n.postList),(0,i.default)(d)),n.page++),console.log("this.postList:",n.postList),n.hasMore=l.data.records.length>=n.pageSize):(console.warn("获取帖子列表失败:",l),e.showToast({title:l.message||"获取数据失败",icon:"none"}),n.hasMore=!1),o.next=22;break;case 17:o.prev=17,o.t0=o["catch"](6),console.error("加载帖子失败:",o.t0),e.showToast({title:"网络错误，请重试",icon:"none"}),n.hasMore=!1;case 22:return o.prev=22,n.loading=!1,n.refreshing=!1,o.finish(22);case 26:case"end":return o.stop()}}),o,null,[[6,17,22,26]])})))()},onRefresh:function(){console.log("刷新首页数据..."),this.refreshing=!0,this.page=1,this.postList=[],this.hasMore=!0,this.loadHotTags(),this.loadPosts(!0)},loadMore:function(){this.loading||(this.page++,this.loadPosts())},formatTime:function(e){var t=new Date,n=t-new Date(e),o=Math.floor(n/6e4),a=Math.floor(n/36e5),r=Math.floor(n/864e5);return o<60?"".concat(o,"分钟前"):a<24?"".concat(a,"小时前"):"".concat(r,"天前")},onPostLike:function(t){var n=this;return(0,c.default)(r.default.mark((function o(){var a;return r.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(o.prev=0,!t.isLiked){o.next=8;break}return o.next=4,(0,u.unlikePost)(t.id);case 4:t.isLiked=!1,t.likeCount=Math.max(0,t.likeCount-1),o.next=12;break;case 8:return o.next=10,(0,u.likePost)(t.id);case 10:t.isLiked=!0,t.likeCount+=1;case 12:a=n.postList.findIndex((function(e){return e.id===t.id})),-1!==a&&n.$set(n.postList,a,d({},t)),o.next=20;break;case 16:o.prev=16,o.t0=o["catch"](0),console.error("点赞操作失败:",o.t0),e.showToast({title:"操作失败",icon:"none"});case 20:case"end":return o.stop()}}),o,null,[[0,16]])})))()},goPostDetail:function(t){e.navigateTo({url:"/pagesSub/social/post/detail?id=".concat(t.id)})},goUserProfile:function(t){t.userId?(console.log("跳转到用户主页，用户ID:",t.userId,"用户名:",t.username),e.navigateTo({url:"/pagesSub/social/user/profile?id=".concat(t.userId)})):e.showToast({title:"用户信息错误",icon:"none"})},goSearch:function(){e.navigateTo({url:"/pagesSub/social/search/index"})},selectTopic:function(e){this.currentTopicIndex!==e&&(this.currentTopicIndex=e,this.currentTopic=this.topicList[e].id,this.page=1,this.postList=[],this.loadPosts(!0))}},(0,s.default)(a,"formatTime",(function(e){if(!e)return"";var t=new Date(e),n=new Date,o=n-t,a=Math.floor(o/6e4),r=Math.floor(o/36e5),s=Math.floor(o/864e5);return a<1?"刚刚":a<60?"".concat(a,"分钟前"):r<24?"".concat(r,"小时前"):s<7?"".concat(s,"天前"):t.toLocaleDateString()})),(0,s.default)(a,"getEmptyText",(function(){var e,t=this,n=(null===(e=this.topicList.find((function(e){return e.id===t.currentTopic})))||void 0===e?void 0:e.name)||"全部";return"all"===this.currentTopic?"暂无帖子":"暂无".concat(n,"相关帖子")})),(0,s.default)(a,"getEmptyDesc",(function(){return"all"===this.currentTopic?"快来发布第一条帖子吧":"换个话题看看其他内容吧"})),(0,s.default)(a,"forceRefresh",(function(){console.log("强制刷新首页数据..."),this.isInitialized=!1,this.postList=[],this.page=1,this.hasMore=!0,this.initializeData()})),a)};t.default=f}).call(this,n("df3c")["default"])},8527:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var o=function(){var e=this.$createElement;this._self._c},a=[]},"90a3":function(e,t,n){"use strict";n.r(t);var o=n("ae12"),a=n("79c8");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("0440");var s=n("828b"),i=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"5f722b1b",null,!1,o["a"],void 0);t["default"]=i.exports},9357:function(e,t,n){},"96f1":function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("cff9");o(n("3240"));var a=o(n("5180"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"99b8":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o}));var o={uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))},uTabs:function(){return n.e("components/uview-ui/components/u-tabs/u-tabs").then(n.bind(null,"6916"))},uLoading:function(){return n.e("components/uview-ui/components/u-loading/u-loading").then(n.bind(null,"2bf0"))}},a=function(){var e=this.$createElement,t=(this._self._c,!this.postList.length&&!this.loading),n=t?this.getEmptyText():null,o=t?this.getEmptyDesc():null;this.$mp.data=Object.assign({},{$root:{g0:t,m0:n,m1:o}})},r=[]},"9be7":function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n("7eb4")),r=o(n("7ca3")),s=o(n("ee10")),i=n("41ad");function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,r.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={name:"SocialMessage",data:function(){return{chatList:[],loading:!1,refreshing:!1,systemUnreadCount:3,likeUnreadCount:12,followUnreadCount:5}},onLoad:function(){this.loadUnreadCounts(),this.loadChatList()},onShow:function(){this.chatList&&0!==this.chatList.length||(console.log("消息页显示时重新加载数据"),this.loadUnreadCounts(),this.loadChatList())},activated:function(){this.chatList&&0!==this.chatList.length||(console.log("消息页激活时重新加载数据"),this.loadUnreadCounts(),this.loadChatList())},methods:{loadUnreadCounts:function(){var e=this;return(0,s.default)(a.default.mark((function t(){var n;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,i.getMessageUnreadCount)();case 3:n=t.sent,n&&0===n.code&&n.data&&(e.systemUnreadCount=n.data.total||0,e.likeUnreadCount=0,e.followUnreadCount=0),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("加载未读消息统计失败:",t.t0);case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},loadChatList:function(){var t=this;return(0,s.default)(a.default.mark((function n(){var o,r,s;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(console.log("开始加载聊天列表..."),t.loading=!0,n.prev=2,o=e.getStorageSync("userid"),o){n.next=8;break}return console.error("用户未登录，无法加载聊天列表"),t.chatList=[],n.abrupt("return");case 8:return n.next=10,(0,i.getConversations)({current:1,size:50});case 10:r=n.sent,console.log("聊天列表API返回:",r),s=[],r&&0===r.code&&r.data&&Array.isArray(r.data)?s=r.data:r&&Array.isArray(r)?s=r:(console.log("没有找到聊天列表或格式不正确"),s=[]),s.length>0?(t.chatList=s.map((function(e){return{id:e.id||e.conversationId,userId:e.otherUserId||e.userId,name:e.otherUserNickname||e.nickname||"用户"+(e.otherUserId||e.userId),avatar:t.formatAvatarUrl(e.otherUserAvatar||e.avatar),lastMessage:e.lastMessageContent||"",lastMessageTime:e.lastMessageTime?new Date(e.lastMessageTime):new Date,lastMessageType:t.getMessageTypeString(e.lastMessageType||1),unreadCount:e.unreadCount||0,isOnline:e.isOnline||!1,isMuted:e.isMuted||!1}})),console.log("会话列表",t.chatList),console.log("聊天列表处理完成，会话数量:",t.chatList.length)):(console.log("没有聊天记录，显示空状态"),t.chatList=[]),n.next=22;break;case 17:n.prev=17,n.t0=n["catch"](2),console.error("加载聊天列表失败:",n.t0),e.showToast({title:"加载失败",icon:"none"}),t.chatList=[];case 22:return n.prev=22,t.loading=!1,t.refreshing=!1,n.finish(22);case 26:case"end":return n.stop()}}),n,null,[[2,17,22,26]])})))()},formatAvatarUrl:function(e){return e?e.startsWith("http")?e:"https://file.foxdance.com.cn"+e:"/static/images/toux.png"},getMessageTypeString:function(e){return{1:"text",2:"image",3:"voice",4:"video",5:"file"}[e]||"text"},formatTime:function(e){var t=new Date,n=t-new Date(e),o=Math.floor(n/6e4),a=Math.floor(n/36e5),r=Math.floor(n/864e5);if(o<1)return"刚刚";if(o<60)return"".concat(o,"分钟前");if(a<24)return"".concat(a,"小时前");if(r<7)return"".concat(r,"天前");var s=new Date(e);return"".concat(s.getMonth()+1,"/").concat(s.getDate())},onRefresh:function(){this.refreshing=!0,this.loadChatList()},openChat:function(t){var n=this;return(0,s.default)(a.default.mark((function o(){var r;return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(console.log("打开聊天:",t),o.prev=1,!(t.unreadCount>0)){o.next=8;break}return o.next=5,(0,i.markMessageAsRead)({senderId:t.userId,receiverId:e.getStorageSync("userid")});case 5:t.unreadCount=0,r=n.chatList.findIndex((function(e){return e.id===t.id})),-1!==r&&n.$set(n.chatList,r,u({},t));case 8:e.navigateTo({url:"/pagesSub/social/chat/detail?userId=".concat(t.userId,"&nickname=").concat(encodeURIComponent(t.name),"&avatar=").concat(encodeURIComponent(t.avatar))}),o.next=15;break;case 11:o.prev=11,o.t0=o["catch"](1),console.error("打开聊天失败:",o.t0),e.navigateTo({url:"/pagesSub/social/chat/detail?userId=".concat(t.userId,"&nickname=").concat(encodeURIComponent(t.name),"&avatar=").concat(encodeURIComponent(t.avatar))});case 15:case"end":return o.stop()}}),o,null,[[1,11]])})))()},showChatActions:function(t){var n=this,o=["置顶",t.isMuted?"取消免打扰":"免打扰","删除聊天"];e.showActionSheet({itemList:o,success:function(e){switch(e.tapIndex){case 0:n.toggleChatTop(t);break;case 1:n.toggleChatMute(t);break;case 2:n.deleteChat(t);break}}})},toggleChatTop:function(e){this.$u.toast("置顶成功")},toggleChatMute:function(e){e.isMuted=!e.isMuted,this.$u.toast(e.isMuted?"已开启免打扰":"已关闭免打扰")},deleteChat:function(t){var n=this;e.showModal({title:"确认删除",content:"确定要删除这个聊天吗？",success:function(e){if(e.confirm){var o=n.chatList.findIndex((function(e){return e.id===t.id}));o>-1&&(n.chatList.splice(o,1),n.$u.toast("删除成功"))}}})},goSearch:function(){e.navigateTo({url:"/pagesSub/social/search/chat"})},startNewChat:function(){e.navigateTo({url:"/pagesSub/social/contact/select"})},goSystemMessages:function(){e.navigateTo({url:"/pagesSub/social/message/system"})},goLikeMessages:function(){e.navigateTo({url:"/pagesSub/social/message/likes"})},goFollowMessages:function(){e.navigateTo({url:"/pagesSub/social/message/followers"})},forceRefresh:function(){console.log("强制刷新消息页数据..."),this.chatList=[],this.loadUnreadCounts(),this.loadChatList()}}};t.default=l}).call(this,n("df3c")["default"])},a0ea:function(e,t,n){"use strict";var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n("808e")),r=o(n("90a3")),s=o(n("b2fb")),i=o(n("e80d")),c=o(n("bea8")),u={name:"SocialMain",components:{TabBar:function(){n.e("pagesSub/social/components/TabBar").then(function(){return resolve(n("5bed"))}.bind(null,n)).catch(n.oe)},HomePage:a.default,DiscoverPage:r.default,PublishPage:s.default,MessagePage:i.default,ProfilePage:c.default},data:function(){return{currentTab:0,homePageKey:0,discoverPageKey:0,messagePageKey:0,profilePageKey:0}},onLoad:function(e){e.tab&&(this.currentTab=parseInt(e.tab))},onShow:function(){this.refreshCurrentTab()},methods:{handleTabChange:function(e){var t=e.index,n=this.currentTab;this.currentTab=t,n!==t&&this.refreshCurrentTab(),console.log("切换到选项卡:",t)},refreshCurrentTab:function(){var e=this;this.$nextTick((function(){switch(e.currentTab){case 0:e.refreshHomePage();break;case 1:e.refreshDiscoverPage();break;case 3:e.refreshMessagePage();break;case 4:e.refreshProfilePage();break;default:console.log("当前tab无需刷新:",e.currentTab)}}))},refreshHomePage:function(){this.$refs.homePage?this.$refs.homePage.forceRefresh?this.$refs.homePage.forceRefresh():this.$refs.homePage.onRefresh?this.$refs.homePage.onRefresh():this.homePageKey+=1:this.homePageKey+=1},refreshDiscoverPage:function(){this.$refs.discoverPage?this.$refs.discoverPage.forceRefresh?this.$refs.discoverPage.forceRefresh():this.$refs.discoverPage.loadDiscoverData?this.$refs.discoverPage.loadDiscoverData():this.discoverPageKey+=1:this.discoverPageKey+=1},refreshMessagePage:function(){this.$refs.messagePage?this.$refs.messagePage.forceRefresh?this.$refs.messagePage.forceRefresh():this.$refs.messagePage.loadChatList?(this.$refs.messagePage.loadUnreadCounts(),this.$refs.messagePage.loadChatList()):this.messagePageKey+=1:this.messagePageKey+=1},refreshProfilePage:function(){console.log("刷新我的页面数据..."),this.$refs.profilePage?this.$refs.profilePage.forceRefresh?this.$refs.profilePage.forceRefresh():this.$refs.profilePage.loadUserInfo?this.$refs.profilePage.loadUserInfo():this.$refs.profilePage.initializeData?this.$refs.profilePage.initializeData():this.profilePageKey+=1:this.profilePageKey+=1}}};t.default=u},ae12:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o}));var o={uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))},uAvatar:function(){return n.e("components/uview-ui/components/u-avatar/u-avatar").then(n.bind(null,"8d35"))}},a=function(){var e=this.$createElement;this._self._c},r=[]},b2fb:function(e,t,n){"use strict";n.r(t);var o=n("5129"),a=n("4173");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("ef7f");var s=n("828b"),i=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"254180e3",null,!1,o["a"],void 0);t["default"]=i.exports},b913:function(e,t,n){"use strict";n.r(t);var o=n("a0ea"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},bea8:function(e,t,n){"use strict";n.r(t);var o=n("c106"),a=n("ef21");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("3427");var s=n("828b"),i=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"7bb4fb94",null,!1,o["a"],void 0);t["default"]=i.exports},c106:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o}));var o={uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))},uAvatar:function(){return n.e("components/uview-ui/components/u-avatar/u-avatar").then(n.bind(null,"8d35"))},uTabs:function(){return n.e("components/uview-ui/components/u-tabs/u-tabs").then(n.bind(null,"6916"))},uEmpty:function(){return n.e("components/uview-ui/components/u-empty/u-empty").then(n.bind(null,"8fa1"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.tabs[e.currentTab]&&e.tabs[e.currentTab].data.length>0),o=!n||0===e.currentTab||1===e.currentTab||4===e.currentTab||2!==e.currentTab&&3!==e.currentTab?null:e.__map(e.tabs[e.currentTab].data,(function(t,n){var o=e.__get_orig(t),a={id:t.id,nickname:t.nickname};return{$orig:o,a0:a}})),a=n?null:e.getEmptyText();e.$mp.data=Object.assign({},{$root:{g0:n,l0:o,m0:a}})},r=[]},e0ee:function(e,t,n){},e80d:function(e,t,n){"use strict";n.r(t);var o=n("f014"),a=n("60de");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("eff6");var s=n("828b"),i=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"36f9cf8e",null,!1,o["a"],void 0);t["default"]=i.exports},ef21:function(e,t,n){"use strict";n.r(t);var o=n("7b6c"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},ef7f:function(e,t,n){"use strict";var o=n("e0ee"),a=n.n(o);a.a},eff6:function(e,t,n){"use strict";var o=n("25cf"),a=n.n(o);a.a},f014:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o}));var o={uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))},uAvatar:function(){return n.e("components/uview-ui/components/u-avatar/u-avatar").then(n.bind(null,"8d35"))},uLoading:function(){return n.e("components/uview-ui/components/u-loading/u-loading").then(n.bind(null,"2bf0"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.chatList,(function(t,n){var o=e.__get_orig(t),a=e.formatTime(t.lastMessageTime);return{$orig:o,m0:a}}))),o=!e.chatList.length&&!e.loading;e.$mp.data=Object.assign({},{$root:{l0:n,g0:o}})},r=[]},f323:function(e,t,n){},f33b:function(e,t,n){},ffde:function(e,t,n){"use strict";var o=n("9357"),a=n.n(o);a.a}},[["96f1","common/runtime","common/vendor"]]]);