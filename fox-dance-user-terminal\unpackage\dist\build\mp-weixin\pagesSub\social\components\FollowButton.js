(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/components/FollowButton"],{"5e89":function(e,t,n){},"607e":function(e,t,n){"use strict";n.r(t);var r=n("8018"),o=n("672c");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("cc16");var u=n("828b"),a=Object(u["a"])(o["default"],r["b"],r["c"],!1,null,"7f8a937a",null,!1,r["a"],void 0);t["default"]=a.exports},"672c":function(e,t,n){"use strict";n.r(t);var r=n("e098"),o=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=o.a},8018:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r}));var r={uButton:function(){return n.e("components/uview-ui/components/u-button/u-button").then(n.bind(null,"fcb0"))}},o=function(){var e=this.$createElement;this._self._c},i=[]},cc16:function(e,t,n){"use strict";var r=n("5e89"),o=n.n(r);o.a},e098:function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("7eb4")),i=r(n("ee10")),u=r(n("7ca3")),a=r(n("3b2d")),s=n("41ad");function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,u.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f={name:"FollowButton",props:{user:{type:Object,required:!0,default:function(){return{}}},followed:{type:Boolean,default:!1},size:{type:String,default:"mini",validator:function(e){return["large","normal","mini"].includes(e)}},disabled:{type:Boolean,default:!1},customStyle:{type:[String,Object],default:""},showLoading:{type:Boolean,default:!0}},data:function(){return{isFollowed:!1,loading:!1}},computed:{buttonStyle:function(){var e={fontSize:this.getFontSize(),height:this.getHeight(),minWidth:this.getMinWidth(),borderRadius:this.getBorderRadius()};return"string"===typeof this.customStyle?"".concat(this.customStyle,"; ").concat(this.objectToStyle(e)):"object"===(0,a.default)(this.customStyle)?l(l({},e),this.customStyle):e}},watch:{followed:{immediate:!0,handler:function(e){this.isFollowed=e}}},methods:{handleFollow:function(){var t=this;return(0,i.default)(o.default.mark((function n(){var r,i,u,a,c;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!t.disabled&&!t.loading){n.next=2;break}return n.abrupt("return");case 2:if(t.showLoading&&(t.loading=!0),n.prev=3,r=e.getStorageSync("userid"),r){n.next=8;break}return e.showToast({title:"请先登录",icon:"none"}),n.abrupt("return");case 8:if(i=t.user.id||t.user.userId,i){n.next=12;break}return e.showToast({title:"用户信息错误",icon:"none"}),n.abrupt("return");case 12:if(r!=i){n.next=15;break}return e.showToast({title:"不能关注自己",icon:"none"}),n.abrupt("return");case 15:if(!t.isFollowed){n.next=28;break}return n.next=18,(0,s.unfollowUser)(i);case 18:if(u=n.sent,!u||0!==u.code){n.next=25;break}t.isFollowed=!1,e.showToast({title:"已取消关注",icon:"success"}),t.$emit("unfollow",{user:t.user,isFollowed:!1}),n.next=26;break;case 25:throw new Error((null===(a=u)||void 0===a?void 0:a.message)||"取消关注失败");case 26:n.next=38;break;case 28:return n.next=30,(0,s.followUser)(i);case 30:if(u=n.sent,!u||0!==u.code){n.next=37;break}t.isFollowed=!0,e.showToast({title:"关注成功",icon:"success"}),t.$emit("follow",{user:t.user,isFollowed:!0}),n.next=38;break;case 37:throw new Error((null===(c=u)||void 0===c?void 0:c.message)||"关注失败");case 38:t.$emit("change",{user:t.user,isFollowed:t.isFollowed}),n.next=46;break;case 41:n.prev=41,n.t0=n["catch"](3),console.error("关注操作失败:",n.t0),e.showToast({title:n.t0.message||"操作失败，请重试",icon:"none"}),t.$emit("error",{user:t.user,error:n.t0});case 46:return n.prev=46,t.showLoading&&setTimeout((function(){t.loading=!1}),500),n.finish(46);case 49:case"end":return n.stop()}}),n,null,[[3,41,46,49]])})))()},getFontSize:function(){var e={large:"32rpx",normal:"28rpx",mini:"24rpx"};return e[this.size]||e.mini},getHeight:function(){var e={large:"80rpx",normal:"64rpx",mini:"56rpx"};return e[this.size]||e.mini},getMinWidth:function(){var e={large:"120rpx",normal:"100rpx",mini:"80rpx"};return e[this.size]||e.mini},getBorderRadius:function(){var e={large:"40rpx",normal:"32rpx",mini:"28rpx"};return e[this.size]||e.mini},objectToStyle:function(e){return Object.keys(e).map((function(t){var n=t.replace(/([A-Z])/g,"-$1").toLowerCase();return"".concat(n,": ").concat(e[t])})).join("; ")},setFollowStatus:function(e){this.isFollowed=e},getFollowStatus:function(){return this.isFollowed}}};t.default=f}).call(this,n("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesSub/social/components/FollowButton-create-component',
    {
        'pagesSub/social/components/FollowButton-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("607e"))
        })
    },
    [['pagesSub/social/components/FollowButton-create-component']]
]);
