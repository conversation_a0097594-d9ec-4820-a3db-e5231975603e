{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/login/login.vue?f36c", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/login/login.vue?d974", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/login/login.vue?f2ea", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/login/login.vue?352b", "uni-app:///pages/login/login.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/login/login.vue?f71c", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/login/login.vue?b6a1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "bgs", "safeAreaTop", "menuButtonInfoHeight", "isAgreement", "avatar", "nickname", "imgbaseUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoad", "methods", "qxTap", "uni", "url", "setTimeout", "subTap", "icon", "title", "duration", "chooseAvatarsc", "driver", "console", "that", "userData", "tisTap", "IsAgree", "getCode", "provider", "success", "getPhoneNumber", "code", "phone_code", "pid", "params", "parentId", "silverGrantId", "getContractData", "showPass", "codeChange", "navTo", "checkId", "confirmId", "identity"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC4L;AAC5L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+tB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC8EnvB;AAMA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IAEA;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;QACAC;QACAA;UACAC;QACA;MACA;QACAC;UACAF;QACA;MACA;IACA;IACAG;MACA;QACAH;UACAI;UACAC;UACAC;QACA;QACA;MACA;MACA;QACAN;UACAI;UACAC;UACAC;QACA;QACA;MACA;MACAN;QACAK;MACA;MACA;QACA;QACAZ;QACAC;MACA;QACA;UACAM;UACAA;YACAK;YACAC;UACA;UACA;YACAN;YACAE;cACAF;gBACAC;cACA;YACA;UACA;YACAC;cACAF;YACA;UACA;QACA,QAEA;MACA;IACA;IACA;IACAO;MACA;MACA;MACA;MACAP;QACAK;MACA;MACA;QAAAG;MAAA;QACAC;QACA;UACAT;UACAU;QACA;MACA;IAEA;IACA;IACAC;MACAX;QACAK;MACA;MACA;MACA;QACA;UACAI;UACAC;UACAA;UACAV;QACA;MACA;IACA;IACAY;MACAZ;QACAK;QACAD;QACAE;MACA;IACA;IACAO;MACA;IACA;IACAC;MACA;MACAd;QACAe;QAAA;QACAC;UACAP;UACAC;UACA;QACA;MACA;IACA;IACAO;MACAR;MACA;MACA;MAEA;QACA;UACA;UACA;QACA;QACAT;UACAK;UACAD;QACA;QACA;QACA;QACA;UACAc;UACAC;UACAC;UACA;UACA;UACA;QACA;;QACA;UACAC,yCACAA;YACAC;UAAA,EACA;QACA;QACA;UACAD,yCACAA;YACAE;UAAA,EACA;QACA;QACA;QACA,0CACAF,QACA;UACAZ;UACA;YACAT;YACAA;cACAK;cACAD;cACAE;YACA;YACAN;YACA;YACAA;YACAU;YACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACA;YACAA;YACAV;YACAA;cACAK;cACAD;cACAE;YACA;;YAEA;UACA;QAEA;MACA;QACAN;UACAK;UACAD;UACAE;QACA;MACA;IACA;IACA;IACAkB;MACA;MACA,kCACA;QACAf;QACA;UACA;UACA;YACA;YACAP;cACAF;gBACAC;cACA;YACA;UACA;YACA;YACA;cACA;cACAS;YACA;cACA;gBACAV;gBACAA;kBACAC;gBACA;cACA;gBACAC;kBACAF;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAyB;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA3B;QACAC;MACA;IACA;IACA2B;MACAC;QACAC;MACA;QACA;UACA;YACA9B;UACA;YACAA;UACA;UAEAA;YACAC;UACA;QACA;MACA;IACA;EAGA;AAEA;AAAA,2B;;;;;;;;;;;;;AClYA;AAAA;AAAA;AAAA;AAA82C,CAAgB,8uCAAG,EAAC,C;;;;;;;;;;;ACAl4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-navbar/u-navbar\" */ \"@/components/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login\" :style=\"{backgroundSize:bgs}\">\r\n\t\t<u-navbar title=\"FOX舞蹈\" back-icon-color=\"#333\" back-icon-size=\"42\"\r\n\t\t\t:background=\"{background:'transparent'}\" :border-bottom=\"false\" title-color=\"#333\" title-size=\"32\">\r\n\r\n\t\t</u-navbar>\r\n\t\t\r\n\t\t<view class=\"logt_bj\" :style=\"'top:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;'\"><image src=\"/static/images/loginbj.png\"></image></view>\r\n\t\t\r\n\t\t<view class=\"acount-login\">\r\n\t\t\t<!-- <view class=\" login_text \">\r\n\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t欢迎来到\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"\">\r\n\t\t\t\t\tFOX空间\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"acount-login_a\">\r\n\t\t\t\t<text>手机号</text>\r\n\t\t\t\t<text>登陆 / 注册</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"acount-login_b\">未注册的手机将会自动注册</view>\r\n\t\t\t<!-- btn样式在 App.vue 文件中 -->\r\n\t\t\t<view class=\"btn\">\r\n\t\t\t\t手机号一键登陆\r\n\t\t\t\t<button v-if=\"isAgreement\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\" size=\"lg\"></text>\r\n\t\t\t\t</button>\r\n\t\t\t\t<button v-else size=\"lg\" @click=\"tisTap\">\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"m-b-30 sm flex row-center\" @click=\"IsAgree\" style=\"height:56rpx;\">\r\n\t\t\t\t<image v-if=\"!isAgreement\" src=\"/static/images/Mr.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t<image v-else src=\"/static/images/Mr_x.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t我已阅读并同意授权\r\n\t\t\t\t\t<view @click.stop=\"navTo('/pages/login/xieYi?type=1')\" style=\"color: #945048;\">《用户注册购卡协议》</view>\r\n\t\t\t\t\t<!-- 和\r\n\t\t\t\t\t<view @click.stop=\"navTo('/pages/login/xieYi?type=2')\" style=\"color: #945048;\">《隐私政策》</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"logo\"><image src=\"/static/images/logo.png\"></image></view>\r\n\t\t\r\n\t\t<view class=\"logsetTc\" v-if=\"editzlToggle\">\r\n\t\t\t<view class=\"logsetTc_n\">\r\n\t\t\t\t<view class=\"logsetTc_t\">\r\n\t\t\t\t\t<view>请先设置头像和昵称</view>\r\n\t\t\t\t\t<text>注册、登录小程序</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"logsetTc_c\">\r\n\t\t\t\t\t<view class=\"qyrz_con_li tx\">\r\n\t\t\t\t\t\t<button open-type=\"chooseAvatar\" @chooseavatar=\"chooseAvatarsc\">获取头像</button>\r\n\t\t\t\t\t\t<view class=\"qyrz_con_li_l\">头像</view>\r\n\t\t\t\t\t\t<view class=\"qyrz_con_li_r\">\r\n\t\t\t\t\t\t\t<view class=\"uni-input\" style=\"height:auto;\"><image :src=\"avatar == '' ? '/static/images/toux.png' : imgbaseUrl + avatar\" mode=\"aspectFill\" class=\"edma_one_a\"></image><image src=\"/static/images/right_more.png\"></image></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"qyrz_con_li\">\r\n\t\t\t\t\t\t<view class=\"qyrz_con_li_l\">昵称</view>\r\n\t\t\t\t\t\t<view class=\"qyrz_con_li_r\">\r\n\t\t\t\t\t\t\t<input type=\"nickname\" placeholder=\"请输入昵称\" v-model=\"nickname\"  maxlength=\"8\" />\r\n\t\t\t\t\t\t\t<!-- <view class=\"uni-input\">{{nickname}}<image src=\"/static/images/icon3.png\"></image></view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"logsetTc_f\">\r\n\t\t\t\t\t<view @click=\"qxTap\">跳过</view>\r\n\t\t\t\t\t<view @click=\"subTap\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tlogin,\r\n\t\tupImg,\r\n\t\tuserInfoApi,\r\n\t\ttoeditUserApi,\r\n\t\tgetContractApi\r\n\t} from '@/config/http.achieve.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbgs: '',\r\n\t\t\t\tsafeAreaTop:wx.getWindowInfo().safeArea.top,\r\n\t\t\t\tmenuButtonInfoHeight:uni.getMenuButtonBoundingClientRect().height,\r\n\t\t\t\tisAgreement: false,\r\n\t\t\t\tavatar:'',//头像\r\n\t\t\t\tnickname:'',//昵称\r\n\t\t\t\timgbaseUrl:'',\r\n\t\t\t\teditzlToggle:false,//编辑资料弹窗\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\t\tlet sys = uni.getSystemInfoSync()\r\n\t\t\tthis.bgs = '100%' + ' ' + (Number(sys.statusBarHeight) + 456) + 'px'\r\n\t\t\t\r\n\t\t\tthis.getCode()\r\n\t\t\t// this.getLoginCode()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//取消弹窗\r\n\t\t\tqxTap(){\r\n\t\t\t\tthis.editzlToggle = false;\r\n\t\t\t\tif(uni.getStorageSync('tokenwx')){\r\n\t\t\t\t\tuni.removeStorageSync('tokenwx')\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t}, 1500)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsubTap(){\r\n\t\t\t\tif (this.avatar == '/static/images/avatar.png') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t\ttitle: '请上传头像',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.nickname.split(\" \").join(\"\").length == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t\ttitle: '请输入昵称',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\ttoeditUserApi({\r\n\t\t\t\t\t// userId:uni.getStorageSync('user').userId,\r\n\t\t\t\t\tavatar:this.avatar,\r\n\t\t\t\t\tnickname:this.nickname\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle:'设置成功',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif(uni.getStorageSync('tokenwx')){\r\n\t\t\t\t\t\t\tuni.removeStorageSync('tokenwx')\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 1500)\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t}, 1500)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//获取头像\r\n\t\t\tchooseAvatarsc(event){\r\n\t\t\t  // console.log(event,'event')\r\n\t\t\t  var that = this;\r\n\t\t\t  const tempFilePaths = event.detail.avatarUrl\r\n\t\t\t  uni.showLoading({\r\n\t\t\t  \ttitle:'加载中'\r\n\t\t\t  })\r\n\t\t\t  upImg(tempFilePaths, 'file',{driver:'cos'}).then(ress => {\r\n\t\t\t  \tconsole.log('上传图片',ress)\r\n\t\t\t  \tif (ress.code == 1) {\r\n\t\t\t  \t\tuni.hideLoading();\r\n\t\t\t  \t\tthat.avatar = ress.data.file.url\r\n\t\t\t  \t}\r\n\t\t\t  })\r\n\t\t\t  \r\n\t\t\t},\r\n\t\t\t//个人信息\r\n\t\t\tuserData(){\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuserInfoApi({}).then(res => {\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tconsole.log('个人信息',res);\r\n\t\t\t\t\t\tthat.avatar = res.data.avatar;\r\n\t\t\t\t\t\tthat.nickname = res.data.nickname == '' ? '微信昵称' : res.data.nickname;\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttisTap(){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请先阅读并同意授权《用户注册购卡协议》',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tIsAgree() {\r\n\t\t\t\tthis.isAgreement = !this.isAgreement\r\n\t\t\t},\r\n\t\t\tgetCode() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin', //使用微信登录\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\tthat.minicode = res.code\r\n\t\t\t\t\t\t// return\r\n\t\t\t\t\t},\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetPhoneNumber(phone) {\r\n\t\t\t\tconsole.log(phone, '45646546546');\r\n\t\t\t\t// return false;\r\n\t\t\t\tlet that = this\r\n\t\t\t\t\r\n\t\t\t\tif (this.isAgreement == true) {\r\n\t\t\t\t\tif (!phone.detail.errMsg.includes('getPhoneNumber:ok')) {\r\n\t\t\t\t\t\t//this.getLoginCode()\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '登录中...',\r\n\t\t\t\t\t\ticon: 'loading'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tconst parentId = uni.getStorageSync('parentId')\r\n\t\t\t\t\tconst silverGrantId = uni.getStorageSync('silverGrantId')\r\n\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\tcode: this.minicode,\r\n\t\t\t\t\t\tphone_code:phone.detail.code,\r\n\t\t\t\t\t\tpid:uni.getStorageSync(\"pid\") ? uni.getStorageSync(\"pid\") : '',\r\n\t\t\t\t\t\t// iv: phone.detail.iv,\r\n\t\t\t\t\t\t// encryptedData: phone.detail.encryptedData,\r\n\t\t\t\t\t\t// superior:uni.getStorageSync(\"pid\") ? uni.getStorageSync(\"pid\") : '',\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (parentId) {\r\n\t\t\t\t\t\tparams = {\r\n\t\t\t\t\t\t\t...params,\r\n\t\t\t\t\t\t\tparentId: parentId\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (silverGrantId) {\r\n\t\t\t\t\t\tparams = {\r\n\t\t\t\t\t\t\t...params,\r\n\t\t\t\t\t\t\tsilverGrantId: silverGrantId\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// return console.log(params,'54654654');\r\n\t\t\t\t\tlogin({\r\n\t\t\t\t\t\t...params,\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tconsole.log(res,'登录')\r\n\t\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tuni.setStorageSync('token', res.data.userinfo.token)\r\n\t\t\t\t\t\t\t// uni.setStorageSync('token', '49e2e458-2c96-489d-a19a-9aacf70b3c13')\r\n\t\t\t\t\t\t\tuni.setStorageSync('userid', res.data.userinfo.id);\r\n\t\t\t\t\t\t\tthat.getContractData(res.data.userinfo.avatar);//获取未签署的合同\r\n\t\t\t\t\t\t\t/*\r\n\t\t\t\t\t\t\tif(res.data.userinfo.avatar == '/static/images/avatar.png'){\r\n\t\t\t\t\t\t\t\t//需要设置头像昵称\r\n\t\t\t\t\t\t\t\tthat.editzlToggle = true;\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tif(uni.getStorageSync('tokenwx')){\r\n\t\t\t\t\t\t\t\t\tuni.removeStorageSync('tokenwx')\r\n\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t\t\t}, 1500)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}*/\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.getCode()\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '登录失败',\r\n\t\t\t\t\t\t\t\ticon: 'error',\r\n\t\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// this.getLoginCode()\r\n\t\t\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先同意协议',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000,\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//获取未签署的合同\r\n\t\t\tgetContractData(avatar){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tgetContractApi({\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log('获取未签署的合同',res)\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\t// res.data = 22;\r\n\t\t\t\t\t\tif(res.data){\r\n\t\t\t\t\t\t\t//需要签署合同\r\n\t\t\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\t\turl:'/pages/index/signing?id=' + res.data\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},1200)\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t//不需要签署\r\n\t\t\t\t\t\t\tif(avatar == '/static/images/avatar.png'){\r\n\t\t\t\t\t\t\t\t//需要设置头像昵称\r\n\t\t\t\t\t\t\t\tthat.editzlToggle = true;\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tif(uni.getStorageSync('tokenwx')){\r\n\t\t\t\t\t\t\t\t\tuni.removeStorageSync('tokenwx')\r\n\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t\t\t}, 1500)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 是否显示密码\r\n\t\t\tshowPass() {\r\n\t\t\t\tthis.isType = !this.isType\r\n\t\t\t\t// if (this.type == 'text') {\r\n\t\t\t\t// \tthis.type = 'password'\r\n\t\t\t\t// } else {\r\n\t\t\t\t// \tthis.type = 'text'\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\r\n\t\t\tcodeChange(tip) {\r\n\t\t\t\tthis.codeTips = tip\r\n\t\t\t},\r\n\t\t\tnavTo(url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcheckId(type) {\r\n\t\t\t\tconfirmId({\r\n\t\t\t\t\tidentity: type\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tif (type == 1) {\r\n\t\t\t\t\t\t\tuni.setStorageSync('identity', 'user')\r\n\t\t\t\t\t\t} else if (type == 2) {\r\n\t\t\t\t\t\t\tuni.setStorageSync('identity', 'master')\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t\r\n\t\t},\r\n\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.logo{\r\n\t\twidth:233rpx;\r\n\t\theight:47rpx;\r\n\t\tposition: fixed;\r\n\t\tbottom:10%;\r\n\t\tleft:50%;\r\n\t\tmargin-left:-116rpx;\r\n\t}\r\n\t.logo image{display:block;width: 100%;height: 100%;}\r\n\t.logt_bj{\r\n\t\twidth:620rpx;\r\n\t\theight:600rpx;\r\n\t\tposition: fixed;\r\n\t\ttop:0;\r\n\t\tright:0;\r\n\t\tz-index: 1;\r\n\t}\r\n\t.logt_bj image{display:block;width: 100%;height: 100%;}\r\n\t.color {\r\n\t\tcolor: #bfbfbf;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground-color: #fff !important;\r\n\t\tpadding: 0;\r\n\r\n\t\t.login {\r\n\t\t\t//背景图片根据需求修改\r\n\t\t\t// background-image: url(@/static/login/login_top_bgi.png);\r\n\t\t\t// background-image: url(https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/userreport/login_top_bgi.png);\r\n\t\t\tbackground-size: 100% 912rpx;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tmin-height: 100vh;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\r\n\t\t\t.acount-login {\r\n\t\t\t\twidth: 750rpx;\r\n\t\t\t\theight: auto;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tmin-height: 0;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tpadding-top: 220rpx;\r\n\t\t\t\tposition:relative;\r\n\t\t\t\tz-index: 2;\r\n\t\t\t\t.login_text {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tview{\r\n\t\t\t\t\t\t&:nth-child(1){\r\n\t\t\t\t\t\t\tpadding-left: 128rpx;\r\n\t\t\t\t\t\t\tfont-size: 50rpx;\r\n\t\t\t\t\t\t\tfont-family: Maoken Glitch Sans;\r\n\t\t\t\t\t\t\tfont-size: 50rpx;\r\n\t\t\t\t\t\t\tcolor: #131315;\r\n\t\t\t\t\t\t\tline-height: 58rpx;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:nth-child(2){\r\n\t\t\t\t\t\t\tfont-family: Maoken Glitch Sans;\r\n\t\t\t\t\t\t\tpadding-left: 210rpx;\r\n\t\t\t\t\t\t\tmargin-top: 15rpx;\r\n\t\t\t\t\t\t\tfont-size: 50rpx;\r\n\t\t\t\t\t\t\tcolor: #131315;\r\n\t\t\t\t\t\t\tfont-weight: Regular;\r\n\t\t\t\t\t\t\tline-height: 59rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.sm {\r\n\t\t\t\t\tmargin-top: 46rpx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 22rpx;\r\n\t\t\t\t\t\theight: 22rpx;\r\n\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tview {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #131315!important;\r\n\t\t\t\t\t\tline-height: 26rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.btn {\r\n\t\tmargin:270rpx 70rpx 0 70rpx;\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 45rpx;\r\n\t\tposition: relative;\r\n\t\toverflow:hidden;\r\n\t\tfont-size:38rpx;\r\n\t}\r\n\t.btn button{\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\theight: 90rpx;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;left: 0;\r\n\t\topacity: 0;\r\n\t}\r\n\t.acount-login_a{\r\n\t\tmargin:0 70rpx;\r\n\t\theight:auto;\r\n\t\toverflow:hidden;\r\n\t}\r\n\t.acount-login_a text{\r\n\t\tdisplay: block;\r\n\t\tfont-size: 70rpx;\r\n\t\tcolor:#000;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.acount-login_b{color:#a19494;font-size: 24rpx;margin:30rpx 70rpx 0 70rpx;}\r\n</style>", "import mod from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753622575469\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}