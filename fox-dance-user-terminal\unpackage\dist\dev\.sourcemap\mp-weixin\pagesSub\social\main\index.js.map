{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?8f1a", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?bff4", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?be7b", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?d4ff", "uni-app:///pagesSub/social/main/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "uni-app:///pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?de44", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?4e32", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?ead9", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?4ea8", "uni-app:///pagesSub/social/discover/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?659a", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?132d", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?0aff", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?d711", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?e559", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?684e", "uni-app:///pagesSub/social/publish/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?547e", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?b521", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?2686", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?53ed", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?f753", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?f573", "uni-app:///pagesSub/social/message/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?d244", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?a1db", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?2fa0", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?306b", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?eccc", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?16a1", "uni-app:///pagesSub/social/profile/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?c704", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?e870", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?0ac5", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/main/index.vue?f83e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "TabBar", "HomePage", "DiscoverPage", "PublishPage", "MessagePage", "ProfilePage", "data", "currentTab", "homePageKey", "discoverPageKey", "messagePageKey", "profile<PERSON>age<PERSON>ey", "onLoad", "onShow", "methods", "handleTabChange", "console", "refreshCurrentTab", "refreshHomePage", "refreshDiscoverPage", "refreshMessagePage", "refreshProfilePage", "PostCard", "postList", "loading", "refreshing", "page", "pageSize", "currentTopic", "currentTopicIndex", "topicList", "id", "hasMore", "isInitialized", "activated", "initializeData", "loadHotTags", "result", "allOption", "loadPosts", "refresh", "params", "current", "size", "sortField", "sortOrder", "selectedTag", "posts", "userId", "title", "username", "userAvatar", "content", "coverImage", "images", "topics", "topicId", "likeCount", "commentCount", "isLiked", "createTime", "uni", "icon", "onRefresh", "loadMore", "formatTime", "onPostLike", "post", "index", "goPostDetail", "url", "goUserProfile", "goSearch", "selectTopic", "dateString", "currentTopicName", "FollowButton", "hotTopics", "recommendUsers", "hotPosts", "<PERSON><PERSON><PERSON><PERSON>", "loadDiscoverData", "loadHotTopics", "limit", "cover", "tag", "postCount", "description", "getDefaultTopics", "loadRecommendUsers", "nickname", "avatar", "isFollowed", "followersCount", "getDefaultRecommendUsers", "loadHotPosts", "timeRange", "formatAvatarUrl", "truncate<PERSON><PERSON><PERSON>", "getDefaultPosts", "loadFeaturedContent", "subtitle", "onUserFollow", "user", "topic", "item", "currentUserId", "userIds", "map", "filter", "userInfo", "postTitle", "postContent", "selectedImages", "coverImageUrl", "selectedTopics", "selectedLocation", "visibility", "publishing", "showTopicModal", "showLocationModal", "topicKeyword", "allTopics", "nearbyLocations", "address", "computed", "canPublish", "visibilityText", "public", "friends", "private", "filteredTopics", "duration", "mounted", "checkUserLogin", "setTimeout", "testApiConnection", "method", "timeout", "response", "loadUserInfo", "hotTags", "goBack", "success", "chooseImage", "maxCount", "count", "sizeType", "sourceType", "tempFilePaths", "mask", "fail", "removeImage", "handleImageSelectFromTabBar", "app", "toggleTopic", "searchTopics", "selectLocation", "latitude", "longitude", "selectLocationItem", "clearLocation", "setVisibility", "itemList", "publishPost", "postData", "tags", "locationName", "locationLatitude", "locationLongitude", "locationAddress", "isPublic", "status", "chatList", "systemUnreadCount", "likeUnreadCount", "followUnreadCount", "loadUnreadCounts", "loadChatList", "conversations", "Array", "conversation", "lastMessage", "lastMessageTime", "lastMessageType", "unreadCount", "isOnline", "isMuted", "getMessageTypeString", "openChat", "chat", "senderId", "receiverId", "encodeURIComponent", "showChatActions", "toggleChatTop", "toggleChatMute", "deleteChat", "startNewChat", "goSystemMessages", "goLikeMessages", "goFollowMessages", "forceRefresh", "bio", "danceType", "privatePostCount", "followingCount", "draftCount", "tabs", "userProfile", "loadPrivatePostCount", "getCurrentUserId", "loadTabData", "tabIndex", "loadUserPosts", "loadPrivatePosts", "loadLikedPosts", "loadFollowingUsers", "users", "type", "loadFollowersUsers", "switchTab", "scanCode", "goSettings", "editAvatar", "editProfile", "goLikeList", "viewPost", "currentTabData", "tab", "getEmptyText", "onUserUnfollow", "onFollowChange", "updateUserFollowStatus", "followingUser", "followerUser"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0BlwB;AACA;AACA;AACA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;MAEAC;IACA;IAEA;IACAC;MAAA;MACA;QACA;UACA;YAAA;YACA;YACA;UACA;YAAA;YACA;YACA;UACA;YAAA;YACA;YACA;UACA;YAAA;YACA;YACA;UACA;YACAD;QAAA;MAEA;IACA;IAEA;IACAE;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACAL;MACA;QACA;QACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsElwB;AAKA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAlB;EACAC;IACAuB;EACA;EACAhB;IACA;MACAiB;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,YACA;QAAAhC;QAAAiC;MAAA,GACA;QAAAjC;QAAAiC;MAAA,GACA;QAAAjC;QAAAiC;MAAA,GACA;QAAAjC;QAAAiC;MAAA,GACA;QAAAjC;QAAAiC;MAAA,GACA;QAAAjC;QAAAiC;MAAA,GACA;QAAAjC;QAAAiC;MAAA,GACA;QAAAjC;QAAAiC;MAAA,GACA;QAAAjC;QAAAiC;MAAA,GACA;QAAAjC;QAAAiC;MAAA,EACA;MACAC;MACAC;IACA;EACA;EACArB;IACA;EACA;EAEA;EACAC;IACA;IACA;MACA;IACA;EACA;EAEA;EACAqB;IACA;IACA;MACA;IACA;EACA;EACApB;IACA;IACAqB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAnB;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACArB;gBAEA,IACAqB,UACAA,qBACAA,eACAA,wBACA;kBACA;kBACAC;kBACA,oBACAA,mDACAD;oBAAA;sBACAvC;sBACAiC;oBACA;kBAAA,IACA;kBACAf;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuB;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACAxB;gBAAA;cAAA;gBAIAA;gBACA;gBAAA;gBAEAyB;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;kBACA;kBACAC,oCACA;oBAAA;kBAAA,EACA;kBACA;oBACAL;kBACA;gBACA;gBAEAzB;gBAAA;gBAAA,OACA;cAAA;gBAAAqB;gBACArB;gBAEA;kBACA+B;oBAAA;sBACAhB;sBACAiB;sBAAA;sBACAC;sBACAC;sBACAC,YACA,4IACA;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBAAA;kBACA5C;kBAEA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;kBACAA;;kBAEA;kBACA;gBACA;kBACA;kBACAA;kBACA6C;oBACAZ;oBACAa;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9C;gBACA6C;kBACAZ;kBACAa;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA/C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAgD;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAA;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACAA;gBACAA;cAAA;gBAGA;gBACAC;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEApD;gBACA6C;kBACAZ;kBACAa;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAO;MACAR;QACAS;MACA;IACA;IAEAC;MACA;QACAV;UACAZ;UACAa;QACA;QACA;MACA;MAEA9C,YACA,iBACAmD,aACA,QACAA,cACA;MACAN;QACAS;MACA;IACA;IAEAE;MACAX;QACAS;MACA;IACA;IAEAG;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;EAAA,6EAGAC;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA;EACA,oFAEA;IAAA;MAAA;IACA,uBACA;MAAA;IAAA,yFACA;IACA,qCACA,+BACAC;EACA,oFAEA;IACA,qCACA,eACA;EACA,oFAGA;IACA3D;IACA;IACA;IACA;IACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AChZA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyHlwB;AAMA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAlB;EACAC;IACA6E;EACA;EACAtE;IACA;MACAuE;MACAC;MACAC;MACAC;IACA;EACA;EACApE;IACA;EACA;EAEAC;IACA;IACA;MACAG;MACA;IACA;EACA;EAEA;EACAkB;IACA;MACAlB;MACA;IACA;EACA;EACAF;IACAmE;MACA;MACA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAlE;gBAAA;gBAAA,OACA;kBAAAmE;gBAAA;cAAA;gBAAA9C;gBACArB;gBAEA;kBACAA;kBACAqB;oBACArB;kBACA;kBAEA;oBAAA;sBACAe;sBACAjC;sBACAsF,OACAC,iBACA;sBACAC;sBACAC;oBACA;kBAAA;kBACAvE;gBACA;kBACAA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwE;MACA,QACA;QACAzD;QACAjC;QACAsF;QACAE;MACA,GACA;QACAvD;QACAjC;QACAsF;QACAE;MACA,GACA;QACAvD;QACAjC;QACAsF;QACAE;MACA,GACA;QACAvD;QACAjC;QACAsF;QACAE;MACA,EACA;IACA;IAEAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAzE;gBAAA;gBAAA,OACA;kBAAAmE;gBAAA;cAAA;gBAAA9C;gBACArB;gBAEA;kBACA;oBAAA;sBACAe;sBACA2D;sBACAC;sBACAJ;sBACAK;sBAAA;sBACAC;sBAAA;sBACAP;oBACA;kBAAA;kBACAtE;;kBAEA;kBACA;gBACA;kBACAA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8E;MACA,QACA;QACA/D;QACA2D;QACAC;QACAJ;QACAK;QACAC;QACAP;MACA,EACA;IACA;IAEAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA/E;gBAAA;gBAAA,OACA;kBAAAmE;kBAAAa;gBAAA;cAAA;gBAAA3D;gBACArB;gBAEA;kBACA+B,qCACAV,cACAA;kBACA;oBAAA;sBACAN;sBACAmB;sBACAC;sBACAF;sBAAA;sBACAG;sBAAA;sBACAC;sBACAI;sBACAC;sBACAE;oBACA;kBAAA;kBACA5C;gBACA;kBACAA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiF;MACA;QACA;MACA;MACA,OACA,iCACAN,SACA;IAEA;IAEA;IACAO;MACA;MACA;IACA;IAEA;IACAC;MACA,QACA;QACApE;QACAmB;QACAC;QACAC;QACAC;QACAI;QACAC;MACA,GACA;QACA3B;QACAmB;QACAC;QACAC;QACAC;QACAI;QACAC;MACA,EACA;IACA;IAEA0C;MACA;MACA,wBACA;QACArE;QACAkB;QACAoD;QACAjB;MACA,GACA;QACArD;QACAkB;QACAoD;QACAjB;MACA,EACA;IACA;IAEAkB;MACAtF;MACA;IACA;EAAA,iFAEAV;IACAU;IACA;IACA;MAAA;IAAA;IACA;MACAuF;MACA;MACA;QACAA;MACA;IACA;EACA,sFAEAjG;IACAU;IACA;IACA;MAAA;IAAA;IACA;MACAuF;MACA;MACA;QACAA;MACA;IACA;EACA,sFAEAjG;IACA;IACA;MAAA;IAAA;IACA;MACAiG;IACA;IACAvF;EACA,4EAEA;IACA6C;MACAS;IACA;EACA,wEAEAkC;IACA3C;MACAS;IACA;EACA,kFAEA;IACAT;MACAS;IACA;EACA,oFAGA;IACAtD;IACA;IACA;IACA;IACA;IACA;EACA,oFAEAuF;IACA1C;MACAS;IACA;EACA,kFAEAH;IACAN;MACAS;IACA;EACA,0FAEAmC;IACA5C;MACAS;IACA;EACA,wGAGA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,MACA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAEAoC;cAAA,IACAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAGAC,gCACAC;gBAAA;cAAA,GACAC;gBAAA;cAAA;cAAA,MACAF;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAAA,OAEA;YAAA;cAAAtE;cACArB;cAEA;gBACA;gBACA;kBACA;oBACAuF;kBACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAvF;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,oFAGA;IACAA;IACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AChfA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoKlwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAQA;EACAlB;EACAQ;IACA;MACAwG;QACAnB;QACAD;MACA;MACAqB;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEAC;MACAC;MAEAC;MACAC,YACA;QAAA3F;QAAAjC;QAAAwF;MAAA,GACA;QAAAvD;QAAAjC;QAAAwF;MAAA,GACA;QAAAvD;QAAAjC;QAAAwF;MAAA,GACA;QAAAvD;QAAAjC;QAAAwF;MAAA,GACA;QAAAvD;QAAAjC;QAAAwF;MAAA,GACA;QAAAvD;QAAAjC;QAAAwF;MAAA,GACA;QAAAvD;QAAAjC;QAAAwF;MAAA,GACA;QAAAvD;QAAAjC;QAAAwF;MAAA,GACA;QAAAvD;QAAAjC;QAAAwF;MAAA,GACA;QAAAvD;QAAAjC;QAAAwF;MAAA,EACA;MAEAqC,kBACA;QACA5F;QACAjC;QACA8H;MACA,GACA;QACA7F;QACAjC;QACA8H;MACA,GACA;QACA7F;QACAjC;QACA8H;MACA;IAEA;EACA;EACAC;IACAC;MACA,OACA,oCACA,sCACA;IAEA;IAEAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QAAA,OACA3B;MAAA,EACA;IACA;EACA;EAEA5F;IACAI;IACAA;;IAEA;IACA;MACA;IACA;;IAEA;IACA6C;MACAZ;MACAa;MACAsE;IACA;IAEA;IACA;IACA;;IAEA;IACA;MACA;IACA;EACA;EAEAC;IACArH;;IAEA;IACA;MACAA;MACA;MACA;MACA;IACA;EACA;EACAF;IACA;IACAwH;MACA;MACA;MAEA;QACAtH;QACA6C;UACAZ;UACAa;UACAsE;QACA;QACAG;UACA1E;YACAS;UACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAkE;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAxH;gBAAA;gBAAA;gBAAA,OAEA6C;kBACAS;kBACAmE;kBACAC;gBACA;cAAA;gBAJAC;gBAKA3H;gBAEA;kBACAA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA6C;kBACAZ;kBACAa;kBACAsE;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA5H;gBAAA;gBAEA;gBACAgC;gBACAhC;gBAAA;gBAAA,OAEA;cAAA;gBAAAqB;gBAEArB;gBAEA;kBACA;oBACA2E,QACA,iCACAtD,qBACA;oBACAqD;kBACA;gBACA;kBACA1E;kBACA;oBACA2E;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1E;gBACAA;gBAEA6C;kBACAZ;kBACAa;kBACAsE;gBACA;gBAEA;kBACAzC;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAR;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAlE;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA6H;gBACA7H;gBAEA,IACA6H,WACAA,sBACAA,gBACAA,yBACA;kBACA;oBAAA;sBACA9G;sBACAjC;sBACAwF;oBACA;kBAAA;kBACAtE;gBACA;kBACA;kBACA;oBAAA;sBACAe;sBACAjC;sBACAwF;oBACA;kBAAA;kBACAtE;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAA;gBAEA6C;kBACAZ;kBACAa;kBACAsE;gBACA;;gBAEA;gBACA,oBACA;kBAAArG;kBAAAjC;kBAAAwF;gBAAA,GACA;kBAAAvD;kBAAAjC;kBAAAwF;gBAAA,GACA;kBAAAvD;kBAAAjC;kBAAAwF;gBAAA,GACA;kBAAAvD;kBAAAjC;kBAAAwF;gBAAA,GACA;kBAAAvD;kBAAAjC;kBAAAwF;gBAAA,EACA;gBACAtE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA8H;MACA;QACAjF;UACAZ;UACAG;UACA2F;YACA;cACAlF;YACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEAmF;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAEApF;kBACAqF;kBACAC;kBACAC;kBACAL;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAM;8BACArI;;8BAEA;8BACA6C;gCACAZ;gCACAqG;8BACA;8BAAA;8BAGA;8BACAtI;8BAAA;8BAAA,OAEA;4BAAA;8BAAAqB;8BACArB;;8BAEA;8BACA6C;8BAEA;gCACA;gCAAA,eACAxB,iFAEA;gCACA;;gCAEA;gCACA;gCAEArB;kCACAsC;kCACAD;gCACA;gCAEA;8BACA;gCACArC;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEA6C;8BACA7C;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;kBACAuI;oBACAvI;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAwI;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAzI;gBAAA;gBAGA;gBACA0I;gBACAzC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAjG;;gBAEA;gBACA6C;kBACAZ;kBACAqG;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAjH;gBACArB;;gBAEA;gBACA6C;gBAEA;kBACA;kBAAA,gBACAxB,mFAEA;kBACA;;kBAEA;kBACA;kBAEArB;oBACAsC;oBACAD;kBACA;kBAEA;gBACA;kBACArC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA6C;gBAEA7C;gBACA;cAAA;gBAGA;gBACA0I;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA1I;gBACA6C;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAY;MACA;IACA;IAEAkF;MACA;MACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MAAA;MACA7I;;MAEA;MACA6C;QACAkF;UACA/H;;UAEA;UACA;YACAlB;YACA8H;YACAkC;YACAC;UACA;UAEAlG;YACAZ;YACAa;YACAsE;UACA;QACA;QACAmB;UACAvI;UAEA;YACA;YACA;UACA;UAEA6C;YACAZ;YACAa;YACAsE;UACA;;UAEA;UACA;QACA;MACA;IACA;IAEA4B;MACA;MACA;IACA;IAEAC;MACA;MACApG;QACAZ;QACAa;QACAsE;MACA;IACA;IAEA8B;MAAA;MACArG;QACAsG;QACApB;UACA;UACA;QACA;MACA;IACA;IAEAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACApH;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAa;kBACAZ;kBACAa;kBACAsE;gBACA;gBACAG;kBACA1E;oBACAS;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAGA;gBACA+F;kBACArH;kBAAA;kBACAC;kBAAA;kBACAG;kBACAE;kBACAD;kBAAA;kBACAiH;oBAAA;kBAAA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA5J;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAqB;gBAEArB;gBAEA;kBACA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACltBA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACwGlwB;AAIA;AAAA;AAAA,eAEA;EACAlB;EACAQ;IACA;MACAuK;MACArJ;MACAC;MACAqJ;MACAC;MACAC;IACA;EACA;EACApK;IACA;IACA;EACA;EAEAC;IACA;IACA;MACAG;MACA;MACA;IACA;EACA;EAEA;EACAkB;IACA;MACAlB;MACA;MACA;IACA;EACA;EACAF;IACA;IACAmK;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAjK;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAkK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAlK;gBACA;gBAAA;gBAGA;gBACA0F;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA1F;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;kBACA0B;kBACAC;gBACA;cAAA;gBAHAN;gBAKArB;;gBAEA;gBACAmK;gBACA,IACA9I,UACAA,qBACAA,eACA+I,4BACA;kBACAD;gBACA;kBACAA;gBACA;kBACAnK;kBACAmK;gBACA;gBAEA;kBACA;oBAAA;sBACApJ;sBACAiB;sBACAlD,MACAuL,kCACAA,yBACA;sBACA1F,+BACA0F,oDACA;sBACAC;sBACAC,gDACA,yCACA;sBACAC,6CACAH,kCACA;sBACAI;sBACAC;sBACAC;oBACA;kBAAA;kBACA3K;kBACAA;gBACA;kBACAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA6C;kBACAZ;kBACAa;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmC;MACA;QACA;MACA;MAEA;QACA;MACA;MAEA;IACA;IAEA;IACA2F;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA3H;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MAEA;MACA;IACA;IAEAF;MACA;MACA;IACA;IAEA8H;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA7K;gBAAA;gBAAA,MAIA8K;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAC;gBACA;cAAA;gBAEA;gBACAF;;gBAEA;gBACA1H;kBAAA;gBAAA;gBACA;kBACA;gBACA;cAAA;gBAGA;gBACAP;kBACAS,mDACAwH,kCACAG,mBACAH,UACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9K;gBACA;gBACA6C;kBACAS,mDACAwH,kCACAG,mBACAH,UACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAI;MAAA;MACA,eACA,MACAJ,gCACA,OACA;MAEAjI;QACAsG;QACApB;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEAoD;MACA;MACA;IACA;IAEAC;MACAN;MACA;IACA;IAEAO;MAAA;MACAxI;QACAZ;QACAG;QACA2F;UACA;YACA;cAAA;YAAA;YACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAvE;MACAX;QACAS;MACA;IACA;IAEAgI;MACAzI;QACAS;MACA;IACA;IAEAiI;MACA1I;QACAS;MACA;IACA;IAEAkI;MACA3I;QACAS;MACA;IACA;IAEAmI;MACA5I;QACAS;MACA;IACA;IAEA;IACAoI;MACA1L;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClaA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACuIlwB;AASA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAlB;EACAC;IACAuB;IACAsD;EACA;EACAtE;IACA;MACAwG;QACA9D;QACA0C;QACAC;QACAgH;QACAC;QACAtH;QACAuH;QAAA;QACAC;QACAjH;QACApC;QACAsJ;MACA;MACAvL;MACAjB;MACA0B;MACA+K,OACA;QAAAlN;QAAAQ;QAAAkB;MAAA,GACA;QAAA1B;QAAAQ;QAAAkB;MAAA,GACA;QAAA1B;QAAAQ;QAAAkB;MAAA,GACA;QAAA1B;QAAAQ;QAAAkB;MAAA,GACA;QAAA1B;QAAAQ;QAAAkB;MAAA;IAEA;EACA;EACAZ;IAAA;IACAI;IACA;IACA;MACAuH;QACA;MACA;IACA;EACA;EACA1H;IACAG;IACA;IACA;MACA;IACA;EACA;EACAF;IACA;IACAqB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAnB;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA4H;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAlC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA1F;gBACA6C;kBACAZ;kBACAa;gBACA;gBAAA;cAAA;gBAIA9C;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAqB;gBACArB;gBAAA,MAEAqB;kBAAA;kBAAA;gBAAA;gBACA4K;gBACA;kBACAjK;kBACA0C;kBACAC;kBACAgH,KACAM,mBACAA,2BACA;kBACAL;kBACAtH;kBACAuH;kBAAA;kBACAC;kBACAjH,gBACAoH;kBACAxJ,WACAwJ;kBACAF;gBACA;gBAEA/L;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAEAA;gBACA6C;kBACAZ;kBACAa;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA9C;gBACA6C;kBACAZ;kBACAa;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoJ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAxK;kBACAC;kBAAA;kBACAK;kBACA2H;gBACA;cAAA;gBALAtI;gBAOA;kBACA;kBACA;oBACA;kBACA;oBACA;oBACA;oBACA;kBACA;gBACA;gBAEArB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmM;MACA;MACA;IACA;IAEA;IACAlH;MACA;QACA,OACA,iCACAN,SACA;MAEA;MACA;IACA;IAEAyH;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGA9M;gBAAA,eAEA+M;gBAAA,kCACA,yBAGA,0BAGA,0BAGA,0BAGA;gBAAA;cAAA;gBAAA;gBAAA,OAXA;cAAA;gBAAA/M;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAU;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA5G;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA1F;gBAAA,kCACA;cAAA;gBAAA;gBAAA,OAGA;kBACA0B;kBACAC;kBACAK;kBACA2H;kBAAA;kBACA/H;kBACAC;gBACA;cAAA;gBAPAR;gBASArB;gBAAA,MAEAqB;kBAAA;kBAAA;gBAAA;gBACAU,qCACAV,cACAA;gBAAA,kCACAU;kBAAA;oBACAhB;oBACAkB;oBACAI;oBACAH;oBACAC,YACA,iCACAgB,cACA;oBACAf;oBACAK;oBACAC;oBACAC;oBACAgH;oBAAA;oBACAC;oBAAA;oBACAhH;kBACA;gBAAA;cAAA;gBAEA5C;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA7G;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA1F;gBAAA,kCACA;cAAA;gBAAA;gBAAA,OAGA;kBACA0B;kBACAC;kBACAK;kBACA2H;kBAAA;kBACA/H;kBACAC;gBACA;cAAA;gBAPAR;gBASArB;gBAAA,MAEAqB;kBAAA;kBAAA;gBAAA;gBACAU,qCACAV,cACAA;gBAAA,kCACAU;kBAAA;oBACAhB;oBACAkB;oBACAI;oBACAH;oBACAC;oBACAC;oBACAK;oBACAC;oBACAC;oBACAgH;oBAAA;oBACAC;oBACAhH;kBACA;gBAAA;cAAA;gBAEA5C;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA9G;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA1F;gBAAA,kCACA;cAAA;gBAGA;gBACAA;gBACA;gBAAA,kCACA;cAAA;gBAAA;gBAAA;gBAEAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA/G;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA1F;gBAAA,kCACA;cAAA;gBAGAA;gBAAA;gBAAA,OACA;kBACA0B;kBACAC;gBACA;cAAA;gBAHAN;gBAIArB;gBAAA,MAEAqB;kBAAA;kBAAA;gBAAA;gBACAqL,qCACArL,cACAA;gBAAA,kCACAqL;kBAAA;oBACA3L;oBACAiB;oBAAA;oBACA0C;oBACAC;oBACAgH;oBACAC;oBACA/G;oBACAD;oBAAA;oBACA+H;kBACA;gBAAA;cAAA;gBAEA3M;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4M;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAlH;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA1F;gBAAA,kCACA;cAAA;gBAGAA;gBAAA;gBAAA,OACA;kBACA0B;kBACAC;gBACA;cAAA;gBAHAN;gBAIArB;gBAAA,MAEAqB;kBAAA;kBAAA;gBAAA;gBACAqL,qCACArL,cACAA;gBAAA,kCACAqL;kBAAA;oBACA3L;oBACAiB;oBAAA;oBACA0C;oBACAC;oBACAgH;oBACAC;oBACA/G;oBACAD;oBAAA;oBACA+H;kBACA;gBAAA;cAAA;gBAEA3M;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA6M;MACA;MACA;MACA;MACA;IACA;IAEAC;MACAjK;QACAkF;UACA/H;QACA;MACA;IACA;IAEA+M;MACAlK;QACAS;MACA;IACA;IAEA0J;MAAA;MACAnK;QACAqF;QACAC;QACAC;QACAL;UACA;UACA;QACA;MACA;IACA;IAEAkF;MACApK;QACAS;MACA;IACA;IAEA4J;MACArK;QACAS;MACA;IACA;IAEA6J;MACAtK;QACAS;MACA;IACA;IAEA;IACAD;MACAR;QACAS;MACA;IACA;IAEAC;MACA;MACA;QACA;QACA;QACA;UACAV;YACAS;UACA;QACA;MACA;QACA;QACA;QACA;QAEAT;UACAS;QACA;MACA;IACA;IAEAJ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAA;gBACAA;gBACAN;kBACAZ;kBACAa;kBACAsE;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACAjE;gBACAA;gBACAN;kBACAZ;kBACAa;kBACAsE;gBACA;cAAA;gBAGA;gBACAgG;gBACAhK;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEApD;gBACA6C;kBACAZ;kBACAa;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4I;MACA1L;MACA;MACA;MACA;;MAEA;MACA;QACAgC;QACA0C;QACAC;QACAgH;QACAC;QACAtH;QACAwH;QACAjH;QACApC;QACAsJ;MACA;;MAEA;MACA;QACAsB;QACAA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA,gBACA,QACA,UACA,QACA,QACA,OACA;MACA;IACA;IAEA;IACAhI;MACAtF;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACAuN;MACAvN;MACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAwN;MACAxN;MACA;MACA;IACA;IAEA;IACAyN;MACA;MACA;QACA;UAAA;QAAA;QACA;UACAC;QACA;MACA;;MAEA;MACA;QACA;UAAA;QAAA;QACA;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3vBA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/main/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/main/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0c4b5bf9&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0c4b5bf9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c4b5bf9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/main/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0c4b5bf9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"social-main-container\">\n    <!-- 内容区域 -->\n    <view class=\"content-area\">\n      <!-- 首页内容 -->\n      <HomePage v-if=\"currentTab === 0\" ref=\"homePage\" :key=\"homePageKey\" />\n\n      <!-- 发现页面内容 -->\n      <DiscoverPage v-if=\"currentTab === 1\" ref=\"discoverPage\" :key=\"discoverPageKey\" />\n\n      <PublishPage v-if=\"currentTab === 2\" />\n\n      <!-- 消息页面内容 -->\n      <MessagePage v-if=\"currentTab === 3\" ref=\"messagePage\" :key=\"messagePageKey\" />\n\n      <!-- 我的页面内容 -->\n      <ProfilePage v-if=\"currentTab === 4\" ref=\"profilePage\" :key=\"profilePageKey\" />\n    </view>\n\n    <!-- 底部导航 -->\n    <TabBar :currentTab=\"currentTab\" @tab-change=\"handleTabChange\" />\n  </view>\n</template>\n\n<script>\nimport TabBar from '../components/TabBar.vue'\nimport HomePage from '../home/<USER>'\nimport DiscoverPage from '../discover/index.vue'\nimport PublishPage from '../publish/index.vue'\nimport MessagePage from '../message/index.vue'\nimport ProfilePage from '../profile/index.vue'\n\nexport default {\n  name: 'SocialMain',\n  components: {\n    TabBar,\n    HomePage,\n    DiscoverPage,\n    PublishPage,\n    MessagePage,\n    ProfilePage\n  },\n  data() {\n    return {\n      currentTab: 0, // 默认显示首页\n      homePageKey: 0, // 用于强制刷新HomePage组件\n      discoverPageKey: 0, // 用于强制刷新DiscoverPage组件\n      messagePageKey: 0, // 用于强制刷新MessagePage组件\n      profilePageKey: 0 // 用于强制刷新ProfilePage组件\n    }\n  },\n  onLoad(options) {\n    // 根据传入的参数设置当前选项卡\n    if (options.tab) {\n      this.currentTab = parseInt(options.tab)\n    }\n  },\n\n  onShow() {\n    // 页面显示时，刷新当前tab的数据\n    this.refreshCurrentTab()\n  },\n  methods: {\n    // 处理 TabBar 切换事件\n    handleTabChange(data) {\n      const { index } = data\n      const previousTab = this.currentTab\n      this.currentTab = index\n\n      // 切换tab时刷新对应页面数据\n      if (previousTab !== index) {\n        this.refreshCurrentTab()\n      }\n\n      console.log('切换到选项卡:', index)\n    },\n\n    // 刷新当前tab的数据\n    refreshCurrentTab() {\n      this.$nextTick(() => {\n        switch (this.currentTab) {\n          case 0: // 首页\n            this.refreshHomePage()\n            break\n          case 1: // 发现页\n            this.refreshDiscoverPage()\n            break\n          case 3: // 消息页\n            this.refreshMessagePage()\n            break\n          case 4: // 我的页面\n            this.refreshProfilePage()\n            break\n          default:\n            console.log('当前tab无需刷新:', this.currentTab)\n        }\n      })\n    },\n\n    // 刷新首页数据\n    refreshHomePage() {\n      if (this.$refs.homePage) {\n        if (this.$refs.homePage.forceRefresh) {\n          this.$refs.homePage.forceRefresh()\n        } else if (this.$refs.homePage.onRefresh) {\n          this.$refs.homePage.onRefresh()\n        } else {\n          this.homePageKey += 1\n        }\n      } else {\n        this.homePageKey += 1\n      }\n    },\n\n    // 刷新发现页数据\n    refreshDiscoverPage() {\n      if (this.$refs.discoverPage) {\n        if (this.$refs.discoverPage.forceRefresh) {\n          this.$refs.discoverPage.forceRefresh()\n        } else if (this.$refs.discoverPage.loadDiscoverData) {\n          this.$refs.discoverPage.loadDiscoverData()\n        } else {\n          this.discoverPageKey += 1\n        }\n      } else {\n        this.discoverPageKey += 1\n      }\n    },\n\n    // 刷新消息页数据\n    refreshMessagePage() {\n      if (this.$refs.messagePage) {\n        if (this.$refs.messagePage.forceRefresh) {\n          this.$refs.messagePage.forceRefresh()\n        } else if (this.$refs.messagePage.loadChatList) {\n          this.$refs.messagePage.loadUnreadCounts()\n          this.$refs.messagePage.loadChatList()\n        } else {\n          this.messagePageKey += 1\n        }\n      } else {\n        this.messagePageKey += 1\n      }\n    },\n\n    // 刷新我的页面数据\n    refreshProfilePage() {\n      console.log('刷新我的页面数据...')\n      if (this.$refs.profilePage) {\n        // 优先调用页面的forceRefresh方法\n        if (this.$refs.profilePage.forceRefresh) {\n          this.$refs.profilePage.forceRefresh()\n        } else if (this.$refs.profilePage.loadUserInfo) {\n          // 如果没有forceRefresh方法，调用loadUserInfo方法\n          this.$refs.profilePage.loadUserInfo()\n        } else if (this.$refs.profilePage.initializeData) {\n          // 如果没有loadUserInfo方法，调用initializeData方法\n          this.$refs.profilePage.initializeData()\n        } else {\n          // 如果都没有，通过改变key强制重新渲染组件\n          this.profilePageKey += 1\n        }\n      } else {\n        // 如果ref不存在，通过改变key强制重新渲染组件\n        this.profilePageKey += 1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.social-main-container {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.content-area {\n  flex: 1;\n  overflow: hidden;\n}\n</style>\n", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=de45d8c2&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"de45d8c2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/home/<USER>\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=de45d8c2&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.postList.length && !_vm.loading\n  var m0 = g0 ? _vm.getEmptyText() : null\n  var m1 = g0 ? _vm.getEmptyDesc() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"home-container\" :style=\"{ height: '100vh', overflow: 'hidden' }\">\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"logo\">\n          <image src=\"https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\" class=\"logo-text\"></image>\n        </view>\n        <view class=\"header-actions\">\n          <u-icon name=\"search\" size=\"24\" color=\"#666\" @click=\"goSearch\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 话题标签栏 -->\n    <view class=\"topic-tabs-container\">\n      <u-tabs\n        :list=\"topicList\"\n        :current=\"currentTopicIndex\"\n        @change=\"selectTopic\"\n        :scrollable=\"true\"\n        activeColor=\"#2979ff\"\n        inactiveColor=\"#666\"\n        fontSize=\"28\"\n        lineColor=\"#2979ff\"\n        lineWidth=\"40\"\n        lineHeight=\"6\"\n        height=\"80\"\n        itemStyle=\"padding: 0 32rpx;\"\n      ></u-tabs>\n    </view>\n\n    <!-- 帖子网格列表 -->\n    <scroll-view\n      class=\"post-list\"\n      scroll-y\n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view class=\"post-grid\">\n        <PostCard\n          v-for=\"post in postList\"\n          :key=\"post.id\"\n          :post=\"post\"\n          class=\"post-card-item\"\n          @click=\"goPostDetail\"\n          @user-click=\"goUserProfile\"\n          @like=\"onPostLike\"\n        />\n      </view>\n\n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"loading\">\n        <u-loading mode=\"circle\" size=\"24\"></u-loading>\n        <text class=\"load-text\">加载中...</text>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"!postList.length && !loading\" class=\"empty-state\">\n        <u-icon name=\"file-text\" color=\"#ccc\" size=\"120rpx\"></u-icon>\n        <text class=\"empty-text\">{{ getEmptyText() }}</text>\n        <text class=\"empty-desc\">{{ getEmptyDesc() }}</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport PostCard from \"../components/PostCard.vue\";\nimport {\n  getPostList,\n  getHotTags,\n  likePost,\n  unlikePost\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialHome\",\n  components: {\n    PostCard\n  },\n  data() {\n    return {\n      postList: [],\n      loading: false,\n      refreshing: false,\n      page: 1,\n      pageSize: 10,\n      currentTopic: \"all\",\n      currentTopicIndex: 0,\n      topicList: [\n        { name: \"全部\", id: \"all\" },\n        { name: \"街舞\", id: \"street-dance\" },\n        { name: \"现代舞\", id: \"modern-dance\" },\n        { name: \"芭蕾\", id: \"ballet\" },\n        { name: \"拉丁舞\", id: \"latin-dance\" },\n        { name: \"爵士舞\", id: \"jazz-dance\" },\n        { name: \"民族舞\", id: \"folk-dance\" },\n        { name: \"古典舞\", id: \"classical-dance\" },\n        { name: \"舞蹈教学\", id: \"dance-teaching\" },\n        { name: \"舞蹈比赛\", id: \"dance-competition\" }\n      ],\n      hasMore: true,\n      isInitialized: false // 标记是否已初始化\n    };\n  },\n  onLoad() {\n    this.initializeData();\n  },\n\n  // 页面显示时重新加载数据\n  onShow() {\n    // 如果还没有初始化，或者数据为空，重新加载\n    if (!this.isInitialized || !this.postList || this.postList.length === 0) {\n      this.initializeData();\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    // 如果还没有初始化，或者数据为空，重新加载\n    if (!this.isInitialized || !this.postList || this.postList.length === 0) {\n      this.initializeData();\n    }\n  },\n  methods: {\n    // 初始化数据\n    async initializeData() {\n      console.log(\"初始化首页数据...\");\n      try {\n        await this.loadHotTags();\n        await this.loadPosts(true);\n        this.isInitialized = true;\n      } catch (error) {\n        console.error(\"初始化数据失败:\", error);\n      }\n    },\n\n    // 加载热门话题\n    async loadHotTags() {\n      try {\n        const result = await getHotTags(10);\n        console.log(\"热门标签API返回:\", result);\n\n        if (\n          result &&\n          result.code === 0 &&\n          result.data &&\n          result.data.length > 0\n        ) {\n          // 保留\"全部\"选项，添加热门话题\n          const allOption = this.topicList[0];\n          this.topicList = [\n            allOption,\n            ...result.data.map(tag => ({\n              name: tag.name,\n              id: tag.id\n            }))\n          ];\n          console.log(\"话题列表更新:\", this.topicList);\n        }\n      } catch (error) {\n        console.error(\"加载热门话题失败:\", error);\n        // 使用默认话题列表\n      }\n    },\n\n    async loadPosts(refresh = false) {\n      if (this.loading) {\n        console.log(\"正在加载中，跳过重复请求\");\n        return;\n      }\n\n      console.log(\"开始加载帖子数据，refresh:\", refresh);\n      this.loading = true;\n      try {\n        const params = {\n          current: refresh ? 1 : this.page,\n          size: this.pageSize,\n          sortField: \"createTime\",\n          sortOrder: \"desc\"\n        };\n\n        // 如果选择了特定话题，添加标签筛选\n        if (this.currentTopic !== \"all\") {\n          // 根据tagId获取标签名称\n          const selectedTag = this.topicList.find(\n            topic => topic.id === this.currentTopic\n          );\n          if (selectedTag) {\n            params.tags = [selectedTag.name];\n          }\n        }\n\n        console.log(\"API请求参数:\", params);\n        const result = await getPostList(params);\n        console.log(\"API返回结果:\", result);\n\n        if (result.code == 0) {\n          const posts = result.data.records.map(post => ({\n            id: post.id,\n            userId: post.userId, // 添加用户ID字段\n            title: post.title || \"\",\n            username: post.nickname || \"无名氏\",\n            userAvatar:\n              \"https://file.foxdance.com.cn\" + post.avatar + '?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85'||\n              \"/static/images/toux.png\",\n            content: post.content,\n            coverImage: post.coverImage,\n            images: post.images || [],\n            topics: post.tags || [],\n            topicId: this.currentTopic,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            createTime: new Date(post.createTime)\n          }));\n          console.log(\"posts:\", posts);\n\n          if (refresh) {\n            this.postList = posts;\n            this.page = 2;\n          } else {\n            this.postList = [...this.postList, ...posts];\n            this.page++;\n          }\n          console.log(\"this.postList:\", this.postList);\n\n          // 检查是否还有更多数据\n          this.hasMore = result.data.records.length >= this.pageSize;\n        } else {\n          // API调用失败\n          console.warn(\"获取帖子列表失败:\", result);\n          uni.showToast({\n            title: result.message || \"获取数据失败\",\n            icon: \"none\"\n          });\n          this.hasMore = false;\n        }\n      } catch (error) {\n        console.error(\"加载帖子失败:\", error);\n        uni.showToast({\n          title: \"网络错误，请重试\",\n          icon: \"none\"\n        });\n        this.hasMore = false;\n      } finally {\n        this.loading = false;\n        this.refreshing = false;\n      }\n    },\n\n    onRefresh() {\n      console.log(\"刷新首页数据...\");\n      this.refreshing = true;\n      this.page = 1;\n      this.postList = [];\n      this.hasMore = true;\n      // 重新加载热门话题和帖子\n      this.loadHotTags();\n      this.loadPosts(true);\n    },\n\n    loadMore() {\n      if (!this.loading) {\n        this.page++;\n        this.loadPosts();\n      }\n    },\n\n    formatTime(time) {\n      const now = new Date();\n      const diff = now - new Date(time);\n      const minutes = Math.floor(diff / 60000);\n      const hours = Math.floor(diff / 3600000);\n      const days = Math.floor(diff / 86400000);\n\n      if (minutes < 60) return `${minutes}分钟前`;\n      if (hours < 24) return `${hours}小时前`;\n      return `${days}天前`;\n    },\n\n    async onPostLike(post) {\n      try {\n        if (post.isLiked) {\n          // 取消点赞\n          await unlikePost(post.id);\n          post.isLiked = false;\n          post.likeCount = Math.max(0, post.likeCount - 1);\n        } else {\n          // 点赞\n          await likePost(post.id);\n          post.isLiked = true;\n          post.likeCount += 1;\n        }\n\n        // 更新帖子列表中的数据\n        const index = this.postList.findIndex(p => p.id === post.id);\n        if (index !== -1) {\n          this.$set(this.postList, index, { ...post });\n        }\n      } catch (error) {\n        console.error(\"点赞操作失败:\", error);\n        uni.showToast({\n          title: \"操作失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      });\n    },\n\n    goUserProfile(post) {\n      if (!post.userId) {\n        uni.showToast({\n          title: \"用户信息错误\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      console.log(\n        \"跳转到用户主页，用户ID:\",\n        post.userId,\n        \"用户名:\",\n        post.username\n      );\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${post.userId}`\n      });\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/search/index\"\n      });\n    },\n\n    selectTopic(index) {\n      if (this.currentTopicIndex === index) return;\n\n      this.currentTopicIndex = index;\n      this.currentTopic = this.topicList[index].id;\n      this.page = 1;\n      this.postList = [];\n\n      // 重新加载帖子\n      this.loadPosts(true);\n    },\n\n    // 格式化时间\n    formatTime(dateString) {\n      if (!dateString) return \"\";\n\n      const date = new Date(dateString);\n      const now = new Date();\n      const diff = now - date;\n\n      const minutes = Math.floor(diff / (1000 * 60));\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n\n      if (minutes < 1) return \"刚刚\";\n      if (minutes < 60) return `${minutes}分钟前`;\n      if (hours < 24) return `${hours}小时前`;\n      if (days < 7) return `${days}天前`;\n\n      return date.toLocaleDateString();\n    },\n\n    getEmptyText() {\n      const currentTopicName =\n        this.topicList.find(topic => topic.id === this.currentTopic)?.name ||\n        \"全部\";\n      return this.currentTopic === \"all\"\n        ? \"暂无帖子\"\n        : `暂无${currentTopicName}相关帖子`;\n    },\n\n    getEmptyDesc() {\n      return this.currentTopic === \"all\"\n        ? \"快来发布第一条帖子吧\"\n        : \"换个话题看看其他内容吧\";\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log(\"强制刷新首页数据...\");\n      this.isInitialized = false;\n      this.postList = [];\n      this.page = 1;\n      this.hasMore = true;\n      this.initializeData();\n    }\n  }\n};\n</script>\n\n\n\n<style lang=\"scss\" scoped>\n.home-container {\n  height: 100vh;\n  background: #f8f9fa;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  position: relative;\n  margin: 0;\n  padding: 0;\n}\n\n.header {\n  background: #fff;\n  border-bottom: 2rpx solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n  flex-shrink: 0;\n}\n\n.header-content {\n  height: 88rpx;\n\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n}\n\n.logo-text {\n  width: 1328 * 0.11rpx;\n  height: 625 * 0.11rpx;\n  margin-left: -10rpx;\n}\n\n.topic-tabs-container {\n  background: #fff;\n  border-bottom: 2rpx solid #f0f0f0;\n  flex-shrink: 0;\n}\n\n/* uview tabs组件样式优化 */\n.topic-tabs-container ::v-deep .u-tabs {\n  background: #fff;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item {\n  padding: 0 32rpx !important;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item__text {\n  font-size: 28rpx !important;\n  font-weight: 500;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__line {\n  border-radius: 6rpx;\n}\n\n.post-list {\n  flex: 1;\n  height: 0; /* 重要：让flex子元素正确计算高度 */\n  overflow: hidden;\n}\n\n/* scroll-view内部内容的样式 */\n.post-list ::v-deep .uni-scroll-view {\n  height: 100% !important;\n  overflow-x: hidden !important;\n}\n\n.post-list ::v-deep .uni-scroll-view-content {\n  min-height: 100%;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 14rpx;\n  padding: 26rpx;\n  padding-bottom: 100rpx; /* 底部留出更多空间 */\n}\n\n.post-card-item {\n  width: calc(50% - 8rpx);\n  margin-bottom: 16rpx;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 160rpx 40rpx;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #999;\n  margin: 32rpx 0 16rpx;\n}\n\n.empty-desc {\n  font-size: 28rpx;\n  color: #ccc;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.load-text {\n  margin-left: 16rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 375px) {\n  .post-list {\n    padding: 12rpx;\n  }\n\n  .post-grid {\n    gap: 12rpx;\n  }\n\n  .post-card-item {\n    width: calc(50% - 6rpx);\n  }\n}\n\n@media screen and (min-width: 768px) {\n  .post-grid {\n    gap: 24rpx;\n  }\n\n  .post-card-item {\n    width: calc(33.33% - 16rpx);\n  }\n}\n\n@media screen and (min-width: 1024px) {\n  .post-list {\n    padding: 32rpx 64rpx;\n  }\n\n  .post-card-item {\n    width: calc(25% - 18rpx);\n  }\n}\n\n// 修改u-tab-itme默认样式\n/deep/ .u-tab-item {\n  padding: 0 25rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753622569378\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1fcbd0ae&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1fcbd0ae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/discover/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1fcbd0ae&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"discover-container\">\n    <!-- 顶部搜索 -->\n    <view class=\"header\">\n      <view class=\"search-bar\" @click=\"goSearch\">\n        <u-icon name=\"search\" size=\"18\" color=\"#999\"></u-icon>\n        <text class=\"search-placeholder\">搜索话题、用户...</text>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 热门话题 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门话题</text>\n          <text class=\"more-btn\" @click=\"goTopicList\">更多</text>\n        </view>\n        <view class=\"topic-grid\">\n          <view\n            v-for=\"topic in hotTopics\"\n            :key=\"topic.id\"\n            class=\"topic-card\"\n            @click=\"goTopic(topic)\"\n          >\n            <image :src=\"topic.cover\" class=\"topic-cover\" mode=\"aspectFill\" />\n            <view class=\"topic-info\">\n              <text class=\"topic-name\">#{{ topic.name }}</text>\n              <text class=\"topic-count\">{{ topic.postCount }}条帖子</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 推荐用户 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">推荐关注</text>\n        </view>\n        <scroll-view class=\"user-scroll\" scroll-x>\n          <view class=\"user-list\">\n            <view\n              v-for=\"user in recommendUsers\"\n              :key=\"user.id\"\n              class=\"user-card\"\n              @click=\"goUserProfile(user)\"\n            >\n              <u-avatar :src=\"user.avatar\" size=\"60\"></u-avatar>\n              <text class=\"user-name\">{{ user.nickname }}</text>\n              <text class=\"user-desc\">{{ user.description }}</text>\n              <FollowButton\n                :user=\"user\"\n                :followed=\"user.isFollowed\"\n                size=\"mini\"\n                @follow=\"onUserFollow\"\n                @change=\"onFollowChange\"\n                @click.stop\n              />\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 热门帖子 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门帖子</text>\n        </view>\n        <view class=\"hot-posts\">\n          <view\n            v-for=\"post in hotPosts\"\n            :key=\"post.id\"\n            class=\"hot-post-item\"\n            @click=\"goPostDetail(post)\"\n          >\n            <view class=\"post-content\">\n              <view class=\"user-info\">\n                <u-avatar :src=\"post.userAvatar\" size=\"32\"></u-avatar>\n                <text class=\"username\">{{ post.username }}</text>\n              </view>\n              <text class=\"post-text\">{{ post.title || post.content || '无标题' }}</text>\n              <view class=\"post-stats\">\n                <text class=\"stat-item\">{{ post.likeCount }}赞</text>\n                <text class=\"stat-item\">{{ post.commentCount }}评论</text>\n              </view>\n            </view>\n            <image\n              v-if=\"post.coverImage\"\n              :src=\"post.coverImage\"\n              class=\"post-cover\"\n              mode=\"aspectFill\"\n            />\n          </view>\n        </view>\n      </view>\n\n      <!-- 精选内容 -->\n      <!-- <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">精选内容</text>\n        </view>\n        <view class=\"featured-grid\">\n          <view\n            v-for=\"item in featuredContent\"\n            :key=\"item.id\"\n            class=\"featured-item\"\n            @click=\"goFeaturedDetail(item)\"\n          >\n            <image :src=\"item.cover\" class=\"featured-cover\" mode=\"aspectFill\" />\n            <view class=\"featured-overlay\">\n              <text class=\"featured-title\">{{ item.title }}</text>\n              <text class=\"featured-subtitle\">{{ item.subtitle }}</text>\n            </view>\n          </view>\n        </view>\n      </view>-->\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport FollowButton from \"../components/FollowButton.vue\";\nimport {\n  getHotTopics,\n  getHotPosts,\n  getRecommendUsers,\n  getFeaturedContent,\n  batchCheckFollowStatus\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialDiscover\",\n  components: {\n    FollowButton\n  },\n  data() {\n    return {\n      hotTopics: [],\n      recommendUsers: [],\n      hotPosts: [],\n      featuredContent: []\n    };\n  },\n  onLoad() {\n    this.loadDiscoverData();\n  },\n\n  onShow() {\n    // 页面显示时检查是否需要加载数据\n    if (!this.hotTopics || this.hotTopics.length === 0) {\n      console.log(\"发现页显示时重新加载数据\");\n      this.loadDiscoverData();\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    if (!this.hotTopics || this.hotTopics.length === 0) {\n      console.log(\"发现页激活时重新加载数据\");\n      this.loadDiscoverData();\n    }\n  },\n  methods: {\n    loadDiscoverData() {\n      this.loadHotTopics();\n      this.loadRecommendUsers();\n      this.loadHotPosts();\n      this.loadFeaturedContent();\n    },\n\n    async loadHotTopics() {\n      try {\n        console.log(\"开始加载热门话题...\");\n        const result = await getHotTopics({ limit: 4 });\n        console.log(\"热门话题API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          console.log(\"🔥 API返回的原始标签数据:\", result.data);\n          result.data.forEach(tag => {\n            console.log(`🔥 标签 ${tag.name} - coverImage: ${tag.coverImage}`);\n          });\n\n          this.hotTopics = result.data.map(tag => ({\n            id: tag.id,\n            name: tag.name,\n            cover:\n              tag.coverImage +\n              \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\",\n            postCount: tag.useCount || 0,\n            description: tag.description\n          }));\n          console.log(\"🔥 处理后的热门话题:\", this.hotTopics);\n        } else {\n          console.error(\"热门话题API返回格式不正确:\", result);\n          // 使用默认数据作为后备\n          this.hotTopics = this.getDefaultTopics();\n        }\n      } catch (error) {\n        console.error(\"加载热门话题失败:\", error);\n        // 使用默认数据作为后备\n        this.hotTopics = this.getDefaultTopics();\n      }\n    },\n\n    // 默认话题数据（后备方案）\n    getDefaultTopics() {\n      return [\n        {\n          id: 1,\n          name: \"街舞\",\n          cover: \"https://picsum.photos/200/120?random=1\",\n          postCount: 1234\n        },\n        {\n          id: 2,\n          name: \"爵士舞\",\n          cover: \"https://picsum.photos/200/120?random=2\",\n          postCount: 856\n        },\n        {\n          id: 3,\n          name: \"芭蕾\",\n          cover: \"https://picsum.photos/200/120?random=3\",\n          postCount: 642\n        },\n        {\n          id: 4,\n          name: \"现代舞\",\n          cover: \"https://picsum.photos/200/120?random=4\",\n          postCount: 789\n        }\n      ];\n    },\n\n    async loadRecommendUsers() {\n      try {\n        console.log(\"开始加载推荐用户...\");\n        const result = await getRecommendUsers({ limit: 10 });\n        console.log(\"推荐用户API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          this.recommendUsers = result.data.map(user => ({\n            id: user.userId || user.id,\n            nickname: user.nickname || \"用户\",\n            avatar: this.formatAvatarUrl(user.avatar),\n            description: user.bio || \"暂无简介\",\n            isFollowed: user.isFollowed || false, // 使用后端返回的关注状态\n            followersCount: user.followerCount || 0, // 修正字段名：followerCount\n            postCount: user.postCount || 0\n          }));\n          console.log(\"推荐用户加载成功:\", this.recommendUsers);\n\n          // 批量检查关注状态\n          this.batchCheckFollowStatus();\n        } else {\n          console.error(\"推荐用户API返回格式不正确:\", result);\n          // 使用默认数据作为后备\n          this.recommendUsers = this.getDefaultRecommendUsers();\n        }\n      } catch (error) {\n        console.error(\"加载推荐用户失败:\", error);\n        // 使用默认数据作为后备\n        this.recommendUsers = this.getDefaultRecommendUsers();\n      }\n    },\n\n    // 默认推荐用户数据（后备方案）\n    getDefaultRecommendUsers() {\n      return [\n        {\n          id: 18,\n          nickname: \"舞蹈达人\",\n          avatar: \"https://picsum.photos/100/100?random=18\",\n          description: \"分享舞蹈技巧和心得\",\n          isFollowed: false,\n          followersCount: 1024,\n          postCount: 128\n        }\n      ];\n    },\n\n    async loadHotPosts() {\n      try {\n        console.log(\"开始加载热门帖子...\");\n        const result = await getHotPosts({ limit: 4, timeRange: \"7d\" });\n        console.log(\"热门帖子API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const posts = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          this.hotPosts = posts.map(post => ({\n            id: post.id,\n            username: post.nickname || post.username || \"用户\",\n            userAvatar: this.formatAvatarUrl(post.avatar || post.userAvatar),\n            title: post.title || \"\", // 添加标题字段\n            content: this.truncateContent(post.title || post.content || \"\"), // 优先显示标题，没有标题则显示内容\n            coverImage: post.coverImage,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            createTime: post.createTime\n          }));\n          console.log(\"热门帖子加载成功:\", this.hotPosts);\n        } else {\n          console.error(\"热门帖子API返回格式不正确:\", result);\n          // 使用默认数据作为后备\n          this.hotPosts = this.getDefaultPosts();\n        }\n      } catch (error) {\n        console.error(\"加载热门帖子失败:\", error);\n        // 使用默认数据作为后备\n        this.hotPosts = this.getDefaultPosts();\n      }\n    },\n\n    // 格式化头像URL\n    formatAvatarUrl(avatar) {\n      if (!avatar) {\n        return \"/static/images/toux.png\";\n      }\n      return (\n        \"https://file.foxdance.com.cn\" +\n        avatar +\n        \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\"\n      );\n    },\n\n    // 截断内容\n    truncateContent(content) {\n      if (!content) return \"暂无内容\";\n      return content.length > 50 ? content.substring(0, 50) + \"...\" : content;\n    },\n\n    // 默认帖子数据（后备方案）\n    getDefaultPosts() {\n      return [\n        {\n          id: 1,\n          username: \"舞蹈达人\",\n          userAvatar: \"https://picsum.photos/100/100?random=20\",\n          content: \"今天的舞蹈练习分享，基础动作很重要...\",\n          coverImage: \"https://picsum.photos/120/120?random=30\",\n          likeCount: 234,\n          commentCount: 45\n        },\n        {\n          id: 2,\n          username: \"街舞教练\",\n          userAvatar: \"https://picsum.photos/100/100?random=21\",\n          content: \"新手入门街舞的几个要点，记得收藏！\",\n          coverImage: \"https://picsum.photos/120/120?random=31\",\n          likeCount: 189,\n          commentCount: 32\n        }\n      ];\n    },\n\n    loadFeaturedContent() {\n      // 模拟精选内容数据\n      this.featuredContent = [\n        {\n          id: 1,\n          title: \"春日穿搭指南\",\n          subtitle: \"时尚达人教你搭配\",\n          cover: \"https://picsum.photos/300/200?random=40\"\n        },\n        {\n          id: 2,\n          title: \"周末好去处\",\n          subtitle: \"城市探索攻略\",\n          cover: \"https://picsum.photos/300/200?random=41\"\n        }\n      ];\n    },\n\n    onUserFollow(data) {\n      console.log(\"关注操作:\", data);\n      // 这里可以调用API进行关注/取消关注操作\n    },\n\n    onUserFollow(data) {\n      console.log(\"用户关注成功:\", data);\n      // 更新本地数据\n      const user = this.recommendUsers.find(u => u.id === data.user.id);\n      if (user) {\n        user.isFollowed = true;\n        // 可以增加粉丝数\n        if (user.followersCount !== undefined) {\n          user.followersCount += 1;\n        }\n      }\n    },\n\n    onUserUnfollow(data) {\n      console.log(\"用户取消关注成功:\", data);\n      // 更新本地数据\n      const user = this.recommendUsers.find(u => u.id === data.user.id);\n      if (user) {\n        user.isFollowed = false;\n        // 可以减少粉丝数\n        if (user.followersCount !== undefined && user.followersCount > 0) {\n          user.followersCount -= 1;\n        }\n      }\n    },\n\n    onFollowChange(data) {\n      // 更新本地数据\n      const user = this.recommendUsers.find(u => u.id === data.user.id);\n      if (user) {\n        user.isFollowed = data.isFollowed;\n      }\n      console.log(\"关注状态变化:\", data);\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/search/index\"\n      });\n    },\n\n    goTopic(topic) {\n      uni.navigateTo({\n        url: `/pagesSub/social/topic/detail?id=${topic.id}`\n      });\n    },\n\n    goTopicList() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/topic/list\"\n      });\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log(\"强制刷新发现页数据...\");\n      this.hotTopics = [];\n      this.recommendUsers = [];\n      this.hotPosts = [];\n      this.featuredContent = [];\n      this.loadDiscoverData();\n    },\n\n    goUserProfile(user) {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${user.id}`\n      });\n    },\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      });\n    },\n\n    goFeaturedDetail(item) {\n      uni.navigateTo({\n        url: `/pagesSub/social/featured/detail?id=${item.id}`\n      });\n    },\n\n    // 批量检查关注状态\n    async batchCheckFollowStatus() {\n      if (!this.recommendUsers || this.recommendUsers.length === 0) return;\n\n      const currentUserId = uni.getStorageSync(\"userid\");\n      if (!currentUserId) return;\n\n      try {\n        const userIds = this.recommendUsers\n          .map(user => user.id)\n          .filter(id => id != currentUserId);\n        if (userIds.length === 0) return;\n\n        const result = await batchCheckFollowStatus(userIds);\n        console.log(\"批量检查关注状态结果:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          // 更新关注状态\n          this.recommendUsers.forEach(user => {\n            if (result.data[user.id] !== undefined) {\n              user.isFollowed = result.data[user.id];\n            }\n          });\n        }\n      } catch (error) {\n        console.error(\"批量检查关注状态失败:\", error);\n      }\n    },\n\n    // 强制刷新页面数据（供父组件调用）\n    forceRefresh() {\n      console.log(\"强制刷新发现页面数据...\");\n      // 重新加载所有数据\n      this.loadDiscoverData();\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.discover-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 200rpx;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  padding: var(--status-bar-height) 32rpx 24rpx;\n  border-bottom: 2rpx solid #e4e7ed;\n}\n\n.search-bar {\n  height: 72rpx;\n  background: #f5f5f5;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  padding: 0 32rpx;\n}\n\n.search-placeholder {\n  margin-left: 16rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.content {\n  margin-top: calc(125rpx + var(--status-bar-height));\n  padding: 0 32rpx;\n  width: auto;\n}\n\n.section {\n  margin-bottom: 48rpx;\n}\n\n.section-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 24rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.more-btn {\n  font-size: 28rpx;\n  color: #2979ff;\n}\n\n.topic-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 22rpx;\n}\n\n.topic-card {\n  width: calc(50% - 12rpx);\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.topic-cover {\n  width: 100%;\n  height: 160rpx;\n}\n\n.topic-info {\n  padding: 24rpx;\n}\n\n.topic-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  display: block;\n}\n\n.topic-count {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 8rpx;\n  display: block;\n}\n\n.user-scroll {\n  white-space: nowrap;\n}\n\n.user-list {\n  display: flex;\n  gap: 32rpx;\n  padding-bottom: 16rpx;\n}\n\n.user-card {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 32rpx 24rpx;\n  min-width: 200rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.user-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin: 16rpx 0 8rpx;\n  text-align: center;\n}\n\n.user-desc {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 24rpx;\n  text-align: center;\n}\n\n.hot-posts {\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.hot-post-item {\n  display: flex;\n  padding: 32rpx;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n.hot-post-item:last-child {\n  border-bottom: none;\n}\n\n.post-content {\n  flex: 1;\n  margin-right: 24rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.username {\n  margin-left: 16rpx;\n  font-size: 26rpx;\n  color: #666;\n}\n\n.post-text {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.4;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.post-stats {\n  display: flex;\n  gap: 32rpx;\n}\n\n.stat-item {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.post-cover {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 16rpx;\n}\n\n.featured-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.featured-item {\n  position: relative;\n  height: 240rpx;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.featured-cover {\n  width: 100%;\n  height: 100%;\n}\n\n.featured-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));\n  padding: 40rpx 32rpx 32rpx;\n}\n\n.featured-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #fff;\n  display: block;\n}\n\n.featured-subtitle {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 8rpx;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753622569531\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=bb7c3636&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bb7c3636\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/publish/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=bb7c3636&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-input/u-input\" */ \"@/components/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.postTitle.length\n  var g1 = _vm.postContent.length\n  var g2 = _vm.selectedImages.length\n  var g3 = _vm.selectedTopics.length\n  var g4 = g3\n    ? _vm.selectedTopics\n        .map(function (t) {\n          return \"#\" + t\n        })\n        .join(\" \")\n    : null\n  var l0 = _vm.__map(_vm.filteredTopics, function (topic, __i0__) {\n    var $orig = _vm.__get_orig(topic)\n    var g5 = _vm.selectedTopics.includes(topic.name)\n    return {\n      $orig: $orig,\n      g5: g5,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showTopicModal = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showLocationModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"publish-container\">\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 用户信息 -->\n      <view class=\"user-section\">\n        <u-avatar :src=\"userInfo.avatar\" size=\"40\"></u-avatar>\n        <text class=\"username\">{{ userInfo.nickname }}</text>\n      </view>\n\n      <!-- 标题输入 -->\n      <view class=\"title-section\">\n        <input v-model=\"postTitle\" class=\"title-input\" placeholder=\"标题（可选）\" :maxlength=\"50\" />\n        <view class=\"title-char-count\">{{ postTitle.length }}/50</view>\n      </view>\n\n      <!-- 文字输入 -->\n      <view class=\"text-section\">\n        <textarea\n          v-model=\"postContent\"\n          class=\"content-input\"\n          placeholder=\"分享你的生活...\"\n          :maxlength=\"500\"\n          auto-height\n          :show-confirm-bar=\"false\"\n        />\n        <view class=\"char-count\">{{ postContent.length }}/500</view>\n      </view>\n\n      <!-- 图片上传 -->\n      <view class=\"image-section\">\n        <view class=\"image-grid\">\n          <view v-for=\"(image, index) in selectedImages\" :key=\"index\" class=\"image-item\">\n            <image :src=\"image\" class=\"uploaded-image\" mode=\"aspectFill\" />\n            <view class=\"delete-btn\" @click=\"removeImage(index)\">\n              <u-icon name=\"close\" color=\"#fff\" size=\"16\"></u-icon>\n            </view>\n          </view>\n          <view v-if=\"selectedImages.length < 9\" class=\"add-image-btn\" @click=\"chooseImage\">\n            <u-icon name=\"camera\" color=\"#999\" size=\"32\"></u-icon>\n            <text class=\"add-text\">添加图片</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 功能选项 -->\n      <view class=\"options-section\">\n        <!-- 话题选择 -->\n        <view class=\"option-item\" @click=\"selectTopic\">\n          <view class=\"option-left\">\n            <u-icon name=\"tags\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">添加话题</text>\n          </view>\n          <view class=\"option-right\">\n            <text\n              v-if=\"selectedTopics.length\"\n              class=\"selected-topics\"\n            >{{ selectedTopics.map(t => '#' + t).join(' ') }}</text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n\n        <!-- 位置定位 -->\n        <view class=\"option-item\" @click=\"selectLocation\">\n          <view class=\"option-left\">\n            <u-icon name=\"map\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">添加位置</text>\n          </view>\n          <view class=\"option-right\">\n            <view v-if=\"selectedLocation\" class=\"location-selected\">\n              <view class=\"location-info-inline\">\n                <text class=\"selected-location\">{{ selectedLocation.name }}</text>\n                <text class=\"selected-address\">{{ selectedLocation.address }}</text>\n              </view>\n              <u-icon name=\"close-circle-fill\" color=\"#999\" size=\"18\" @click.stop=\"clearLocation\"></u-icon>\n            </view>\n            <u-icon v-else name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n\n        <!-- 可见性设置 -->\n        <view class=\"option-item\" @click=\"setVisibility\">\n          <view class=\"option-left\">\n            <u-icon name=\"eye\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">可见性</text>\n          </view>\n          <view class=\"option-right\">\n            <text class=\"visibility-text\">{{ visibilityText }}</text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 提醒文字 -->\n      <view class=\"tips-section\">\n        <text class=\"tips-text\">发布即表示同意《社区公约》，请文明发言，共建和谐社区</text>\n      </view>\n\n      <!-- 发布按钮 -->\n      <view class=\"publish-section\">\n        <text\n          class=\"publish-btn\"\n          :class=\"{ disabled: !canPublish }\"\n          @click=\"publishPost\"\n        >{{ publishing ? '发布中...' : '发布' }}</text>\n      </view>\n    </scroll-view>\n\n    <!-- 话题选择弹窗 -->\n    <u-popup v-model=\"showTopicModal\" mode=\"bottom\" border-radius=\"20\">\n      <view class=\"topic-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择话题</text>\n          <u-icon name=\"close\" @click=\"showTopicModal = false\"></u-icon>\n        </view>\n        <view class=\"topic-search\">\n          <u-input\n            v-model=\"topicKeyword\"\n            placeholder=\"搜索话题\"\n            prefix-icon=\"search\"\n            @input=\"searchTopics\"\n          />\n        </view>\n        <view class=\"topic-list\">\n          <view\n            v-for=\"topic in filteredTopics\"\n            :key=\"topic.id\"\n            class=\"topic-option\"\n            :class=\"{ selected: selectedTopics.includes(topic.name) }\"\n            @click=\"toggleTopic(topic)\"\n          >\n            <text class=\"topic-name\">#{{ topic.name }}</text>\n            <text class=\"topic-count\">{{ topic.postCount }}条帖子</text>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n\n    <!-- 位置选择弹窗 -->\n    <u-popup v-model=\"showLocationModal\" mode=\"bottom\" border-radius=\"20\">\n      <view class=\"location-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择位置</text>\n          <u-icon name=\"close\" @click=\"showLocationModal = false\"></u-icon>\n        </view>\n        <view class=\"location-list\">\n          <view\n            v-for=\"location in nearbyLocations\"\n            :key=\"location.id\"\n            class=\"location-option\"\n            @click=\"selectLocationItem(location)\"\n          >\n            <u-icon name=\"map-pin\" color=\"#2979ff\" size=\"16\"></u-icon>\n            <view class=\"location-info\">\n              <text class=\"location-name\">{{ location.name }}</text>\n              <text class=\"location-address\">{{ location.address }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport {\n  createPost,\n  getHotTags,\n  getUserProfile,\n  uploadPostImage,\n  uploadPostImages\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialPublish\",\n  data() {\n    return {\n      userInfo: {\n        avatar: \"/static/images/toux.png\",\n        nickname: \"加载中...\"\n      },\n      postTitle: \"\",\n      postContent: \"\",\n      selectedImages: [],\n      coverImageUrl: \"\", // 封面图片URL\n      selectedTopics: [],\n      selectedLocation: null,\n      visibility: \"public\", // public, friends, private\n      publishing: false, // 发布状态\n\n      showTopicModal: false,\n      showLocationModal: false,\n\n      topicKeyword: \"\",\n      allTopics: [\n        { id: 1, name: \"街舞\", postCount: 1234 },\n        { id: 2, name: \"现代舞\", postCount: 856 },\n        { id: 3, name: \"芭蕾\", postCount: 642 },\n        { id: 4, name: \"拉丁舞\", postCount: 789 },\n        { id: 5, name: \"爵士舞\", postCount: 456 },\n        { id: 6, name: \"民族舞\", postCount: 321 },\n        { id: 7, name: \"古典舞\", postCount: 298 },\n        { id: 8, name: \"舞蹈教学\", postCount: 567 },\n        { id: 9, name: \"舞蹈比赛\", postCount: 234 },\n        { id: 10, name: \"舞蹈培训\", postCount: 189 }\n      ],\n\n      nearbyLocations: [\n        {\n          id: 1,\n          name: \"星巴克咖啡\",\n          address: \"北京市朝阳区三里屯太古里\"\n        },\n        {\n          id: 2,\n          name: \"三里屯太古里\",\n          address: \"北京市朝阳区三里屯路19号\"\n        },\n        {\n          id: 3,\n          name: \"朝阳公园\",\n          address: \"北京市朝阳区朝阳公园南路1号\"\n        }\n      ]\n    };\n  },\n  computed: {\n    canPublish() {\n      return (\n        this.postTitle.trim().length > 0 ||\n        this.postContent.trim().length > 0 ||\n        this.selectedImages.length > 0\n      );\n    },\n\n    visibilityText() {\n      const map = {\n        public: \"公开\",\n        friends: \"仅朋友可见\",\n        private: \"仅自己可见\"\n      };\n      return map[this.visibility];\n    },\n\n    filteredTopics() {\n      if (!this.topicKeyword) return this.allTopics;\n      return this.allTopics.filter(topic =>\n        topic.name.includes(this.topicKeyword)\n      );\n    }\n  },\n\n  onLoad(options) {\n    console.log(\"=== 发布页面加载 ===\");\n    console.log(\"onLoad方法被调用了\", options);\n\n    // 首先检查用户登录状态\n    if (!this.checkUserLogin()) {\n      return; // 如果未登录，直接返回，不执行后续操作\n    }\n\n    // 显示一个提示确认方法被调用\n    uni.showToast({\n      title: \"onLoad被调用\",\n      icon: \"none\",\n      duration: 2000\n    });\n\n    this.testApiConnection();\n    this.loadUserInfo();\n    this.loadHotTopics();\n\n    // 检查是否从图片选择跳转过来\n    if (options && options.fromImageSelect === \"true\") {\n      this.handleImageSelectFromTabBar();\n    }\n  },\n\n  mounted() {\n    console.log(\"=== mounted生命周期被调用 ===\");\n\n    // 如果onLoad没有被调用，在这里也执行一次\n    if (this.userInfo.nickname === \"加载中...\") {\n      console.log(\"onLoad可能没有执行，在mounted中重新执行\");\n      this.testApiConnection();\n      this.loadUserInfo();\n      this.loadHotTopics();\n    }\n  },\n  methods: {\n    // 检查用户登录状态\n    checkUserLogin() {\n      const token = uni.getStorageSync(\"token\");\n      const userId = uni.getStorageSync(\"userid\");\n\n      if (!token || !userId) {\n        console.log(\"用户未登录，跳转到登录页\");\n        uni.showToast({\n          title: \"请先登录\",\n          icon: \"none\",\n          duration: 2000\n        });\n        setTimeout(() => {\n          uni.navigateTo({\n            url: \"/pages/login/login\"\n          });\n        }, 1500);\n        return false;\n      }\n\n      return true;\n    },\n\n    // 测试API连接\n    async testApiConnection() {\n      console.log(\"测试API连接...\");\n      try {\n        const response = await uni.request({\n          url: \"http://localhost:8101/api/health\",\n          method: \"GET\",\n          timeout: 5000\n        });\n        console.log(\"API连接测试结果:\", response);\n\n        if (response.statusCode === 200) {\n          console.log(\"✅ 后端服务连接正常\");\n        } else {\n          console.log(\"❌ 后端服务响应异常:\", response.statusCode);\n        }\n      } catch (error) {\n        console.error(\"❌ 后端服务连接失败:\", error);\n        uni.showToast({\n          title: \"后端服务连接失败\",\n          icon: \"none\",\n          duration: 3000\n        });\n      }\n    },\n\n    // 加载用户信息\n    async loadUserInfo() {\n      console.log(\"开始加载用户信息...\");\n      try {\n        // 从缓存中获取当前用户ID\n        const userId = uni.getStorageSync(\"userid\");\n        console.log(\"调用getUserProfile，用户ID:\", userId);\n\n        const result = await getUserProfile(userId);\n\n        console.log(\"获取用户信息结果:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          this.userInfo = {\n            avatar:\n              \"https://file.foxdance.com.cn\" +\n              result.data.avatar +\n              \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\",\n            nickname: result.data.nickname || \"无名氏\"\n          };\n        } else {\n          console.warn(\"获取用户信息失败:\", result);\n          this.userInfo = {\n            avatar: \"/static/images/toux.png\",\n            nickname: \"用户\"\n          };\n        }\n      } catch (error) {\n        console.error(\"加载用户信息失败:\", error);\n        console.error(\"错误详情:\", error.message || error);\n\n        uni.showToast({\n          title: \"用户信息加载失败\",\n          icon: \"none\",\n          duration: 2000\n        });\n\n        this.userInfo = {\n          avatar: \"/static/images/toux.png\",\n          nickname: \"用户\"\n        };\n      }\n    },\n\n    // 加载热门话题\n    async loadHotTopics() {\n      console.log(\"开始加载热门话题...\");\n      try {\n        const hotTags = await getHotTags(20);\n        console.log(\"获取热门话题结果:\", hotTags);\n\n        if (\n          hotTags &&\n          hotTags.code === 0 &&\n          hotTags.data &&\n          hotTags.data.length > 0\n        ) {\n          this.allTopics = hotTags.data.map(tag => ({\n            id: tag.id,\n            name: tag.name,\n            postCount: tag.useCount || 0\n          }));\n          console.log(\"话题加载成功，数量:\", this.allTopics.length);\n        } else if (hotTags && hotTags.length > 0) {\n          // 兼容直接返回数组的情况\n          this.allTopics = hotTags.map(tag => ({\n            id: tag.id,\n            name: tag.name,\n            postCount: tag.useCount || 0\n          }));\n          console.log(\"话题加载成功（数组格式），数量:\", this.allTopics.length);\n        } else {\n          console.log(\"没有获取到话题数据，hotTags:\", hotTags);\n        }\n      } catch (error) {\n        console.error(\"加载热门话题失败:\", error);\n        console.error(\"错误详情:\", error.message || error);\n\n        uni.showToast({\n          title: \"话题加载失败\",\n          icon: \"none\",\n          duration: 2000\n        });\n\n        // 使用默认话题列表\n        this.allTopics = [\n          { id: 1, name: \"生活\", postCount: 0 },\n          { id: 2, name: \"美食\", postCount: 0 },\n          { id: 3, name: \"旅行\", postCount: 0 },\n          { id: 4, name: \"摄影\", postCount: 0 },\n          { id: 5, name: \"时尚\", postCount: 0 }\n        ];\n        console.log(\"使用默认话题列表\");\n      }\n    },\n    goBack() {\n      if (this.postTitle || this.postContent || this.selectedImages.length) {\n        uni.showModal({\n          title: \"提示\",\n          content: \"确定要放弃编辑吗？\",\n          success: res => {\n            if (res.confirm) {\n              uni.navigateBack();\n            }\n          }\n        });\n      } else {\n        uni.navigateBack();\n      }\n    },\n\n    async chooseImage() {\n      const maxCount = 9 - this.selectedImages.length;\n\n      uni.chooseImage({\n        count: maxCount,\n        sizeType: [\"compressed\"],\n        sourceType: [\"album\", \"camera\"],\n        success: async res => {\n          const tempFilePaths = res.tempFilePaths;\n          console.log(\"🔥 选择图片成功，开始上传到COS:\", tempFilePaths);\n\n          // 显示上传进度\n          uni.showLoading({\n            title: \"上传图片中...\",\n            mask: true\n          });\n\n          try {\n            // 使用批量上传API\n            console.log(\"🔥 开始批量上传图片:\", tempFilePaths);\n\n            const result = await uploadPostImages(tempFilePaths);\n            console.log(\"🔥 批量上传结果:\", result);\n\n            // 隐藏加载提示\n            uni.hideLoading();\n\n            if (result.code === 0 && result.data) {\n              // 获取上传结果\n              const { images, coverImage } = result.data;\n\n              // 更新选中的图片列表\n              this.selectedImages = images;\n\n              // 保存封面图片URL（用于发布时传给后端）\n              this.coverImageUrl = coverImage;\n\n              console.log(\"✅ 批量上传成功:\", {\n                images: images.length,\n                coverImage\n              });\n\n              this.$u.toast(`成功上传${images.length}张图片`);\n            } else {\n              console.error(\"❌ 批量上传失败:\", result);\n              this.$u.toast(\"图片上传失败\");\n            }\n          } catch (error) {\n            uni.hideLoading();\n            console.error(\"❌ 图片上传过程异常:\", error);\n            this.$u.toast(\"图片上传失败\");\n          }\n        },\n        fail: error => {\n          console.error(\"❌ 选择图片失败:\", error);\n          this.$u.toast(\"选择图片失败\");\n        }\n      });\n    },\n\n    removeImage(index) {\n      this.selectedImages.splice(index, 1);\n    },\n\n    // 处理从TabBar图片选择跳转过来的情况\n    async handleImageSelectFromTabBar() {\n      console.log(\"🔥 处理从TabBar选择的图片\");\n\n      try {\n        // 从全局数据中获取选择的图片\n        const app = getApp();\n        const selectedImages = app.globalData.selectedImages;\n\n        if (selectedImages && selectedImages.length > 0) {\n          console.log(\"🔥 获取到选择的图片:\", selectedImages);\n\n          // 显示上传进度\n          uni.showLoading({\n            title: \"上传图片中...\",\n            mask: true\n          });\n\n          try {\n            // 使用批量上传API\n            const result = await uploadPostImages(selectedImages);\n            console.log(\"🔥 批量上传结果:\", result);\n\n            // 隐藏加载提示\n            uni.hideLoading();\n\n            if (result.code === 0 && result.data) {\n              // 获取上传结果\n              const { images, coverImage } = result.data;\n\n              // 更新选中的图片列表\n              this.selectedImages = images;\n\n              // 保存封面图片URL\n              this.coverImageUrl = coverImage;\n\n              console.log(\"✅ 批量上传成功:\", {\n                images: images.length,\n                coverImage\n              });\n\n              this.$u.toast(`成功上传${images.length}张图片`);\n            } else {\n              console.error(\"❌ 批量上传失败:\", result);\n              this.$u.toast(\"图片上传失败\");\n            }\n          } catch (uploadError) {\n            // 隐藏加载提示\n            uni.hideLoading();\n\n            console.error(\"❌ 批量上传异常:\", uploadError);\n            this.$u.toast(uploadError.message || \"图片上传失败\");\n          }\n\n          // 清除全局数据\n          app.globalData.selectedImages = null;\n        }\n      } catch (error) {\n        console.error(\"❌ 处理TabBar图片选择失败:\", error);\n        uni.hideLoading();\n        this.$u.toast(\"处理图片失败\");\n      }\n    },\n\n    selectTopic() {\n      this.showTopicModal = true;\n    },\n\n    toggleTopic(topic) {\n      const index = this.selectedTopics.indexOf(topic.name);\n      if (index > -1) {\n        this.selectedTopics.splice(index, 1);\n      } else {\n        if (this.selectedTopics.length < 3) {\n          this.selectedTopics.push(topic.name);\n        } else {\n          this.$u.toast(\"最多选择3个话题\");\n        }\n      }\n    },\n\n    searchTopics() {\n      // 搜索话题逻辑\n    },\n\n    selectLocation() {\n      console.log(\"打开位置选择...\");\n\n      // 使用uni.chooseLocation打开地图选择位置\n      uni.chooseLocation({\n        success: res => {\n          console.log(\"位置选择成功:\", res);\n\n          // 构建位置对象\n          this.selectedLocation = {\n            name: res.name || res.address,\n            address: res.address,\n            latitude: res.latitude,\n            longitude: res.longitude\n          };\n\n          uni.showToast({\n            title: \"位置选择成功\",\n            icon: \"success\",\n            duration: 1500\n          });\n        },\n        fail: err => {\n          console.error(\"位置选择失败:\", err);\n\n          if (err.errMsg && err.errMsg.includes(\"cancel\")) {\n            // 用户取消选择，不显示错误提示\n            return;\n          }\n\n          uni.showToast({\n            title: \"位置选择失败\",\n            icon: \"none\",\n            duration: 2000\n          });\n\n          // 如果地图选择失败，回退到弹窗选择\n          this.showLocationModal = true;\n        }\n      });\n    },\n\n    selectLocationItem(location) {\n      this.selectedLocation = location;\n      this.showLocationModal = false;\n    },\n\n    clearLocation() {\n      this.selectedLocation = null;\n      uni.showToast({\n        title: \"已清除位置\",\n        icon: \"success\",\n        duration: 1000\n      });\n    },\n\n    setVisibility() {\n      uni.showActionSheet({\n        itemList: [\"公开\", \"仅朋友可见\", \"仅自己可见\"],\n        success: res => {\n          const visibilityMap = [\"public\", \"friends\", \"private\"];\n          this.visibility = visibilityMap[res.tapIndex];\n        }\n      });\n    },\n\n    async publishPost() {\n      if (!this.canPublish || this.publishing) return;\n\n      // 检查用户登录状态\n      const userId = uni.getStorageSync(\"userid\");\n      if (!userId) {\n        uni.showToast({\n          title: \"请先登录\",\n          icon: \"none\",\n          duration: 2000\n        });\n        setTimeout(() => {\n          uni.navigateTo({\n            url: \"/pages/login/login\"\n          });\n        }, 1500);\n        return;\n      }\n\n      this.publishing = true;\n\n      try {\n        // 构建发布数据 - 符合PostCreateDTO格式\n        const postData = {\n          userId: Number(userId), // 从缓存获取当前用户ID\n          title: this.postTitle.trim() || null, // 单独发送标题字段\n          content: this.postContent.trim(),\n          images: this.selectedImages,\n          coverImage: this.coverImageUrl, // 封面图片URL\n          tags: this.selectedTopics.map(topic => topic.name || topic),\n          locationName: this.selectedLocation?.name || \"\",\n          locationLatitude: this.selectedLocation?.latitude || null,\n          locationLongitude: this.selectedLocation?.longitude || null,\n          locationAddress: this.selectedLocation?.address || \"\",\n          isPublic: this.visibility === \"public\" ? 1 : 0,\n          status: 1 // 1-已发布\n        };\n\n        console.log(\"发布帖子数据:\", postData);\n\n        // 调用发布API\n        const result = await createPost(postData);\n\n        console.log(\"发布API返回结果:\", result);\n\n        if (result && result.code === 0) {\n          this.$u.toast(\"发布成功\");\n        } else {\n          this.$u.toast(result?.message || \"发布失败，请重试\");\n          this.publishing = false;\n        }\n      } catch (error) {\n        console.error(\"发布帖子失败:\", error);\n        this.$u.toast(\"网络错误，请重试\");\n        this.publishing = false;\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.publish-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n}\n\n.content {\n  padding: 20px 16px;\n  padding-top: calc(20px + var(--status-bar-height)); /* 状态栏高度 */\n  padding-bottom: calc(20px + env(safe-area-inset-bottom)); /* 安全区域 */\n  width: auto;\n}\n\n.user-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.username {\n  margin-left: 12px;\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n/* 标题输入区域 */\n.title-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 12px;\n  border: 1px solid #f0f0f0;\n}\n\n.title-input {\n  width: 100%;\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  border: none;\n  outline: none;\n  background: transparent;\n  line-height: 1.4;\n}\n\n.title-input::placeholder {\n  color: #c0c4cc;\n  font-weight: 400;\n}\n\n.title-char-count {\n  text-align: right;\n  font-size: 12px;\n  color: #999;\n  margin-top: 8px;\n}\n\n.text-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  position: relative;\n}\n\n.content-input {\n  width: 100%;\n  min-height: 120px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #333;\n}\n\n.char-count {\n  position: absolute;\n  bottom: 12px;\n  right: 16px;\n  font-size: 12px;\n  color: #999;\n}\n\n.image-section {\n  margin-bottom: 16px;\n}\n\n.image-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.image-item {\n  position: relative;\n  width: calc(33.33% - 6px);\n  height: 100px;\n}\n\n.uploaded-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n}\n\n.delete-btn {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  width: 24px;\n  height: 24px;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-image-btn {\n  width: calc(33.33% - 6px);\n  height: 100px;\n  background: #f5f5f5;\n  border: 2px dashed #ddd;\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-text {\n  font-size: 12px;\n  color: #999;\n  margin-top: 4px;\n}\n\n.options-section {\n  background: #fff;\n  border-radius: 12px;\n  margin-bottom: 16px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.option-item:last-child {\n  border-bottom: none;\n}\n\n.option-left {\n  display: flex;\n  align-items: center;\n}\n\n.option-text {\n  margin-left: 12px;\n  font-size: 15px;\n  color: #333;\n}\n\n.option-right {\n  display: flex;\n  align-items: center;\n}\n\n.selected-topics,\n.selected-location,\n.visibility-text {\n  font-size: 14px;\n  color: #666;\n  margin-right: 8px;\n}\n\n/* 位置选择相关样式 */\n.location-selected {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  max-width: 200px;\n}\n\n.location-info-inline {\n  flex: 1;\n  margin-right: 8px;\n  overflow: hidden;\n}\n\n.selected-location {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.selected-address {\n  font-size: 12px;\n  color: #999;\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-top: 2px;\n}\n\n.tips-section {\n  padding: 16px;\n}\n\n.tips-text {\n  font-size: 12px;\n  color: #999;\n  line-height: 1.4;\n  text-align: center;\n}\n\n/* 发布按钮区域 */\n.publish-section {\n  padding: 24px 16px;\n  display: flex;\n  justify-content: center;\n}\n\n.publish-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  font-size: 16px;\n  font-weight: 600;\n  padding: 14px 48px;\n  border-radius: 28px;\n  text-align: center;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);\n  min-width: 120px;\n}\n\n.publish-btn.disabled {\n  background: #e4e7ed;\n  color: #c0c4cc;\n  box-shadow: none;\n}\n\n.publish-btn:not(.disabled):active {\n  transform: scale(0.95);\n  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.4);\n}\n\n.topic-modal,\n.location-modal {\n  background: #fff;\n  border-radius: 20px 20px 0 0;\n  max-height: 60vh;\n}\n\n.modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20px 20px 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.modal-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.topic-search {\n  padding: 16px 20px;\n}\n\n.topic-list,\n.location-list {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.topic-option {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.topic-option.selected {\n  background: #f0f8ff;\n}\n\n.topic-name {\n  font-size: 15px;\n  color: #333;\n}\n\n.topic-count {\n  font-size: 12px;\n  color: #999;\n}\n\n.location-option {\n  display: flex;\n  align-items: center;\n  padding: 12px 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.location-info {\n  margin-left: 12px;\n  flex: 1;\n}\n\n.location-name {\n  font-size: 15px;\n  color: #333;\n  display: block;\n}\n\n.location-address {\n  font-size: 12px;\n  color: #999;\n  margin-top: 2px;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753622569750\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2caef1dd&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2caef1dd\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/message/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2caef1dd&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.chatList, function (chat, __i0__) {\n    var $orig = _vm.__get_orig(chat)\n    var m0 = _vm.formatTime(chat.lastMessageTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = !_vm.chatList.length && !_vm.loading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"message-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"title\">消息</text>\n        <view class=\"header-actions\">\n          <u-icon name=\"search\" size=\"24\" color=\"#333\" @click=\"goSearch\"></u-icon>\n          <u-icon name=\"plus\" size=\"24\" color=\"#333\" @click=\"startNewChat\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能入口 -->\n    <view class=\"quick-actions\">\n      <view class=\"action-item\" @click=\"goSystemMessages\">\n        <view class=\"action-icon system\">\n          <u-icon name=\"bell\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-text\">系统消息</text>\n        <view v-if=\"systemUnreadCount\" class=\"unread-badge\">{{ systemUnreadCount }}</view>\n      </view>\n\n      <view class=\"action-item\" @click=\"goLikeMessages\">\n        <view class=\"action-icon like\">\n          <u-icon name=\"heart\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-text\">赞和评论</text>\n        <view v-if=\"likeUnreadCount\" class=\"unread-badge\">{{ likeUnreadCount }}</view>\n      </view>\n\n      <view class=\"action-item\" @click=\"goFollowMessages\">\n        <view class=\"action-icon follow\">\n          <u-icon name=\"account-fill\" color=\"#fff\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"action-text\">新粉丝</text>\n        <view v-if=\"followUnreadCount\" class=\"unread-badge\">{{ followUnreadCount }}</view>\n      </view>\n    </view>\n\n    <scroll-view\n      class=\"chat-list\"\n      scroll-y\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <!-- 聊天列表 -->\n      <view\n        v-for=\"chat in chatList\"\n        :key=\"chat.id\"\n        class=\"chat-item\"\n        @click=\"openChat(chat)\"\n        @longpress=\"showChatActions(chat)\"\n      >\n        <view class=\"chat-avatar\">\n          <u-avatar :src=\"chat.avatar\" size=\"50\"></u-avatar>\n          <view v-if=\"chat.isOnline\" class=\"online-dot\"></view>\n        </view>\n\n        <view class=\"chat-content\">\n          <view class=\"chat-header\">\n            <text class=\"chat-name\">{{ chat.name }}</text>\n            <text class=\"chat-time\">{{ formatTime(chat.lastMessageTime) }}</text>\n          </view>\n\n          <view class=\"chat-preview\">\n            <view class=\"message-preview\">\n              <text\n                v-if=\"chat.lastMessageType === 'text'\"\n                class=\"preview-text\"\n              >{{ chat.lastMessage }}</text>\n              <text v-else-if=\"chat.lastMessageType === 'image'\" class=\"preview-text\">[图片]</text>\n              <text v-else-if=\"chat.lastMessageType === 'voice'\" class=\"preview-text\">[语音]</text>\n              <text v-else class=\"preview-text\">{{ chat.lastMessage }}</text>\n            </view>\n\n            <view class=\"chat-status\">\n              <view\n                v-if=\"chat.unreadCount\"\n                class=\"unread-count\"\n              >{{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}</view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"!chatList.length && !loading\" class=\"empty-state\">\n        <u-icon name=\"chat\" color=\"#ccc\" size=\"60\"></u-icon>\n        <text class=\"empty-text\">暂无消息</text>\n        <text class=\"empty-desc\">开始与朋友聊天吧</text>\n      </view>\n\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-state\">\n        <u-loading mode=\"circle\" size=\"24\"></u-loading>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport {\n  getConversations,\n  getMessageUnreadCount,\n  markMessageAsRead\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialMessage\",\n  data() {\n    return {\n      chatList: [],\n      loading: false,\n      refreshing: false,\n      systemUnreadCount: 0,\n      likeUnreadCount: 0,\n      followUnreadCount: 0\n    };\n  },\n  onLoad() {\n    this.loadUnreadCounts();\n    this.loadChatList();\n  },\n\n  onShow() {\n    // 页面显示时检查是否需要加载数据\n    if (!this.chatList || this.chatList.length === 0) {\n      console.log(\"消息页显示时重新加载数据\");\n      this.loadUnreadCounts();\n      this.loadChatList();\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    if (!this.chatList || this.chatList.length === 0) {\n      console.log(\"消息页激活时重新加载数据\");\n      this.loadUnreadCounts();\n      this.loadChatList();\n    }\n  },\n  methods: {\n    // 加载未读消息统计\n    async loadUnreadCounts() {\n      try {\n        // 暂时将所有徽标都设为0，不显示假数据\n        this.systemUnreadCount = 0;\n        this.likeUnreadCount = 0;\n        this.followUnreadCount = 0;\n\n        // 如果需要真实数据，可以取消注释下面的代码\n        // const unreadData = await getMessageUnreadCount();\n        // if (unreadData && unreadData.code === 0 && unreadData.data) {\n        //   this.systemUnreadCount = unreadData.data.total || 0;\n        // }\n      } catch (error) {\n        console.error(\"加载未读消息统计失败:\", error);\n        // 使用默认值\n      }\n    },\n\n    async loadChatList() {\n      console.log(\"开始加载聊天列表...\");\n      this.loading = true;\n\n      try {\n        // 检查用户登录状态\n        const currentUserId = uni.getStorageSync(\"userid\");\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载聊天列表\");\n          this.chatList = [];\n          return;\n        }\n\n        const result = await getConversations({\n          current: 1,\n          size: 50\n        });\n\n        console.log(\"聊天列表API返回:\", result);\n\n        // 处理API返回的数据格式\n        let conversations = [];\n        if (\n          result &&\n          result.code === 0 &&\n          result.data &&\n          Array.isArray(result.data)\n        ) {\n          conversations = result.data;\n        } else if (result && Array.isArray(result)) {\n          conversations = result;\n        } else {\n          console.log(\"没有找到聊天列表或格式不正确\");\n          conversations = [];\n        }\n\n        if (conversations.length > 0) {\n          this.chatList = conversations.map(conversation => ({\n            id: conversation.id || conversation.conversationId,\n            userId: conversation.otherUserId || conversation.userId,\n            name:\n              conversation.otherUserNickname ||\n              conversation.nickname ||\n              \"用户\" + (conversation.otherUserId || conversation.userId),\n            avatar: this.formatAvatarUrl(\n              conversation.otherUserAvatar || conversation.avatar\n            ),\n            lastMessage: conversation.lastMessageContent || \"\",\n            lastMessageTime: conversation.lastMessageTime\n              ? new Date(conversation.lastMessageTime)\n              : new Date(),\n            lastMessageType: this.getMessageTypeString(\n              conversation.lastMessageType || 1\n            ),\n            unreadCount: conversation.unreadCount || 0,\n            isOnline: conversation.isOnline || false,\n            isMuted: conversation.isMuted || false\n          }));\n          console.log(\"会话列表\", this.chatList);\n          console.log(\"聊天列表处理完成，会话数量:\", this.chatList.length);\n        } else {\n          console.log(\"没有聊天记录，显示空状态\");\n          this.chatList = [];\n        }\n      } catch (error) {\n        console.error(\"加载聊天列表失败:\", error);\n        uni.showToast({\n          title: \"加载失败\",\n          icon: \"none\"\n        });\n        this.chatList = [];\n      } finally {\n        this.loading = false;\n        this.refreshing = false;\n      }\n    },\n\n    // 格式化头像URL\n    formatAvatarUrl(avatar) {\n      if (!avatar) {\n        return \"/static/images/toux.png\";\n      }\n\n      if (avatar.startsWith(\"http\")) {\n        return avatar;\n      }\n\n      return \"https://file.foxdance.com.cn\" + avatar;\n    },\n\n    // 获取消息类型字符串\n    getMessageTypeString(messageType) {\n      const typeMap = {\n        1: \"text\",\n        2: \"image\",\n        3: \"voice\",\n        4: \"video\",\n        5: \"file\"\n      };\n      return typeMap[messageType] || \"text\";\n    },\n\n    formatTime(time) {\n      const now = new Date();\n      const diff = now - new Date(time);\n      const minutes = Math.floor(diff / 60000);\n      const hours = Math.floor(diff / 3600000);\n      const days = Math.floor(diff / 86400000);\n\n      if (minutes < 1) return \"刚刚\";\n      if (minutes < 60) return `${minutes}分钟前`;\n      if (hours < 24) return `${hours}小时前`;\n      if (days < 7) return `${days}天前`;\n\n      const date = new Date(time);\n      return `${date.getMonth() + 1}/${date.getDate()}`;\n    },\n\n    onRefresh() {\n      this.refreshing = true;\n      this.loadChatList();\n    },\n\n    async openChat(chat) {\n      console.log(\"打开聊天:\", chat);\n\n      try {\n        // 标记消息已读\n        if (chat.unreadCount > 0) {\n          await markMessageAsRead({\n            senderId: chat.userId,\n            receiverId: uni.getStorageSync(\"userid\")\n          });\n\n          // 清除未读数\n          chat.unreadCount = 0;\n\n          // 更新聊天列表\n          const index = this.chatList.findIndex(c => c.id === chat.id);\n          if (index !== -1) {\n            this.$set(this.chatList, index, { ...chat });\n          }\n        }\n\n        // 跳转到聊天详情页，使用与用户主页一致的参数格式\n        uni.navigateTo({\n          url: `/pagesSub/social/chat/detail?userId=${\n            chat.userId\n          }&nickname=${encodeURIComponent(\n            chat.name\n          )}&avatar=${encodeURIComponent(chat.avatar)}`\n        });\n      } catch (error) {\n        console.error(\"打开聊天失败:\", error);\n        // 即使标记已读失败，也要跳转到聊天页面\n        uni.navigateTo({\n          url: `/pagesSub/social/chat/detail?userId=${\n            chat.userId\n          }&nickname=${encodeURIComponent(\n            chat.name\n          )}&avatar=${encodeURIComponent(chat.avatar)}`\n        });\n      }\n    },\n\n    showChatActions(chat) {\n      const actions = [\n        \"置顶\",\n        chat.isMuted ? \"取消免打扰\" : \"免打扰\",\n        \"删除聊天\"\n      ];\n\n      uni.showActionSheet({\n        itemList: actions,\n        success: res => {\n          switch (res.tapIndex) {\n            case 0:\n              this.toggleChatTop(chat);\n              break;\n            case 1:\n              this.toggleChatMute(chat);\n              break;\n            case 2:\n              this.deleteChat(chat);\n              break;\n          }\n        }\n      });\n    },\n\n    toggleChatTop(chat) {\n      // 置顶/取消置顶逻辑\n      this.$u.toast(\"置顶成功\");\n    },\n\n    toggleChatMute(chat) {\n      chat.isMuted = !chat.isMuted;\n      this.$u.toast(chat.isMuted ? \"已开启免打扰\" : \"已关闭免打扰\");\n    },\n\n    deleteChat(chat) {\n      uni.showModal({\n        title: \"确认删除\",\n        content: \"确定要删除这个聊天吗？\",\n        success: res => {\n          if (res.confirm) {\n            const index = this.chatList.findIndex(item => item.id === chat.id);\n            if (index > -1) {\n              this.chatList.splice(index, 1);\n              this.$u.toast(\"删除成功\");\n            }\n          }\n        }\n      });\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/search/chat\"\n      });\n    },\n\n    startNewChat() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/contact/select\"\n      });\n    },\n\n    goSystemMessages() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/message/system\"\n      });\n    },\n\n    goLikeMessages() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/message/likes\"\n      });\n    },\n\n    goFollowMessages() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/message/followers\"\n      });\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log(\"强制刷新消息页数据...\");\n      this.chatList = [];\n      this.loadUnreadCounts();\n      this.loadChatList();\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.message-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 100px;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 1px solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.header-actions {\n  display: flex;\n  gap: 16px;\n}\n\n.quick-actions {\n  margin-top: calc(44px + var(--status-bar-height));\n  background: #fff;\n  padding: 16px;\n  display: flex;\n  justify-content: space-around;\n  border-bottom: 8px solid #f8f9fa;\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n}\n\n.action-icon {\n  width: 44px;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px;\n}\n\n.action-icon.system {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.action-icon.like {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.action-icon.follow {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.action-text {\n  font-size: 12px;\n  color: #666;\n}\n\n.unread-badge {\n  position: absolute;\n  top: -2px;\n  right: 8px;\n  background: #ff4757;\n  color: #fff;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  min-width: 16px;\n  text-align: center;\n}\n\n.chat-list {\n  background: #fff;\n}\n\n.chat-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.chat-item:last-child {\n  border-bottom: none;\n}\n\n.chat-avatar {\n  position: relative;\n  margin-right: 12px;\n}\n\n.online-dot {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  width: 12px;\n  height: 12px;\n  background: #52c41a;\n  border: 2px solid #fff;\n  border-radius: 6px;\n}\n\n.chat-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.chat-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 4px;\n}\n\n.chat-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.chat-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.chat-preview {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.message-preview {\n  flex: 1;\n  min-width: 0;\n}\n\n.preview-text {\n  font-size: 14px;\n  color: #666;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n\n.chat-status {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.unread-count {\n  background: #ff4757;\n  color: #fff;\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 10px;\n  min-width: 18px;\n  text-align: center;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80px 20px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #999;\n  margin: 16px 0 8px;\n}\n\n.empty-desc {\n  font-size: 14px;\n  color: #ccc;\n}\n\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.loading-text {\n  margin-left: 8px;\n  color: #999;\n  font-size: 14px;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753622570505\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4df458ff&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4df458ff\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4df458ff&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-empty/u-empty\" */ \"@/components/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.tabs[_vm.currentTab] && _vm.tabs[_vm.currentTab].data.length > 0\n  var l0 =\n    g0 &&\n    !(_vm.currentTab === 0 || _vm.currentTab === 1 || _vm.currentTab === 4) &&\n    (_vm.currentTab === 2 || _vm.currentTab === 3)\n      ? _vm.__map(_vm.tabs[_vm.currentTab].data, function (user, __i1__) {\n          var $orig = _vm.__get_orig(user)\n          var a0 = {\n            id: user.id,\n            nickname: user.nickname,\n          }\n          return {\n            $orig: $orig,\n            a0: a0,\n          }\n        })\n      : null\n  var m0 = !g0 ? _vm.getEmptyText() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"profile-container\">\n    <!-- 蓝色渐变背景头部 -->\n    <view class=\"header-section\">\n      <view class=\"header-bg\"></view>\n\n      <!-- 顶部操作按钮 -->\n      <view class=\"header-actions\">\n        <u-icon name=\"scan\" color=\"#fff\" size=\"48rpx\" @click=\"scanCode\"></u-icon>\n        <u-icon name=\"setting\" color=\"#fff\" size=\"48rpx\" @click=\"goSettings\"></u-icon>\n      </view>\n\n      <!-- 用户信息 -->\n      <view class=\"user-info-section\">\n        <view class=\"user-avatar-container\">\n          <u-avatar :src=\"userInfo.avatar\" size=\"120\" @click=\"editAvatar\"></u-avatar>\n        </view>\n\n        <!-- 用户信息内容容器 -->\n        <view class=\"user-info-content\">\n          <!-- 加载状态 -->\n          <view v-if=\"loading\" class=\"loading-container\">\n            <view class=\"loading-spinner\"></view>\n            <text class=\"loading-text\">加载中...</text>\n          </view>\n\n          <!-- 用户信息 -->\n          <view v-else class=\"user-info-row\">\n            <!-- 左侧用户信息 -->\n            <view class=\"user-details\">\n              <text class=\"nickname\">{{ userInfo.nickname || '加载中...' }}</text>\n              <text class=\"social-id\">社区ID：{{ userInfo.socialId || '--' }}</text>\n              <text class=\"dance-type\">舞种：{{ userInfo.danceType || '--' }}</text>\n              <text class=\"bio\">{{ userInfo.bio || '暂无个人简介' }}</text>\n            </view>\n\n            <!-- 右侧编辑链接 -->\n            <view class=\"edit-section\">\n              <text class=\"edit-link\" @click=\"editProfile\">编辑资料</text>\n            </view>\n          </view>\n\n          <!-- 数据统计 -->\n          <view class=\"stats-row\">\n            <view class=\"stat-item\" @click=\"switchTab(0)\">\n              <text class=\"stat-number\">{{ userInfo.postCount }}</text>\n              <text class=\"stat-label\">帖子</text>\n            </view>\n            <view class=\"stat-item\" @click=\"switchTab(2)\">\n              <text class=\"stat-number\">{{ userInfo.followingCount }}</text>\n              <text class=\"stat-label\">关注</text>\n            </view>\n            <view class=\"stat-item\" @click=\"switchTab(3)\">\n              <text class=\"stat-number\">{{ userInfo.followersCount }}</text>\n              <text class=\"stat-label\">粉丝</text>\n            </view>\n            <view class=\"stat-item\">\n              <text class=\"stat-number\">{{ userInfo.likeCount }}</text>\n              <text class=\"stat-label\">获赞</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 操作按钮 -->\n      <view class=\"tabs-container\">\n        <u-tabs\n          :list=\"tabs\"\n          :current=\"currentTab\"\n          @change=\"switchTab\"\n          lineWidth=\"30\"\n          lineColor=\"#303133\"\n          :activeStyle=\"{ color: '#303133', fontWeight: 'bold' }\"\n          :inactiveStyle=\"{ color: '#606266' }\"\n        ></u-tabs>\n      </view>\n\n      <!-- 内容列表 -->\n      <view class=\"posts-content\">\n        <view v-if=\"tabs[currentTab] && tabs[currentTab].data.length > 0\">\n          <!-- 帖子列表 (作品、私密作品、喜欢) -->\n          <view v-if=\"currentTab === 0 || currentTab === 1 || currentTab === 4\" class=\"post-grid\">\n            <PostCard\n              v-for=\"post in tabs[currentTab].data\"\n              :key=\"post.id\"\n              :post=\"post\"\n              class=\"post-card-item\"\n              @click=\"goPostDetail\"\n              @user-click=\"goUserProfile\"\n              @like=\"onPostLike\"\n            />\n          </view>\n\n          <!-- 用户列表 (关注、粉丝) -->\n          <view v-else-if=\"currentTab === 2 || currentTab === 3\" class=\"user-list\">\n            <view\n              v-for=\"user in tabs[currentTab].data\"\n              :key=\"user.id\"\n              class=\"user-item\"\n              @click=\"goUserProfile(user)\"\n            >\n              <u-avatar :src=\"user.avatar\" size=\"80\" class=\"user-avatar\"></u-avatar>\n              <view class=\"user-info\">\n                <text class=\"user-nickname\">{{ user.nickname }}</text>\n                <text class=\"user-bio\">{{ user.bio }}</text>\n                <text class=\"user-stats\">{{ user.followersCount }} 粉丝</text>\n              </view>\n              <view class=\"user-action\">\n                <FollowButton\n                  :user=\"{ id: user.id, nickname: user.nickname }\"\n                  :followed=\"user.isFollowed\"\n                  size=\"mini\"\n                  @follow=\"onUserFollow\"\n                  @unfollow=\"onUserUnfollow\"\n                  @change=\"onFollowChange\"\n                  @click.stop\n                />\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <view v-else class=\"empty-state\">\n          <u-empty mode=\"list\" :text=\"getEmptyText()\"></u-empty>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport PostCard from \"../components/PostCard.vue\";\nimport FollowButton from \"../components/FollowButton.vue\";\nimport {\n  getUserProfile,\n  updateUserProfile,\n  getPostList,\n  likePost,\n  unlikePost,\n  getFollowingList,\n  getFollowersList,\n  getUserPostStats\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialProfile\",\n  components: {\n    PostCard,\n    FollowButton\n  },\n  data() {\n    return {\n      userInfo: {\n        userId: \"\",\n        nickname: \"\",\n        avatar: \"\",\n        bio: \"\",\n        danceType: \"\",\n        postCount: 0,\n        privatePostCount: 0, // 私密作品数\n        followingCount: 0,\n        followersCount: 0,\n        likeCount: 0,\n        draftCount: 0\n      },\n      loading: true,\n      currentTab: 0,\n      isInitialized: false,\n      tabs: [\n        { name: \"作品\", data: [], loading: false },\n        { name: \"私密作品\", data: [], loading: false },\n        { name: \"关注\", data: [], loading: false },\n        { name: \"粉丝\", data: [], loading: false },\n        { name: \"喜欢\", data: [], loading: false }\n      ]\n    };\n  },\n  onLoad() {\n    console.log(\"Profile页面 onLoad\");\n    // 延迟加载，确保页面完全初始化\n    this.$nextTick(() => {\n      setTimeout(() => {\n        this.initializeData();\n      }, 100);\n    });\n  },\n  onShow() {\n    console.log(\"Profile页面 onShow\");\n    // 页面显示时刷新数据\n    if (this.isInitialized) {\n      this.loadUserInfo();\n    }\n  },\n  methods: {\n    // 初始化数据\n    async initializeData() {\n      console.log(\"初始化Profile页面数据...\");\n      try {\n        await this.loadUserInfo();\n        this.isInitialized = true;\n      } catch (error) {\n        console.error(\"初始化数据失败:\", error);\n      }\n    },\n\n    async loadUserInfo() {\n      try {\n        // 获取当前用户ID\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载用户信息\");\n          uni.showToast({\n            title: \"请先登录\",\n            icon: \"none\"\n          });\n          return;\n        }\n\n        console.log(\"开始加载用户信息 - userId:\", currentUserId);\n        this.loading = true;\n\n        const result = await getUserProfile(currentUserId);\n        console.log(\"用户信息API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const userProfile = result.data;\n          this.userInfo = {\n            userId: userProfile.id || userProfile.userId || currentUserId,\n            nickname: userProfile.nickname || \"舞蹈爱好者\",\n            avatar: this.formatAvatarUrl(userProfile.avatar),\n            bio:\n              userProfile.bio ||\n              userProfile.userProfile ||\n              \"热爱舞蹈，享受生活\",\n            danceType: userProfile.danceType || \"街舞\",\n            postCount: userProfile.postCount || 0,\n            privatePostCount: 0, // 先设为0，后面会更新\n            followingCount: userProfile.followingCount || 0,\n            followersCount:\n              userProfile.followersCount || userProfile.followerCount || 0,\n            likeCount:\n              userProfile.likeReceivedCount || userProfile.likeCount || 0,\n            draftCount: userProfile.draftCount || 0\n          };\n\n          console.log(\"用户信息加载成功:\", this.userInfo);\n\n          // 加载私密作品数量\n          await this.loadPrivatePostCount(currentUserId);\n\n          // 加载用户帖子数据\n          this.loadTabData(this.currentTab);\n        } else {\n          console.error(\"用户信息API返回格式不正确:\", result);\n          uni.showToast({\n            title: \"加载用户信息失败\",\n            icon: \"none\"\n          });\n        }\n      } catch (error) {\n        console.error(\"加载用户信息失败:\", error);\n        uni.showToast({\n          title: \"加载失败，请重试\",\n          icon: \"none\"\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 加载私密作品数量\n    async loadPrivatePostCount(userId) {\n      try {\n        // 使用getPostList API获取私密作品数量\n        const result = await getPostList({\n          current: 1,\n          size: 1, // 只需要获取数量，不需要具体数据\n          userId: userId,\n          isPublic: 0 // 只查询私密作品\n        });\n\n        if (result && result.code === 0 && result.data) {\n          // 如果返回的是分页对象，获取total字段\n          if (result.data.total !== undefined) {\n            this.userInfo.privatePostCount = result.data.total;\n          } else if (Array.isArray(result.data)) {\n            // 如果返回的是数组，需要再次查询获取准确数量\n            // 这里暂时设为数组长度，实际应该有total字段\n            this.userInfo.privatePostCount = result.data.length;\n          }\n        }\n\n        console.log(\"私密作品数量:\", this.userInfo.privatePostCount);\n      } catch (error) {\n        console.error(\"加载私密作品数量失败:\", error);\n        this.userInfo.privatePostCount = 0;\n      }\n    },\n\n    // 获取当前用户ID\n    getCurrentUserId() {\n      const userId = uni.getStorageSync(\"userid\");\n      return userId ? Number(userId) : null;\n    },\n\n    // 格式化头像URL\n    formatAvatarUrl(avatar) {\n      if (avatar) {\n        return (\n          \"https://file.foxdance.com.cn\" +\n          avatar +\n          \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\"\n        );\n      }\n      return \"static/images/toux.png\";\n    },\n\n    async loadTabData(tabIndex) {\n      if (this.tabs[tabIndex].loading) return;\n\n      this.$set(this.tabs[tabIndex], \"loading\", true);\n\n      try {\n        let data = [];\n\n        switch (tabIndex) {\n          case 0: // 作品\n            data = await this.loadUserPosts();\n            break;\n          case 1: // 私密作品\n            data = await this.loadPrivatePosts();\n            break;\n          case 2: // 关注\n            data = await this.loadFollowingUsers();\n            break;\n          case 3: // 粉丝\n            data = await this.loadFollowersUsers();\n            break;\n          case 4: // 喜欢\n            data = await this.loadLikedPosts();\n            break;\n        }\n\n        this.$set(this.tabs[tabIndex], \"data\", data);\n      } catch (error) {\n        console.error(`加载标签页${tabIndex}数据失败:`, error);\n        this.$set(this.tabs[tabIndex], \"data\", []);\n      } finally {\n        this.$set(this.tabs[tabIndex], \"loading\", false);\n      }\n    },\n\n    // 加载用户发布的公开帖子\n    async loadUserPosts() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载帖子\");\n          return [];\n        }\n\n        const result = await getPostList({\n          current: 1,\n          size: 20,\n          userId: currentUserId,\n          isPublic: 1, // 只加载公开帖子\n          sortField: \"createTime\",\n          sortOrder: \"desc\"\n        });\n\n        console.log(\"用户帖子API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const posts = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          return posts.map(post => ({\n            id: post.id,\n            title: post.title || \"\",\n            coverImage: post.coverImage,\n            username: post.nickname || this.userInfo.nickname,\n            userAvatar:\n              \"https://file.foxdance.com.cn\" +\n              post.avatar +\n              \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\",\n            content: post.content,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            isPublic: post.isPublic, // 添加私密状态字段\n            status: post.status, // 添加帖子状态字段\n            createTime: new Date(post.createTime)\n          }));\n        } else {\n          console.log(\"用户帖子API返回格式不正确:\", result);\n          return [];\n        }\n      } catch (error) {\n        console.error(\"加载用户帖子失败:\", error);\n        return [];\n      }\n    },\n\n    // 加载用户发布的私密帖子\n    async loadPrivatePosts() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载私密帖子\");\n          return [];\n        }\n\n        const result = await getPostList({\n          current: 1,\n          size: 20,\n          userId: currentUserId,\n          isPublic: 0, // 只加载私密帖子\n          sortField: \"createTime\",\n          sortOrder: \"desc\"\n        });\n\n        console.log(\"用户私密帖子API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const posts = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          return posts.map(post => ({\n            id: post.id,\n            title: post.title || \"\",\n            coverImage: post.coverImage,\n            username: post.nickname || this.userInfo.nickname,\n            userAvatar: \"https://file.foxdance.com.cn\" + post.avatar,\n            content: post.content,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            isPublic: post.isPublic, // 私密帖子标识\n            status: post.status,\n            createTime: new Date(post.createTime)\n          }));\n        } else {\n          console.log(\"用户私密帖子API返回格式不正确:\", result);\n          return [];\n        }\n      } catch (error) {\n        console.error(\"加载用户私密帖子失败:\", error);\n        return [];\n      }\n    },\n\n    // 加载用户点赞的帖子\n    async loadLikedPosts() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载点赞帖子\");\n          return [];\n        }\n\n        // TODO: 实现获取用户点赞帖子的API\n        console.log(\"加载用户点赞的帖子 - userId:\", currentUserId);\n        // 暂时返回空数组，等待后端API实现\n        return [];\n      } catch (error) {\n        console.error(\"加载点赞帖子失败:\", error);\n        return [];\n      }\n    },\n\n    // 加载关注的用户\n    async loadFollowingUsers() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载关注列表\");\n          return [];\n        }\n\n        console.log(\"开始加载关注用户列表...\");\n        const result = await getFollowingList(currentUserId, {\n          current: 1,\n          size: 50\n        });\n        console.log(\"关注用户API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const users = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          return users.map(user => ({\n            id: user.userId || user.id,\n            userId: user.userId || user.id, // 确保有userId字段\n            nickname: user.nickname || \"用户\",\n            avatar: this.formatAvatarUrl(user.avatar),\n            bio: user.bio || \"暂无简介\",\n            danceType: user.danceType || \"\",\n            followersCount: user.followerCount || 0,\n            isFollowed: true, // 关注列表中的用户都是已关注的\n            type: \"user\" // 标识这是用户类型数据\n          }));\n        } else {\n          console.error(\"关注用户API返回格式不正确:\", result);\n          return [];\n        }\n      } catch (error) {\n        console.error(\"加载关注用户失败:\", error);\n        return [];\n      }\n    },\n\n    // 加载粉丝用户\n    async loadFollowersUsers() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载粉丝列表\");\n          return [];\n        }\n\n        console.log(\"开始加载粉丝用户列表...\");\n        const result = await getFollowersList(currentUserId, {\n          current: 1,\n          size: 50\n        });\n        console.log(\"粉丝用户API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const users = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          return users.map(user => ({\n            id: user.userId || user.id,\n            userId: user.userId || user.id, // 确保有userId字段\n            nickname: user.nickname || \"用户\",\n            avatar: this.formatAvatarUrl(user.avatar),\n            bio: user.bio || \"暂无简介\",\n            danceType: user.danceType || \"\",\n            followersCount: user.followerCount || 0,\n            isFollowed: user.isFollowed || false, // 需要检查是否互相关注\n            type: \"user\" // 标识这是用户类型数据\n          }));\n        } else {\n          console.error(\"粉丝用户API返回格式不正确:\", result);\n          return [];\n        }\n      } catch (error) {\n        console.error(\"加载粉丝用户失败:\", error);\n        return [];\n      }\n    },\n\n    switchTab(item) {\n      const index = typeof item === \"object\" ? item.index : item;\n      if (this.currentTab === index) return;\n      this.currentTab = index;\n      this.loadTabData(index);\n    },\n\n    scanCode() {\n      uni.scanCode({\n        success: res => {\n          console.log(\"扫码结果:\", res);\n        }\n      });\n    },\n\n    goSettings() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/settings/index\"\n      });\n    },\n\n    editAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: [\"compressed\"],\n        sourceType: [\"album\", \"camera\"],\n        success: res => {\n          // 上传头像\n          this.userInfo.avatar = res.tempFilePaths[0];\n        }\n      });\n    },\n\n    editProfile() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/profile/edit\"\n      });\n    },\n\n    goLikeList() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/like/list\"\n      });\n    },\n\n    viewPost(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      });\n    },\n\n    // PostCard组件需要的方法\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      });\n    },\n\n    goUserProfile(data) {\n      // 判断是帖子数据还是用户数据\n      if (data.type === \"user\") {\n        // 用户数据，直接跳转\n        const userId = data.id || data.userId;\n        if (userId) {\n          uni.navigateTo({\n            url: `/pagesSub/social/user/profile?userId=${userId}&name=${data.nickname}`\n          });\n        }\n      } else {\n        // 帖子数据，跳转到帖子作者页面\n        // 如果是自己的帖子，不需要跳转\n        if (data.username === this.userInfo.nickname) return;\n\n        uni.navigateTo({\n          url: `/pagesSub/social/user/profile?id=${data.userId || data.id}`\n        });\n      }\n    },\n\n    async onPostLike(post) {\n      try {\n        if (post.isLiked) {\n          // 取消点赞\n          await unlikePost(post.id);\n          post.isLiked = false;\n          post.likeCount = Math.max(0, post.likeCount - 1);\n          uni.showToast({\n            title: \"取消点赞\",\n            icon: \"none\",\n            duration: 1000\n          });\n        } else {\n          // 点赞\n          await likePost(post.id);\n          post.isLiked = true;\n          post.likeCount += 1;\n          uni.showToast({\n            title: \"点赞成功\",\n            icon: \"success\",\n            duration: 1000\n          });\n        }\n\n        // 更新对应标签页中的帖子数据\n        const currentTabData = this.tabs[this.currentTab].data;\n        const index = currentTabData.findIndex(p => p.id === post.id);\n        if (index !== -1) {\n          this.$set(currentTabData, index, { ...post });\n        }\n      } catch (error) {\n        console.error(\"点赞操作失败:\", error);\n        uni.showToast({\n          title: \"操作失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    // 强制刷新页面数据（供父组件调用）\n    forceRefresh() {\n      console.log(\"强制刷新个人资料页面数据...\");\n      // 重置数据状态\n      this.isInitialized = false;\n      this.loading = true;\n\n      // 清空当前数据\n      this.userInfo = {\n        userId: \"\",\n        nickname: \"\",\n        avatar: \"\",\n        bio: \"\",\n        danceType: \"\",\n        postCount: 0,\n        followingCount: 0,\n        followersCount: 0,\n        likeCount: 0,\n        draftCount: 0\n      };\n\n      // 清空tabs数据\n      this.tabs.forEach(tab => {\n        tab.data = [];\n        tab.loading = false;\n      });\n\n      // 重新初始化数据\n      this.initializeData();\n    },\n\n    // 获取空状态文本\n    getEmptyText() {\n      const tabNames = [\n        \"暂无作品\",\n        \"暂无私密作品\",\n        \"暂无关注\",\n        \"暂无粉丝\",\n        \"暂无喜欢\"\n      ];\n      return tabNames[this.currentTab] || \"暂无内容\";\n    },\n\n    // 用户关注成功事件\n    onUserFollow(data) {\n      console.log(\"用户关注成功:\", data);\n      // 更新本地数据\n      this.updateUserFollowStatus(data.user.id, true);\n\n      // 更新关注数统计\n      this.userInfo.followingCount += 1;\n    },\n\n    // 用户取消关注成功事件\n    onUserUnfollow(data) {\n      console.log(\"用户取消关注成功:\", data);\n      // 更新本地数据\n      this.updateUserFollowStatus(data.user.id, false);\n\n      // 更新关注数统计\n      if (this.userInfo.followingCount > 0) {\n        this.userInfo.followingCount -= 1;\n      }\n    },\n\n    // 关注状态变化事件\n    onFollowChange(data) {\n      console.log(\"关注状态变化:\", data);\n      // 更新本地数据\n      this.updateUserFollowStatus(data.user.id, data.isFollowed);\n    },\n\n    // 更新用户关注状态的辅助方法\n    updateUserFollowStatus(userId, isFollowed) {\n      // 更新关注列表中的状态\n      if (this.tabs[2] && this.tabs[2].data) {\n        const followingUser = this.tabs[2].data.find(u => u.id === userId);\n        if (followingUser) {\n          followingUser.isFollowed = isFollowed;\n        }\n      }\n\n      // 更新粉丝列表中的状态\n      if (this.tabs[3] && this.tabs[3].data) {\n        const followerUser = this.tabs[3].data.find(u => u.id === userId);\n        if (followerUser) {\n          followerUser.isFollowed = isFollowed;\n        }\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-container {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding-bottom: 100rpx;\n}\n\n.header-section {\n  position: relative;\n  background: #fff;\n}\n\n.header-bg {\n  height: 400rpx;\n  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);\n}\n\n.header-actions {\n  position: absolute;\n  top: 60rpx;\n  right: 32rpx;\n  display: flex;\n  gap: 32rpx;\n  z-index: 10;\n}\n\n.user-info-section {\n  padding: 40rpx 50rpx;\n  background: #f8f9fa;\n}\n\n.user-info-content {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-top: 50rpx;\n  border: 1rpx solid #e9ecef;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.user-avatar-container {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 24rpx;\n  position: absolute;\n  top: 340rpx;\n  left: 12%;\n}\n\n.user-info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 24rpx;\n  padding: 20rpx 40rpx;\n}\n\n.user-details {\n  flex: 1;\n  text-align: left;\n}\n\n.edit-section {\n  flex-shrink: 0;\n  margin-left: 20rpx;\n  display: flex;\n  align-items: flex-start;\n}\n\n.edit-link {\n  font-size: 28rpx;\n  font-weight: 500;\n  border: 1rpx solid #2979ff;\n  border-radius: 10rpx;\n  padding: 10rpx 20rpx;\n  margin: 10rpx;\n  color: #2979ff;\n}\n\n.nickname {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n\n\n.social-id {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n  margin-bottom: 12rpx;\n  font-weight: 500;\n}\n\n.dance-type {\n  font-size: 26rpx;\n  color: #999;\n  display: block;\n  margin-bottom: 16rpx;\n  font-weight: 500;\n}\n\n.bio {\n  font-size: 28rpx;\n  color: #999;\n  line-height: 1.4;\n  display: block;\n}\n\n.stats-row {\n  display: flex;\n  justify-content: center;\n  gap: 80rpx;\n  margin-bottom: 0;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-number {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.tabs-container {\n  background: #fff;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.content {\n  background: #fff;\n  min-height: 60vh;\n}\n\n.posts-content {\n  padding: 32rpx;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 14rpx;\n}\n\n.post-card-item {\n  width: calc(50% - 8rpx);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n\n/* Loading 相关样式 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx 0;\n}\n\n.loading-spinner {\n  width: 40rpx;\n  height: 40rpx;\n  border: 4rpx solid #f3f3f3;\n  border-top: 4rpx solid #007aff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20rpx;\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 用户列表样式 */\n.user-list {\n  padding: 0 32rpx;\n}\n\n.user-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.user-item:last-child {\n  border-bottom: none;\n}\n\n.user-avatar {\n  margin-right: 24rpx;\n}\n\n.user-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.user-nickname {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.user-bio {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 8rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.user-stats {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.user-action {\n  margin-left: 24rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753623719617\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0c4b5bf9&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0c4b5bf9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753622569507\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}