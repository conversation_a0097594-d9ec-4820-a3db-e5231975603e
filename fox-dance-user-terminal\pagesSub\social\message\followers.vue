<template>
  <view class="followers-container">
    <!-- 消息列表 -->
    <scroll-view 
      class="message-list"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view v-if="messageList && messageList.length > 0">
        <view 
          v-for="message in messageList" 
          :key="message.id"
          class="message-card"
          @click="openUserProfile(message.userId)"
        >
          <!-- 用户头像 -->
          <u-avatar 
            :src="message.userAvatar" 
            size="50"
            class="user-avatar"
          ></u-avatar>
          
          <!-- 用户信息 -->
          <view class="user-info">
            <view class="user-header">
              <text class="user-name">{{ message.userName }}</text>
              <text class="follow-time">{{ formatTime(message.createTime) }}</text>
            </view>
            <text class="user-desc">{{ message.userDesc || '这个人很懒，什么都没有留下' }}</text>
            
            <!-- 用户标签 -->
            <view v-if="message.userTags && message.userTags.length > 0" class="user-tags">
              <u-tag 
                v-for="tag in message.userTags" 
                :key="tag"
                :text="tag"
                size="mini"
                type="primary"
                mode="light"
                class="tag-item"
              ></u-tag>
            </view>
          </view>
          
          <!-- 关注按钮 -->
          <view class="follow-actions">
            <FollowButton
              :user="{ id: message.userId, nickname: message.userName }"
              :followed="message.isFollowBack"
              size="mini"
              @follow="onUserFollow"
              @unfollow="onUserUnfollow"
              @change="onFollowChange"
              @click.stop
            />
          </view>
          
          <!-- 未读标识 -->
          <view v-if="!message.isRead" class="unread-dot"></view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <u-empty mode="data" text="暂无新粉丝"></u-empty>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && messageList && messageList.length > 0" class="load-more">
        <u-loading mode="flower" size="24"></u-loading>
        <text class="load-text">加载中...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import FollowButton from '../components/FollowButton.vue'
import { getFollowersList } from '@/utils/socialApi.js'

export default {
  name: 'NewFollowers',
  components: {
    FollowButton
  },
  data() {
    return {
      isRefreshing: false,
      hasMore: true,
      page: 1,
      pageSize: 20,
      loading: false,
      messageList: [],
      followBtnStyle: {
        width: '120rpx',
        height: '60rpx',
        fontSize: '24rpx'
      },
      followedBtnStyle: {
        width: '120rpx',
        height: '60rpx',
        fontSize: '24rpx',
        color: '#999',
        borderColor: '#e4e7ed'
      }
    }
  },
  onLoad() {
    this.loadMessages()
  },
  methods: {
    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 60) {
        return `${minutes}分钟前关注了你`
      } else if (hours < 24) {
        return `${hours}小时前关注了你`
      } else {
        return `${days}天前关注了你`
      }
    },

    openUserProfile(userId) {
      // 标记为已读
      const message = this.messageList.find(msg => msg.userId === userId)
      if (message) {
        message.isRead = true
      }

      // 跳转到用户主页
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?userId=${userId}&name=${message ? message.userName : ''}`
      })
    },

    // 用户关注成功事件
    onUserFollow(data) {
      console.log('用户关注成功:', data)
      // 更新本地数据
      this.updateUserFollowStatus(data.user.id, true)
    },

    // 用户取消关注成功事件
    onUserUnfollow(data) {
      console.log('用户取消关注成功:', data)
      // 更新本地数据
      this.updateUserFollowStatus(data.user.id, false)
    },

    // 关注状态变化事件
    onFollowChange(data) {
      console.log('关注状态变化:', data)
      // 更新本地数据
      this.updateUserFollowStatus(data.user.id, data.isFollowed)
    },

    // 更新用户关注状态的辅助方法
    updateUserFollowStatus(userId, isFollowed) {
      const message = this.messageList.find(msg => msg.userId === userId)
      if (message) {
        message.isFollowBack = isFollowed
      }
    },

    onRefresh() {
      this.isRefreshing = true
      this.page = 1
      this.loadMessages().finally(() => {
        this.isRefreshing = false
      })
    },

    loadMore() {
      if (!this.hasMore) return
      this.page++
      this.loadMessages()
    },

    async loadMessages() {
      if (this.loading) return

      try {
        this.loading = true
        const currentUserId = this.getCurrentUserId()
        if (!currentUserId) {
          console.error('用户未登录，无法加载粉丝列表')
          return
        }

        console.log('开始加载新粉丝列表...')
        const result = await getFollowersList(currentUserId, {
          current: this.page,
          size: this.pageSize
        })
        console.log('新粉丝API返回:', result)

        if (result && result.code === 0 && result.data) {
          const followers = Array.isArray(result.data) ? result.data : (result.data.records || [])

          // 转换数据格式
          const newMessages = followers.map(follower => ({
            id: follower.userId || follower.id,
            userId: follower.userId || follower.id,
            userName: follower.nickname || '用户',
            userAvatar: this.formatAvatarUrl(follower.avatar),
            userDesc: follower.bio || '这个人很懒，什么都没有留下',
            userTags: this.parseUserTags(follower.danceType),
            createTime: new Date(follower.followTime || follower.createTime || Date.now()),
            isRead: Math.random() > 0.5, // 暂时随机生成已读状态
            isFollowBack: follower.isFollowed || false
          }))

          if (this.page === 1) {
            this.messageList = newMessages
          } else {
            this.messageList.push(...newMessages)
          }

          // 检查是否还有更多数据
          this.hasMore = followers.length >= this.pageSize
        } else {
          console.error('新粉丝API返回格式不正确:', result)
          if (this.page === 1) {
            this.messageList = []
          }
        }
      } catch (error) {
        console.error('加载新粉丝失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 获取当前用户ID
    getCurrentUserId() {
      return uni.getStorageSync('userid')
    },

    // 格式化头像URL
    formatAvatarUrl(avatar) {
      if (!avatar) return '/static/images/toux.png'
      if (avatar.startsWith('http')) return avatar
      return avatar.startsWith('/') ? avatar : `/${avatar}`
    },

    // 解析用户标签
    parseUserTags(danceType) {
      if (!danceType) return []
      return [danceType]
    }
  }
}
</script>

<style lang="scss" scoped>
.followers-container {
  height: 100vh;
  background: #f5f5f5;
}

.message-list {
  height: 100%;
  padding: 32rpx 0;
}

.message-card {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  margin: 0 32rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.user-avatar {
  margin-right: 24rpx;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  margin-right: 24rpx;
}

.user-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.follow-time {
  font-size: 24rpx;
  color: #999;
}

.user-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  margin: 0;
}

.follow-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.unread-dot {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
}

.empty-state {
  display: flex;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.load-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}
</style>
