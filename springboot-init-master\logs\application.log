2025-07-27 21:22:01.604 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 23720 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-27 21:22:01.613 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-27 21:22:01.715 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-27 21:22:01.715 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-27 21:22:03.295 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-27 21:22:03.317 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.318 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.319 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.320 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.320 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:22:03.320 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-27 21:22:04.829 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-27 21:22:04.846 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:22:04.846 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 21:22:04.846 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-27 21:22:05.003 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-27 21:22:05.004 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3287 ms
2025-07-27 21:22:05.371 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@671d327e'
2025-07-27 21:22:05.578 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-27 21:22:05.617 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-27 21:22:05.658 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-27 21:22:05.709 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-27 21:22:05.761 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-27 21:22:05.779 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-27 21:22:05.794 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 21:22:05.837 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-27 21:22:05.844 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 21:22:05.850 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 21:22:05.850 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 21:22:05.852 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-27 21:22:05.905 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-27 21:22:05.943 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-27 21:22:05.972 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-27 21:22:06.023 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-27 21:22:06.066 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-27 21:22:06.100 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-27 21:22:06.124 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-27 21:22:06.152 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-27 21:22:06.187 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-27 21:22:06.212 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-27 21:22:06.242 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-27 21:22:06.279 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-27 21:22:06.318 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-27 21:22:06.343 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-27 21:22:06.393 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-27 21:22:06.423 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-27 21:22:06.457 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-27 21:22:06.500 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-27 21:22:06.845 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-27 21:22:07.017 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-27 21:22:07.029 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-27 21:22:07.162 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-27 21:22:07.189 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-27 21:22:07.749 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-27 21:22:08.702 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-27 21:22:08.758 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-27 21:22:08.940 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-27 21:22:09.090 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-27 21:22:09.353 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-27 21:22:09.624 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:22:09.683 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-27 21:22:09.686 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-27 21:22:09.694 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-27 21:22:09.812 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-27 21:22:10.281 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-27 21:22:10.542 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-27 21:22:10.735 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-27 21:22:10.739 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-27 21:22:10.742 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-27 21:22:10.744 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-27 21:22:10.746 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-27 21:22:11.332 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-27 21:22:11.350 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 10.685 seconds (JVM running for 12.65)
2025-07-27 21:23:48.560 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 21:23:48.560 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 21:23:48.564 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-27 21:23:48.781 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 18a311ab-ddf2-412c-9594-92c179fac415, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-27 21:23:48.842 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 21:23:50.503 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 21:23:50.511 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-27 21:23:50.535 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-27 21:23:50.644 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-27 21:23:50.658 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-27 21:23:50.658 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-27 21:23:50.658 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-27 21:23:50.658 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-27 21:23:50.658 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-27 21:23:50.658 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-27 21:23:50.658 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-27 21:23:50.658 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=3, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 22:25:46 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-27 21:23:50.669 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 18a311ab-ddf2-412c-9594-92c179fac415, cost: 1902ms
2025-07-27 21:23:50.943 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: e040682a-ee03-43f7-93a0-107a9cd8dffc, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-27 21:23:50.948 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-27 21:23:51.081 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-27 21:23:51.084 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-27 21:23:51.131 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-27 21:23:51.135 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-27 21:23:51.136 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-27 21:23:51.186 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=[]
2025-07-27 21:23:51.188 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-27 21:23:51.190 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:23:51.191 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:23:51.191 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:23:51.192 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:23:51.193 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:23:51.193 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:23:51.194 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 4
2025-07-27 21:23:51.194 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: e040682a-ee03-43f7-93a0-107a9cd8dffc, cost: 259ms
2025-07-27 21:23:56.033 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: dba0f59a-6439-4cf5-8832-66908ec42fea, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [4]
2025-07-27 21:23:56.037 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 7110ba7d-d76e-42ce-9e4c-5df8ffb5a42d, path: /api/users/recommend, ip: 0:0:0:0:0:0:0:1, params: [10, org.apache.catalina.connector.RequestFacade@64a8373e]
2025-07-27 21:23:56.040 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.RecommendController - 获取推荐用户 - currentUserId: 24840, limit: 10
2025-07-27 21:23:56.041 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.RecommendServiceImpl - 开始获取推荐用户 - currentUserId: 24840, limit: 10
2025-07-27 21:23:56.048 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 72b4bf99-50b2-406b-9e09-107d771a478d, path: /api/post/hot, ip: 0:0:0:0:0:0:0:1, params: [1, 4, 7, ]
2025-07-27 21:23:56.083 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-27 21:23:56.084 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 4(Integer)
2025-07-27 21:23:56.096 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-27 21:23:56.096 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 18(Long)
2025-07-27 21:23:56.109 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectHotPosts_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner, (p.like_count * 3 + p.comment_count * 2 + p.share_count * 1) AS hot_score FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY)) TOTAL
2025-07-27 21:23:56.110 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectHotPosts_mpCount - ==> Parameters: null, null, null, 7(Integer)
2025-07-27 21:23:56.134 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 4
2025-07-27 21:23:56.135 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-27 21:23:56.135 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-27 21:23:56.135 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-27 21:23:56.135 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-27 21:23:56.135 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=3, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 22:25:46 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-27 21:23:56.135 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: dba0f59a-6439-4cf5-8832-66908ec42fea, cost: 103ms
2025-07-27 21:23:56.142 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-27 21:23:56.142 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.RecommendServiceImpl - 找到推荐用户 - userId: 18, nickname: joker, avatar: /storage/default/20250409/tmp_58f8b9d60b48df63de68e7fb9726aac32dbbf20cc77c6c9254e.jpeg
2025-07-27 21:23:56.153 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-27 21:23:56.153 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 18(Long)
2025-07-27 21:23:56.158 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectHotPosts_mpCount - <==      Total: 1
2025-07-27 21:23:56.159 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectHotPosts - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner , (p.like_count * 3 + p.comment_count * 2 + p.share_count * 1) as hot_score FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY hot_score DESC, p.create_time DESC LIMIT ?
2025-07-27 21:23:56.160 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectHotPosts - ==> Parameters: null, null, null, 7(Integer), 4(Long)
2025-07-27 21:23:56.200 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-27 21:23:56.200 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.RecommendServiceImpl - 获取推荐用户完成 - 返回数量: 1
2025-07-27 21:23:56.200 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.RecommendController - 获取推荐用户成功 - 返回数量: 1
2025-07-27 21:23:56.200 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 7110ba7d-d76e-42ce-9e4c-5df8ffb5a42d, cost: 163ms
2025-07-27 21:23:56.209 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=[]
2025-07-27 21:23:56.209 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-27 21:23:56.209 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:23:56.209 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:23:56.211 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:23:56.211 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:23:56.211 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:23:56.211 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:23:56.215 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectHotPosts - <==      Total: 4
2025-07-27 21:23:56.216 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 72b4bf99-50b2-406b-9e09-107d771a478d, cost: 168ms
2025-07-27 21:23:56.241 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: ea76fe14-ce10-4fbb-9ba7-9fc1a4273048, path: /api/follow/batch-status, ip: 0:0:0:0:0:0:0:1, params: [[18], org.apache.catalina.connector.RequestFacade@76d11c3d]
2025-07-27 21:23:56.286 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.FollowMapper.selectList - ==>  Preparing: SELECT id,follower_id,following_id,status,create_time,update_time,is_delete FROM user_follows WHERE is_delete=0 AND (follower_id = ? AND following_id IN (?) AND status = ?)
2025-07-27 21:23:56.286 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.FollowMapper.selectList - ==> Parameters: 24840(Long), 18(Long), 1(Integer)
2025-07-27 21:23:56.339 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.FollowMapper.selectList - <==      Total: 1
2025-07-27 21:23:56.340 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.FollowController - 批量检查关注状态成功 - currentUserId: 24840, userCount: 1
2025-07-27 21:23:56.341 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: ea76fe14-ce10-4fbb-9ba7-9fc1a4273048, cost: 100ms
2025-07-27 21:24:00.976 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 92d15eec-6551-4d22-bb0c-6810204f925b, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [4]
2025-07-27 21:24:00.979 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: a31b7781-f092-466f-a4b5-f1bd1483d846, path: /api/users/recommend, ip: 0:0:0:0:0:0:0:1, params: [10, org.apache.catalina.connector.RequestFacade@71585185]
2025-07-27 21:24:00.980 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.RecommendController - 获取推荐用户 - currentUserId: 24840, limit: 10
2025-07-27 21:24:00.980 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.RecommendServiceImpl - 开始获取推荐用户 - currentUserId: 24840, limit: 10
2025-07-27 21:24:00.984 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 235b036c-b563-40d2-8521-470295f1428e, path: /api/post/hot, ip: 0:0:0:0:0:0:0:1, params: [1, 4, 7, ]
2025-07-27 21:24:01.023 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-27 21:24:01.024 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 4(Integer)
2025-07-27 21:24:01.024 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-27 21:24:01.025 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 18(Long)
2025-07-27 21:24:01.037 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectHotPosts_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner, (p.like_count * 3 + p.comment_count * 2 + p.share_count * 1) AS hot_score FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY)) TOTAL
2025-07-27 21:24:01.038 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectHotPosts_mpCount - ==> Parameters: null, null, null, 7(Integer)
2025-07-27 21:24:01.070 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-27 21:24:01.071 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 4
2025-07-27 21:24:01.071 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.RecommendServiceImpl - 找到推荐用户 - userId: 18, nickname: joker, avatar: /storage/default/20250409/tmp_58f8b9d60b48df63de68e7fb9726aac32dbbf20cc77c6c9254e.jpeg
2025-07-27 21:24:01.071 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-27 21:24:01.071 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-27 21:24:01.071 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-27 21:24:01.071 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-27 21:24:01.071 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=3, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 22:25:46 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-27 21:24:01.071 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-27 21:24:01.072 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 92d15eec-6551-4d22-bb0c-6810204f925b, cost: 96ms
2025-07-27 21:24:01.072 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 18(Long)
2025-07-27 21:24:01.086 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectHotPosts_mpCount - <==      Total: 1
2025-07-27 21:24:01.088 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectHotPosts - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner , (p.like_count * 3 + p.comment_count * 2 + p.share_count * 1) as hot_score FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY hot_score DESC, p.create_time DESC LIMIT ?
2025-07-27 21:24:01.090 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectHotPosts - ==> Parameters: null, null, null, 7(Integer), 4(Long)
2025-07-27 21:24:01.118 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-27 21:24:01.119 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.RecommendServiceImpl - 获取推荐用户完成 - 返回数量: 1
2025-07-27 21:24:01.119 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.RecommendController - 获取推荐用户成功 - 返回数量: 1
2025-07-27 21:24:01.119 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: a31b7781-f092-466f-a4b5-f1bd1483d846, cost: 139ms
2025-07-27 21:24:01.137 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=[]
2025-07-27 21:24:01.138 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-27 21:24:01.139 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:24:01.139 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:24:01.140 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:24:01.140 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:24:01.141 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:24:01.141 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:24:01.141 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectHotPosts - <==      Total: 4
2025-07-27 21:24:01.141 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 235b036c-b563-40d2-8521-470295f1428e, cost: 156ms
2025-07-27 21:24:01.174 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: bb1da9b1-43a5-4499-8b71-53f19c4e8a4a, path: /api/follow/batch-status, ip: 0:0:0:0:0:0:0:1, params: [[18], org.apache.catalina.connector.RequestFacade@76d11c3d]
2025-07-27 21:24:01.176 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.FollowMapper.selectList - ==>  Preparing: SELECT id,follower_id,following_id,status,create_time,update_time,is_delete FROM user_follows WHERE is_delete=0 AND (follower_id = ? AND following_id IN (?) AND status = ?)
2025-07-27 21:24:01.177 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.FollowMapper.selectList - ==> Parameters: 24840(Long), 18(Long), 1(Integer)
2025-07-27 21:24:01.228 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.FollowMapper.selectList - <==      Total: 1
2025-07-27 21:24:01.229 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.FollowController - 批量检查关注状态成功 - currentUserId: 24840, userCount: 1
2025-07-27 21:24:01.229 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: bb1da9b1-43a5-4499-8b71-53f19c4e8a4a, cost: 55ms
2025-07-27 21:24:01.347 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 2f72cfeb-fe45-4ada-b853-5b6c1e7496e2, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@76d11c3d]
2025-07-27 21:24:01.375 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-27 21:24:01.380 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-27 21:24:01.382 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-27 21:24:01.428 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-27 21:24:01.429 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-27 21:24:01.430 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-27 21:24:01.475 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-27 21:24:01.475 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-27 21:24:01.476 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-27 21:24:01.476 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-27 21:24:01.476 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 2f72cfeb-fe45-4ada-b853-5b6c1e7496e2, cost: 128ms
2025-07-27 21:24:03.195 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 8935adc5-f45b-471d-830b-19e01953e574, path: /api/user/profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-27 21:24:03.244 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-27 21:24:03.244 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-27 21:24:03.291 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-27 21:24:03.291 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-27 21:24:03.292 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-27 21:24:03.337 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-27 21:24:03.337 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.c.UserProfileController - 获取用户详情成功 - userId: 24840
2025-07-27 21:24:03.337 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 8935adc5-f45b-471d-830b-19e01953e574, cost: 142ms
2025-07-27 21:24:03.353 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 85ba94a3-1ead-45c7-a624-3cb8323f0c19, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=0, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=1)]
2025-07-27 21:24:03.400 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-27 21:24:03.402 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-27 21:24:03.448 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-27 21:24:03.448 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-27 21:24:03.452 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-27 21:24:03.453 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 0(Integer), 24840(Long)
2025-07-27 21:24:03.501 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-27 21:24:03.501 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 85ba94a3-1ead-45c7-a624-3cb8323f0c19, cost: 147ms
2025-07-27 21:24:03.515 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: b454875b-9a07-4119-958d-277dc357e9e8, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=1, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=20)]
2025-07-27 21:24:03.516 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-27 21:24:03.516 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-27 21:24:03.565 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-27 21:24:03.566 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-27 21:24:03.570 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-27 21:24:03.570 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long)
2025-07-27 21:24:03.618 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-27 21:24:03.619 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ? ORDER BY p.create_time desc LIMIT ?
2025-07-27 21:24:03.619 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long), 20(Long)
2025-07-27 21:24:03.667 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=[]
2025-07-27 21:24:03.668 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-27 21:24:03.668 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:24:03.668 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:24:03.668 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:24:03.668 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:24:03.670 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:24:03.670 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:24:03.671 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 4
2025-07-27 21:24:03.671 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: b454875b-9a07-4119-958d-277dc357e9e8, cost: 156ms
2025-07-27 21:28:08.999 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 3ec48848-5586-45eb-b579-14645a0b6699, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-27 21:28:09.048 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-27 21:28:09.049 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-27 21:28:09.099 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-27 21:28:09.099 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-27 21:28:09.099 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-27 21:28:09.100 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-27 21:28:09.100 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-27 21:28:09.100 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-27 21:28:09.100 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-27 21:28:09.100 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-27 21:28:09.100 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=3, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 22:25:46 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-27 21:28:09.101 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 3ec48848-5586-45eb-b579-14645a0b6699, cost: 101ms
2025-07-27 21:28:09.147 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: ac0d52b1-0ab9-4214-8478-c2d0c4f71b5f, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-27 21:28:09.147 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-27 21:28:09.197 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-27 21:28:09.198 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-27 21:28:09.505 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-27 21:28:09.506 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-27 21:28:09.506 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-27 21:28:09.553 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=[]
2025-07-27 21:28:09.554 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-27 21:28:09.554 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:28:09.554 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:28:09.555 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:28:09.555 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:28:09.555 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:28:09.555 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:28:09.555 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 4
2025-07-27 21:28:09.556 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: ac0d52b1-0ab9-4214-8478-c2d0c4f71b5f, cost: 408ms
2025-07-27 21:28:10.076 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 05016d7d-b73e-453a-accf-49a724dbaa01, path: /api/user/profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-27 21:28:10.124 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-27 21:28:10.124 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-27 21:28:10.172 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-27 21:28:10.173 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-27 21:28:10.173 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-27 21:28:10.220 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-27 21:28:10.220 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.c.UserProfileController - 获取用户详情成功 - userId: 24840
2025-07-27 21:28:10.220 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 05016d7d-b73e-453a-accf-49a724dbaa01, cost: 144ms
2025-07-27 21:28:10.236 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 0430b107-119a-4923-9edd-329f271aa479, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=0, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=1)]
2025-07-27 21:28:10.280 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-27 21:28:10.280 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-27 21:28:10.325 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-27 21:28:10.325 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-27 21:28:10.329 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-27 21:28:10.330 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 0(Integer), 24840(Long)
2025-07-27 21:28:10.375 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-27 21:28:10.375 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 0430b107-119a-4923-9edd-329f271aa479, cost: 139ms
2025-07-27 21:28:10.394 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: a7d48e07-6828-4dc8-bec7-2e952ba86434, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=1, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=20)]
2025-07-27 21:28:10.440 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-27 21:28:10.440 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-27 21:28:10.488 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-27 21:28:10.488 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-27 21:28:10.492 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-27 21:28:10.493 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long)
2025-07-27 21:28:10.539 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-27 21:28:10.540 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ? ORDER BY p.create_time desc LIMIT ?
2025-07-27 21:28:10.541 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long), 20(Long)
2025-07-27 21:28:10.590 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=[]
2025-07-27 21:28:10.591 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-27 21:28:10.591 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:28:10.591 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:28:10.592 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:28:10.592 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:28:10.592 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:28:10.592 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:28:10.592 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 4
2025-07-27 21:28:10.592 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: a7d48e07-6828-4dc8-bec7-2e952ba86434, cost: 198ms
2025-07-27 21:36:11.388 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 21:36:11.398 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 21:36:11.519 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 23720 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-27 21:36:11.520 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:12.104 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-27 21:36:12.257 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-27 21:36:12.257 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:36:12.257 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 21:36:12.257 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-27 21:36:12.272 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-27 21:36:12.272 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 749 ms
2025-07-27 21:36:12.356 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@fc5162e'
2025-07-27 21:36:12.372 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-27 21:36:12.388 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-27 21:36:12.388 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-27 21:36:12.419 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-27 21:36:12.419 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-27 21:36:12.435 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-27 21:36:12.435 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 21:36:12.435 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-27 21:36:12.435 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 21:36:12.435 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 21:36:12.435 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 21:36:12.435 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-27 21:36:12.450 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-27 21:36:12.466 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-27 21:36:12.482 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-27 21:36:12.511 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-27 21:36:12.525 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-27 21:36:12.541 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-27 21:36:12.556 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-27 21:36:12.592 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-27 21:36:12.612 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-27 21:36:12.622 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-27 21:36:12.660 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-27 21:36:12.678 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-27 21:36:12.691 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-27 21:36:12.706 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-27 21:36:12.726 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-27 21:36:12.741 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-27 21:36:12.757 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-27 21:36:12.793 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-27 21:36:12.911 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-27 21:36:12.959 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-27 21:36:12.965 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-27 21:36:13.022 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-27 21:36:13.042 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-27 21:36:13.331 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-27 21:36:14.067 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-27 21:36:14.108 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-27 21:36:14.194 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-27 21:36:14.264 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-27 21:36:14.344 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-27 21:36:14.435 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:36:14.435 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-27 21:36:14.435 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-27 21:36:14.435 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-27 21:36:14.466 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-27 21:36:14.498 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-27 21:36:14.583 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-27 21:36:14.671 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-27 21:36:14.672 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-27 21:36:14.673 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-27 21:36:14.675 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-27 21:36:14.677 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-27 21:36:14.850 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-27 21:36:14.854 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 3.377 seconds (JVM running for 856.162)
2025-07-27 21:36:14.857 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-27 21:36:28.656 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 23720 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-27 21:36:28.656 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-27 21:36:29.048 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:36:29.049 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-27 21:36:29.166 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-27 21:36:29.166 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:36:29.166 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 21:36:29.166 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-27 21:36:29.190 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-27 21:36:29.190 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 532 ms
2025-07-27 21:36:29.272 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@7f64e741'
2025-07-27 21:36:29.298 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-27 21:36:29.310 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-27 21:36:29.322 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-27 21:36:29.343 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-27 21:36:29.363 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-27 21:36:29.382 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-27 21:36:29.386 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 21:36:29.411 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-27 21:36:29.414 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 21:36:29.416 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 21:36:29.416 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 21:36:29.418 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-27 21:36:29.452 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-27 21:36:29.481 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-27 21:36:29.504 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-27 21:36:29.539 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-27 21:36:29.556 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-27 21:36:29.573 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-27 21:36:29.593 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-27 21:36:29.618 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-27 21:36:29.656 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-27 21:36:29.672 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-27 21:36:29.687 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-27 21:36:29.704 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-27 21:36:29.704 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-27 21:36:29.740 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-27 21:36:29.754 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-27 21:36:29.768 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-27 21:36:29.779 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-27 21:36:29.790 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-27 21:36:29.892 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-27 21:36:29.945 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-27 21:36:29.953 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-27 21:36:30.036 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-27 21:36:30.044 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-27 21:36:30.262 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-27 21:36:31.193 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-27 21:36:31.232 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-27 21:36:31.330 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-27 21:36:31.384 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-27 21:36:31.437 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-27 21:36:31.512 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:36:31.523 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-27 21:36:31.523 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-27 21:36:31.524 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-27 21:36:31.551 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-27 21:36:31.612 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-27 21:36:31.667 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-27 21:36:31.710 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-27 21:36:31.710 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-27 21:36:31.710 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-27 21:36:31.714 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-27 21:36:31.715 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-27 21:36:31.854 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-27 21:36:31.854 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 3.239 seconds (JVM running for 873.168)
2025-07-27 21:36:31.860 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-27 21:43:52.675 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 21:43:52.675 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 21:43:52.677 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-27 21:43:52.684 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 2a8c4539-491d-4d80-a9f2-ed6a3517b500, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-27 21:43:52.698 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-27 21:43:53.193 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-27 21:43:53.193 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-27 21:43:53.193 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-27 21:43:53.300 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-27 21:43:53.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-27 21:43:53.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-27 21:43:53.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-27 21:43:53.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-27 21:43:53.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-27 21:43:53.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-27 21:43:53.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-27 21:43:53.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=3, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 22:25:46 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-27 21:43:53.307 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 2a8c4539-491d-4d80-a9f2-ed6a3517b500, cost: 625ms
2025-07-27 21:43:53.350 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 5c7aca6e-b7bc-4bda-8576-2dc47c7664d5, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-27 21:43:53.353 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-27 21:43:53.366 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-27 21:43:53.367 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-27 21:43:53.415 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-27 21:43:53.416 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-27 21:43:53.416 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-27 21:43:53.469 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=[]
2025-07-27 21:43:53.470 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-27 21:43:53.470 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:43:53.470 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:43:53.470 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:43:53.470 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:43:53.471 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-27 21:43:53.471 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-27 21:43:53.471 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 4
2025-07-27 21:43:53.471 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 5c7aca6e-b7bc-4bda-8576-2dc47c7664d5, cost: 128ms
2025-07-27 21:44:15.040 [Thread-13] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2025-07-27 21:44:15.042 [Thread-13] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2025-07-27 21:44:15.239 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 23720 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-27 21:44:15.239 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.884 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:15.885 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-27 21:44:16.043 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-27 21:44:16.044 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:44:16.044 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 21:44:16.044 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-27 21:44:16.074 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-27 21:44:16.074 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 831 ms
2025-07-27 21:44:16.241 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@77959e25'
2025-07-27 21:44:16.274 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-27 21:44:16.281 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'authInterceptor': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\mapper\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
2025-07-27 21:44:16.282 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 21:44:16.292 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 21:44:16.418 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'authInterceptor': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\mapper\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\mapper\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:544)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 22 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\mapper\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1534)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 41 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	... 52 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 65 common frames omitted
Caused by: org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:576)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:445)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:609)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:218)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 66 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:95)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:574)
	... 74 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:118)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:263)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:254)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:246)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:119)
	... 76 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:120)
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:149)
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:116)
	... 80 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:196)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:89)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:261)
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:116)
	... 82 common frames omitted
2025-07-27 21:44:18.053 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 23720 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-27 21:44:18.053 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.738 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:18.739 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-27 21:44:18.902 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-27 21:44:18.902 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:44:18.903 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 21:44:18.903 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-27 21:44:18.932 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-27 21:44:18.932 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 875 ms
2025-07-27 21:44:19.028 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@14b73bf5'
2025-07-27 21:44:19.047 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-27 21:44:19.051 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'authInterceptor': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\mapper\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
2025-07-27 21:44:19.052 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 21:44:19.057 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 21:44:19.062 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'authInterceptor': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\mapper\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\mapper\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:544)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 22 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userMapper' defined in file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\com\yupi\springbootinit\mapper\UserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1534)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 41 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	... 52 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 65 common frames omitted
Caused by: org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:576)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:445)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:609)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:218)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 66 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:95)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:574)
	... 74 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:118)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:263)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:254)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:246)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:119)
	... 76 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.yupi.springbootinit.model.entity.BaUser'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:120)
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:149)
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:116)
	... 80 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.yupi.springbootinit.model.entity.BaUser
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:196)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:89)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:261)
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:116)
	... 82 common frames omitted
2025-07-27 21:44:21.660 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 23720 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-27 21:44:21.661 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.063 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.063 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.063 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-27 21:44:22.063 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-27 21:44:22.184 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-27 21:44:22.185 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:44:22.185 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 21:44:22.185 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-27 21:44:22.215 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-27 21:44:22.215 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 552 ms
2025-07-27 21:44:22.285 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@40626c6c'
2025-07-27 21:44:22.301 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-27 21:44:22.312 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-27 21:44:22.323 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-27 21:44:22.342 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-27 21:44:22.353 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-27 21:44:22.358 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-27 21:44:22.359 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 21:44:22.363 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-27 21:44:22.364 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 21:44:22.365 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 21:44:22.365 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 21:44:22.365 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-27 21:44:22.377 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-27 21:44:22.389 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-27 21:44:22.398 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-27 21:44:22.411 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-27 21:44:22.421 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-27 21:44:22.430 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-27 21:44:22.448 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-27 21:44:22.463 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-27 21:44:22.477 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-27 21:44:22.509 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-27 21:44:22.522 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-27 21:44:22.534 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-27 21:44:22.545 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-27 21:44:22.556 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-27 21:44:22.572 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-27 21:44:22.582 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-27 21:44:22.596 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-27 21:44:22.607 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-27 21:44:22.754 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-27 21:44:22.851 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-27 21:44:22.860 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-27 21:44:22.956 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-27 21:44:22.967 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-27 21:44:23.269 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-27 21:44:24.093 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-27 21:44:24.122 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-27 21:44:24.181 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-27 21:44:24.230 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-27 21:44:24.284 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-27 21:44:24.356 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-27 21:44:24.359 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-27 21:44:24.360 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-27 21:44:24.360 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-27 21:44:24.380 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-27 21:44:24.411 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-27 21:44:24.456 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-27 21:44:24.488 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-27 21:44:24.488 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-27 21:44:24.489 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-27 21:44:24.490 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-27 21:44:24.491 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-27 21:44:24.598 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-27 21:44:24.599 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.965 seconds (JVM running for 1345.912)
2025-07-27 21:44:24.600 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
