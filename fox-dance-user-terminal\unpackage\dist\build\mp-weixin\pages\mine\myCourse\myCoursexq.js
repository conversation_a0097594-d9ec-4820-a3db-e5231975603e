(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/myCourse/myCoursexq"],{"0091":function(e,t,i){},7260:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){}));var o=function(){var e=this,t=e.$createElement,i=(e._self._c,e.courseDetail.id?e.courseDetail.videoFileList.length:null),o=e.courseDetail.id?""==e.courseDetail.music_link&&0==e.courseDetail.reviewVideoFileList.length:null;e._isMounted||(e.e0=function(t){e.yyToggle=!0},e.e1=function(t){e.yyToggle=!0},e.e2=function(t){t.stopPropagation(),e.ljtkToggle=!0},e.e3=function(t){e.yyToggle=!1},e.e4=function(t){e.ljtkToggle=!1}),e.$mp.data=Object.assign({},{$root:{g0:i,g1:o}})},n=[]},"854f":function(e,t,i){"use strict";i.r(t);var o=i("a08a"),n=i.n(o);for(var s in o)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(s);t["default"]=n.a},a08a:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i("6061"),n={data:function(){return{isLogined:!0,navLists:["全部","等位中","待开课","授课中","已完成"],courseDetail:{id:0},kcId:0,imgbaseUrl:"",yyToggle:!1,controlsToggle:!1,speedState:!1,speedNum:!1,speedRate:0,controlsToggle_tc:!1,speedState_tc:!1,speedNum_tc:!1,speedRate_tc:0,ljtkToggle:!1,qjbutton:"#131315",type:0,imgbaseUrlOss:"",appointmentrecordid:0}},onShow:function(){this.imgbaseUrlOss=this.$baseUrlOss},onLoad:function(t){this.appointmentrecordid=t.appointmentrecordid?t.appointmentrecordid:0,this.type=t.type?t.type:0,this.qjbutton=e.getStorageSync("storeInfo").button,this.imgbaseUrl=this.$baseUrl,this.kcId=t.id,this.courseData(),e.setStorageSync("schedulesx",1)},methods:{bfTap:function(e){console.log(e),this.courseDetail.reviewVideoFileList[e].toggleVideo=!0},speedTap:function(e){this.courseDetail.videoFileList[e].toggle=!this.courseDetail.videoFileList[e].toggle},handleFullScreen:function(e){this.speedState=e.detail.fullScreen,this.speedNum=!1},handleControlstoggle:function(e){this.controlsToggle=e.detail.show},handleSetSpeedRate:function(t,i){var o=e.createVideoContext("videoId_"+i);o.playbackRate(t),this.speedRate=t,this.speedNum=!1,this.courseDetail.videoFileList[i].toggle=!1,e.showToast({icon:"none",title:"已切换至"+t+"倍数",duration:2e3})},speedTap_tc:function(e){this.speedNum_tc=!0,this.courseDetail.reviewVideoFileList[e].toggle=!this.courseDetail.reviewVideoFileList[e].toggle},handleFullScreen_tc:function(e){this.speedState_tc=e.detail.fullScreen,this.speedNum_tc=!1},handleControlstoggle_tc:function(e){this.controlsToggle_tc=e.detail.show},handleSetSpeedRate_tc:function(t,i){var o=e.createVideoContext("videoId_tc_"+i);o.playbackRate(t),this.speedRate_tc=t,this.speedNum_tc=!1,e.showToast({icon:"none",title:"已切换至"+t+"倍数",duration:2e3}),this.courseDetail.reviewVideoFileList[i].toggle=!1},qxyySubTap:function(){var t=this;e.showLoading({title:"加载中"}),(0,o.cancelCourseApi)({id:t.courseDetail.appointment_record_id}).then((function(i){console.log("取消预约提交",i),1==i.code?(e.hideLoading(),t.yyToggle=!1,e.showToast({icon:"success",title:"取消成功",duration:2e3}),t.courseData()):t.yyToggle=!1}))},courseData:function(){var t=this;e.showLoading({title:"加载中"}),(0,o.myCourseXqApi)({id:t.kcId,type:t.type,AppointmentRecordId:t.appointmentrecordid}).then((function(i){if(console.log("课程详情1",i),1==i.code){e.hideLoading();for(var o=0;o<i.data.videoFileList.length;o++)i.data.videoFileList[o].toggle=!1;for(var n=0;n<i.data.reviewVideoFileList.length;n++)i.data.reviewVideoFileList[n].toggle=!1,i.data.reviewVideoFileList[n].toggleVideo=!1;t.courseDetail=i.data}}))},homeTap:function(){e.switchTab({url:"/pages/index/index"})},dhTap:function(){e.openLocation({name:this.courseDetail.store.address,latitude:1*this.courseDetail.store.latitude,longitude:1*this.courseDetail.store.longitude,success:function(){console.log("success")}})},yypdTo:function(t){return this.isLogined?(console.log(t,"item"),0==t.member?(this.ljtkToggle=!0,!1):void e.navigateTo({url:"/pages/Schedule/confirmOrder?id="+t.id+"&storeid="+t.store.id})):(e.showToast({icon:"none",title:"请先登录"}),setTimeout((function(){e.navigateTo({url:"/pages/login/login"})}),1e3),!1)},kqhyts:function(){e.showToast({title:"预约课程已满",icon:"none",duration:1e3})},ljktTap:function(){this.ljtkToggle=!1,e.switchTab({url:"/pages/buy/buy"})},navTo:function(t){e.navigateTo({url:t})},downloadFile:function(t){if(""==t)return e.showToast({title:"暂无可下载的附件",icon:"none"}),!1;var i=t.split(".").pop().toLowerCase();["jpg","jpeg","png","gif","bmp"].includes(i),["mp4","mov","avi","m4v"].includes(i);e.showLoading({title:"下载中..."});var o=this;e.downloadFile({url:t,success:function(e){if(200===e.statusCode){var t=e.tempFilePath;o.saveVideo(t)}},fail:function(t){e.showToast({title:"下载失败",icon:"none"})},complete:function(){e.hideLoading()}})},saveVideo:function(t){e.saveVideoToPhotosAlbum({filePath:t,success:function(){e.showToast({title:"视频已保存到相册"})},fail:function(e){}})}}};t.default=n}).call(this,i("df3c")["default"])},e2cd:function(e,t,i){"use strict";(function(e,t){var o=i("47a9");i("cff9");o(i("3240"));var n=o(i("ffa9"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(n.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},e34a:function(e,t,i){"use strict";var o=i("0091"),n=i.n(o);n.a},ffa9:function(e,t,i){"use strict";i.r(t);var o=i("7260"),n=i("854f");for(var s in n)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(s);i("e34a");var a=i("828b"),l=Object(a["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=l.exports}},[["e2cd","common/runtime","common/vendor"]]]);