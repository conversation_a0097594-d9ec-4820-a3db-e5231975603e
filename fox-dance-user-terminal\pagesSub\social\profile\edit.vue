<template>
  <view class="edit-profile-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 编辑内容 -->
    <scroll-view v-else class="content" scroll-y>
      <!-- 头像编辑 -->
      <view class="edit-section">
        <view class="edit-item">
          <text class="edit-label">头像</text>
          <view class="avatar-edit" @click="editAvatar">
            <u-avatar :src="userInfo.avatar" size="80"></u-avatar>
            <u-icon name="camera" color="#999" size="32rpx" class="camera-icon"></u-icon>
          </view>
        </view>
      </view>

      <!-- 基本信息编辑 -->
      <view class="edit-section">
        <view class="edit-item">
          <text class="edit-label">昵称</text>
          <u-input v-model="userInfo.nickname" placeholder="请输入昵称" border="none" class="edit-input"></u-input>
        </view>

        <view class="edit-item">
          <text class="edit-label">社区ID</text>
          <u-input v-model="userInfo.socialId" placeholder="请输入社区ID" border="none" class="edit-input"></u-input>
        </view>

        <view class="edit-item">
          <text class="edit-label">舞种</text>
          <u-input
            v-model="userInfo.danceType"
            placeholder="请输入舞种"
            border="none"
            class="edit-input"
          ></u-input>
        </view>

        <view class="edit-item">
          <text class="edit-label">个人简介</text>
          <u-input
            v-model="userInfo.bio"
            type="textarea"
            placeholder="请输入个人简介"
            border="none"
            class="edit-textarea"
            :autoHeight="true"
          ></u-input>
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="button-section">
        <u-button
          type="primary"
          @click="saveProfile"
          class="save-button"
          :loading="saving"
          :disabled="saving"
        >{{ saving ? '保存中...' : '保存' }}</u-button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getUserProfile, updateUserProfile } from "@/utils/socialApi.js";

export default {
  name: "EditProfile",
  data() {
    return {
      userInfo: {
        userId: "",
        nickname: "",
        avatar: "",
        bio: "",
        danceType: ""
      },
      loading: false,
      saving: false
    };
  },
  onLoad() {
    this.loadUserInfo();
  },
  methods: {
    async loadUserInfo() {
      try {
        // 获取当前用户ID
        const currentUserId = this.getCurrentUserId();
        if (!currentUserId) {
          console.error("用户未登录，无法加载用户信息");
          uni.showToast({
            title: "请先登录",
            icon: "none"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
          return;
        }

        console.log("开始加载用户信息进行编辑 - userId:", currentUserId);
        this.loading = true;

        const result = await getUserProfile(currentUserId);
        console.log("用户信息API返回:", result);

        if (result && result.code === 0 && result.data) {
          const userProfile = result.data;
          this.userInfo = {
            userId: userProfile.id || userProfile.userId || currentUserId,
            nickname: userProfile.nickname || "",
            avatar: this.formatAvatarUrl(userProfile.avatar),
            bio: userProfile.bio || userProfile.userProfile || "",
            danceType: userProfile.danceType || ""
          };

          console.log("用户信息加载成功:", this.userInfo);
        } else {
          console.error("用户信息API返回格式不正确:", result);
          uni.showToast({
            title: "加载用户信息失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("加载用户信息失败:", error);
        uni.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取当前用户ID
    getCurrentUserId() {
      const userId = uni.getStorageSync("userid");
      return userId ? Number(userId) : null;
    },

    // 格式化头像URL
    formatAvatarUrl(avatar) {
      if (avatar) {
        return (
          "https://file.foxdance.com.cn" +
          avatar +
          "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85"
        );
      }
      return "static/images/toux.png";
    },

    goBack() {
      uni.navigateBack();
    },

    editAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: res => {
          // 上传头像
          this.userInfo.avatar = res.tempFilePaths[0];
        }
      });
    },

    async saveProfile() {
      try {
        // 验证必填字段
        if (!this.userInfo.nickname || !this.userInfo.nickname.trim()) {
          uni.showToast({
            title: "请输入昵称",
            icon: "none"
          });
          return;
        }

        if (this.userInfo.nickname.length > 20) {
          uni.showToast({
            title: "昵称不能超过20个字符",
            icon: "none"
          });
          return;
        }

        if (this.userInfo.bio && this.userInfo.bio.length > 200) {
          uni.showToast({
            title: "个人简介不能超过200个字符",
            icon: "none"
          });
          return;
        }

        console.log("开始保存用户资料:", this.userInfo);
        this.saving = true;

        // 准备更新数据
        const updateData = {
          nickname: this.userInfo.nickname.trim(),
          socialId: this.userInfo.socialId ? this.userInfo.socialId.trim() : "",
          bio: this.userInfo.bio ? this.userInfo.bio.trim() : "",
          danceType: this.userInfo.danceType
            ? this.userInfo.danceType.trim()
            : ""
        };

        // 如果头像有变化，也包含头像
        if (
          this.userInfo.avatar &&
          !this.userInfo.avatar.includes("/static/images/")
        ) {
          updateData.avatar = this.userInfo.avatar;
        }

        const result = await updateUserProfile(updateData);
        console.log("更新用户资料API返回:", result);

        if (result && result.code === 0) {
          uni.showToast({
            title: "保存成功",
            icon: "success"
          });

          // 延迟返回，让用户看到成功提示
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          console.error("保存用户资料失败:", result);
          uni.showToast({
            title: result?.message || "保存失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("保存用户资料异常:", error);
        uni.showToast({
          title: "保存失败，请重试",
          icon: "none"
        });
      } finally {
        this.saving = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.edit-profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-top: 30rpx;
}

.content {
  flex: 1;
}

.edit-section {
  background: #fff;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.edit-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.edit-item:last-child {
  border-bottom: none;
}

.edit-label {
  width: 160rpx;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.avatar-edit {
  position: relative;
  display: flex;
  align-items: center;
}

.camera-icon {
  position: absolute;
  bottom: 0;
  right: 0;
  background: #fff;
  border-radius: 50%;
  padding: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.edit-input {
  flex: 1;
  margin-left: 24rpx;
}

.edit-textarea {
  flex: 1;
  margin-left: 24rpx;
  min-height: 120rpx;
}

.button-section {
  padding: 40rpx 32rpx 80rpx;
}

.save-button {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* Loading 相关样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
</style>
