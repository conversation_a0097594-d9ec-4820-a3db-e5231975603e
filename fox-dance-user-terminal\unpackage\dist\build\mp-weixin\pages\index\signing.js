(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/signing"],{"23a8":function(n,t,o){"use strict";o.r(t);var i=o("caa2"),e=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){o.d(t,n,(function(){return i[n]}))}(a);t["default"]=e.a},"2a69":function(n,t,o){"use strict";var i=o("a9f1"),e=o.n(i);e.a},8233:function(n,t,o){"use strict";o.r(t);var i=o("9f7f"),e=o("23a8");for(var a in e)["default"].indexOf(a)<0&&function(n){o.d(t,n,(function(){return e[n]}))}(a);o("2a69");var c=o("828b"),u=Object(c["a"])(e["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=u.exports},"9f7f":function(n,t,o){"use strict";o.d(t,"b",(function(){return e})),o.d(t,"c",(function(){return a})),o.d(t,"a",(function(){return i}));var i={jpSignaturePopup:function(){return o.e("uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup").then(o.bind(null,"9e1f"))}},e=function(){var n=this.$createElement;this._self._c},a=[]},a9f1:function(n,t,o){},b88f:function(n,t,o){"use strict";(function(n,t){var i=o("47a9");o("cff9");i(o("3240"));var e=i(o("8233"));n.__webpack_require_UNI_MP_PLUGIN__=o,t(e.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},caa2:function(n,t,o){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o("6061"),e=o("9169"),a={data:function(){return{isLogined:!0,imgbaseUrl:"",image2:"",url:"",htImgsrc:"",htId:0}},onShow:function(){},onLoad:function(n){console.log("options12",n,e.apis),this.imgbaseUrl=this.$baseUrl_ht,this.htId=n.id?n.id:0,this.htconData()},methods:{cesTap:function(){n.showModal({title:"提示",content:"查询到您有未签署的合同，请点击下一步继续签署",showCancel:!1,confirmText:"下一步",success:function(t){t.confirm?(console.log("用户点击确定"),n.reLaunch({url:"/pages/index/signing?id=23"})):t.cancel&&console.log("用户点击取消")}})},htconData:function(){n.showLoading({title:"生成合同中"});var t=this;(0,i.hqhtnrApi)({id:t.htId}).then((function(o){1==o.code&&(n.hideLoading(),t.htImgsrc=o.data)}))},qmTap:function(){if(""==this.image2)return n.showToast({icon:"none",title:"请先进行签字",duration:2e3}),!1;this.sctxTap(this.image2)},toPop1:function(){this.$refs.signature1.toPop()},sctxTap:function(t){console.log(t,"tempFilePaths");var o=this;n.showLoading({title:"加载中"}),(0,i.upImg)(t,"file").then((function(t){console.log("上传图片",t),1==t.code&&(n.hideLoading(),o.qshtSub(t.data.file.url))}))},qshtSub:function(t){n.showLoading({title:"加载中"});var o=this;(0,i.signContractApi)({id:o.htId,image:t}).then((function(t){1==t.code&&(n.hideLoading(),o.getContractData(t.data),n.showToast({icon:"success",title:"签署成功",duration:2e3}))}))},getContractData:function(t){(0,i.getContractApi)({}).then((function(o){console.log("获取未签署的合同",o),1==o.code&&(o.data?n.showModal({title:"提示",content:"查询到您还有"+t+"份未签署的合同，请点击下一步继续签署",showCancel:!1,confirmText:"下一步",success:function(t){t.confirm?(console.log("用户点击确定"),n.reLaunch({url:"/pages/index/signing?id="+o.data})):t.cancel&&console.log("用户点击取消")}}):setTimeout((function(){n.switchTab({url:"/pages/index/index"})}),1500))}))},navTo:function(t){n.navigateTo({url:t})}}};t.default=a}).call(this,o("df3c")["default"])}},[["b88f","common/runtime","common/vendor"]]]);