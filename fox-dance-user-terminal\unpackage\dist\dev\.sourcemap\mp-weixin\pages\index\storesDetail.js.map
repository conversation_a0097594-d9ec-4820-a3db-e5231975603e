{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/index/storesDetail.vue?c292", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/index/storesDetail.vue?1881", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/index/storesDetail.vue?0857", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/index/storesDetail.vue?f985", "uni-app:///pages/index/storesDetail.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/index/storesDetail.vue?8b39", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/index/storesDetail.vue?210d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "loding", "kcjsDetail", "imgbaseUrl", "imgbaseUrlOss", "jibLists", "jibIndex", "jibText", "jb<PERSON><PERSON><PERSON>", "wuzLists", "wuzIndex", "wuzText", "wuzToggle", "laosLists", "laosIndex", "laosText", "laosToggle", "storeInfo", "storeCourseLists", "page", "total_pages", "zanwsj", "status", "loadingText", "loadmoreText", "nomoreText", "storeId", "ljtkToggle", "qj<PERSON>ton", "<PERSON><PERSON><PERSON><PERSON>", "onShow", "onLoad", "methods", "storesxqTap", "console", "uni", "icon", "title", "setTimeout", "url", "yypdTo", "ljktTap", "dhTap", "name", "latitude", "longitude", "success", "kqhyts", "duration", "storeCourseData", "id", "level_id", "dance_id", "teacher_id", "that", "onReachBottom", "onPullDownRefresh", "storeDetail", "res", "categoryData", "gbTcTap", "jbStartTap", "jibTap", "jibSubTap", "jib<PERSON><PERSON><PERSON>", "wuzStartTap", "wuzTap", "wuzSubTap", "wuzReact", "laosStartTap", "laosTap", "laosSubTap", "laosReact", "openImg", "arr", "current", "urls", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AAC4L;AAC5L,gBAAgB,6LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAsuB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2H1vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACA;QACAC;UACAC;UACAC;QACA;QACAC;UACAH;YACAI;UACA;QACA;QACA;MACA;MACA;MACA;QACA;MACA;QACAJ;UACA;UACAI;QACA;MACA;IACA;IACA;IACAC;MACA;QACAL;UACAC;UACAC;QACA;QACAC;UACAH;YACAI;UACA;QACA;QACA;MACA;MACA;MACA;QACA;QACA;MACA;MACAJ;QACAI;MACA;IACA;IACA;IACAE;MACA;MACAN;QACAI;MACA;IACA;IACA;IACAG;MACA;MACAP;QACAQ;QACAC;QACAC;QACAC;UACAZ;QACA;MACA;IACA;IACA;IACAa;MACAZ;QACAE;QACAD;QACAY;MACA;IACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAC;MAEA;MACAd;QACAE;MACA;MACA;QACAlB;QACA+B;QACAC;QACAC;QACAC;MACA;QACAnB;QACA;AACA;AACA;AACA;QACA;UACA;UACAoB;UACAA;UACAA;UACA;UACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UACA;YACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAA;UACAnB;UACAA;QACA;MACA;IACA;IACAoB;MACA;MACA;QACA;UACA;QACA;MACA;IACA;;IACAC;MACAtB;MACA;MACA;MACA;IACA;IACA;IACAuB;MACAtB;QACAE;MACA;MACA;MACA;QACAQ;QACAD;QACAM;MACA;QACAhB;QACA;UACAC;UACAmB;UACA;UACA;YACAI;UACA;UACA;UACAJ;QACA;MACA;IACA;IACA;IACAK;MACA;MACA;QACAzB;QACA;UACAoB;UACAA;UACAA;QACA;MACA;IACA;IACA;IACAM;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACAxC;MACAC;QACAwC;QACAC;MACA;IACA;IACAC;MACA1C;QACAI;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC/dA;AAAA;AAAA;AAAA;AAAq3C,CAAgB,qvCAAG,EAAC,C;;;;;;;;;;;ACAz4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/storesDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/storesDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./storesDetail.vue?vue&type=template&id=72619e0e&\"\nvar renderjs\nimport script from \"./storesDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./storesDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./storesDetail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/storesDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./storesDetail.vue?vue&type=template&id=72619e0e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loding ? _vm.storeCourseLists.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.ljtkToggle = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.ljtkToggle = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./storesDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./storesDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"storesDetail\"  :style=\"{ '--qjbutton-color': qjbutton,'--qjziti-color': qjziti }\" v-if=\"loding\">\r\n\t\t\r\n\t\t<view class=\"stor_one\">\r\n\t\t\t<view class=\"stor_one_t\">\r\n\t\t\t\t<image :src=\"imgbaseUrl + storeInfo.image\" mode=\"aspectFill\" class=\"stor_one_t_l\"></image>\r\n\t\t\t\t<view class=\"stor_one_t_r\">\r\n\t\t\t\t\t<view class=\"stor_one_t_r_a\">{{storeInfo.name}}</view>\r\n\t\t\t\t\t<view class=\"stor_one_t_r_b\">{{storeInfo.introduce}}</view>\r\n\t\t\t\t\t<view class=\"stor_one_t_r_c\"><image src=\"/static/images/icon18.png\"></image>距离你{{storeInfo.distance}}km</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stor_one_b\">\r\n\t\t\t\t<view>{{storeInfo.address}}</view>\r\n\t\t\t\t<text @click.stop=\"dhTap\">导航前往</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"stor_two\">\r\n\t\t\t<view class=\"kcxq_two_t\"><view class=\"kcxq_two_t_n\"><text>路线指引</text><text></text></view></view>\r\n\t\t\t<view class=\"stor_two_b\">\r\n\t\t\t\t<!-- <video src=\"https://www.runoob.com/try/demo_source/movie.mp4\" controls></video>\r\n\t\t\t\t\t<rich-text :nodes=\"kcjsDetail\"></rich-text>\r\n\t\t\t\t -->\r\n\t\t\t\t<video v-if=\"storeInfo.route_guidance\" :src=\"storeInfo.isoss ? storeInfo.route_guidance : imgbaseUrl + storeInfo.route_guidance\" controls></video>\r\n\t\t\t\t<image :src=\"imgbaseUrl + item\" v-for=\"(item,index) in storeInfo.poster\" :key=\"index\" mode=\"widthFix\" @click=\"openImg(index,storeInfo.poster)\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"stor_thr\">\r\n\t\t\t<view class=\"kcxq_two_t\" style=\"margin:0 52rpx;\"><view class=\"kcxq_two_t_n\"><text>门店课程</text><text></text></view></view>\r\n\t\t\t\r\n\t\t\t<view class=\"stor_thr_c\">\r\n\t\t\t\t<view class=\"stor_thr_c_n\" :style=\"(jbToggle || wuzToggle || laosToggle) ? 'border-radius: 20rpx 20rpx 0 0;' : ''\">\r\n\t\t\t\t\t<view class=\"stor_thr_c_li\" :class=\"jbToggle ? 'stor_thr_c_li_ac' : ''\" @click=\"jbStartTap\">{{jibText == '' ? '级别' : jibText}}<text></text></view>\r\n\t\t\t\t\t<view class=\"stor_thr_c_li\" :class=\"wuzToggle ? 'stor_thr_c_li_ac' : ''\" @click=\"wuzStartTap\">{{wuzText == '' ? '舞种' : wuzText}}<text></text></view>\r\n\t\t\t\t\t<view class=\"stor_thr_c_li\" :class=\"laosToggle ? 'stor_thr_c_li_ac' : ''\" @click=\"laosStartTap\">{{laosText == '' ? '老师' : laosText}}<text></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"gg_rgba\" v-if=\"jbToggle || wuzToggle || laosToggle\" @click=\"gbTcTap\"></view>\r\n\t\t\t\t<!-- 级别 go -->\r\n\t\t\t\t<view class=\"teaxzTanc\" v-if=\"jbToggle\">\r\n\t\t\t\t\t<view class=\"teaxzTanc_t\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in jibLists\" :key=\"index\" :class=\"jibIndex == index ? 'teaxzTanc_t_ac' : ''\" @click=\"jibTap(index)\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"teaxzTanc_b\"><view @click=\"jibReact\">重置</view><text @click=\"jibSubTap\">提交</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 级别 end -->\r\n\t\t\t\t<!-- 舞种 go -->\r\n\t\t\t\t<view class=\"teaxzTanc\" v-if=\"wuzToggle\">\r\n\t\t\t\t\t<view class=\"teaxzTanc_t\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in wuzLists\" :key=\"index\" :class=\"wuzIndex == index ? 'teaxzTanc_t_ac' : ''\" @click=\"wuzTap(index)\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"teaxzTanc_b\"><view @click=\"wuzReact\">重置</view><text @click=\"wuzSubTap\">提交</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 舞种 end -->\r\n\t\t\t\t<!-- 老师 go -->\r\n\t\t\t\t<view class=\"teaxzTanc\" v-if=\"laosToggle\">\r\n\t\t\t\t\t<view class=\"teaxzTanc_t\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in laosLists\" :key=\"index\" :class=\"laosIndex == index ? 'teaxzTanc_t_ac' : ''\" @click=\"laosTap(index)\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"teaxzTanc_b\"><view @click=\"laosReact\">重置</view><text @click=\"laosSubTap\">提交</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 老师 end -->\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"teaCon\">\r\n\t\t\t\t<view class=\"teaCon_li\" v-for=\"(item,index) in storeCourseLists\" :key=\"index\" @click=\"storesxqTap(item)\">\r\n\t\t\t\t\t<view class=\"teaCon_li_a\">{{item.course.name}}</view>\r\n\t\t\t\t\t<view class=\"teaCon_li_b\">\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.teacher.image\" mode=\"aspectFill\" class=\"teaCon_li_b_l\"></image>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_c\">\r\n\t\t\t\t\t\t\t<view class=\"teaCon_li_b_c_a\">{{item.start_time}}-{{item.end_time}}</view>\r\n\t\t\t\t\t\t\t<view class=\"teaCon_li_b_c_b\">上课老师：{{item.teacher.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"teaCon_li_b_c_b\" v-if=\"item.frequency*1 > 0\">次卡消耗：{{item.frequency*1}}次</view>\r\n\t\t\t\t\t\t\t<view class=\"teaCon_li_b_c_c\"><text v-if=\"item.level_name\">{{item.level_name}}</text><text>{{item.dance_name}}</text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-if=\"item.status == 1\" @click.stop>待开课</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 2\" @click.stop>授课中</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 3\" @click.stop>已完成</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 4\" @click.stop>等位中</view>\r\n\t\t\t\t\t\t<!-- <view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 6\" @click.stop>未开始预约</view> -->\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_r yysj\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 6\" @click.stop><text>{{item.start_reservation}}</text><text>开始预约</text></view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 7\" @click.stop>截止预约</view>\r\n\t\t\t\t\t\t<!-- 未开启等位并且课程预约爆满 (课程预约满员时开启等位:0=关闭,1=开启)-->\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.equivalent*1 == 0 && (item.appointment_number*1 >= item.maximum_reservation*1)\" @click.stop=\"kqhyts\">预约</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_r\" :style=\"item.member == 0 ? 'background:#BEBEBE' : ''\" v-else-if=\"item.member == 0\" @click.stop=\"ljtkToggle = true\">预约</view>\r\n\t\t\t\t\t\t<!-- 开启等位 -->\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_r\" v-else @click.stop=\"yypdTo(item,'/pages/Schedule/Schedulexq?id' + item.id)\">{{item.waiting_number*1 > 0 ? '去排队' : '预约'}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"teaCon_li_c\"  v-if=\"item.appointment_number > 0\">\r\n\t\t\t\t\t\t<view class=\"teaCon_li_c_l\">\r\n\t\t\t\t\t\t\t<!-- /static/images/toux.png -->\r\n\t\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.avatar\" v-for=\"(item,index) in item.appointment_people\" :key=\"index\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_c_r\">已预约：<text>{{item.appointment_number}}</text>人;<template v-if=\"item.waiting_number*1 > 0\"><text>{{item.waiting_number}}</text>人在等位</template></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"gg_zwsj\" style=\"margin-bottom:60rpx;\" v-if=\"storeCourseLists.length == 0\">\r\n\t\t\t\t<view class=\"gg_zwsj_w\">\r\n\t\t\t\t\t<image src=\"/static/images/wusj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t<text>暂无课程</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 提示预约弹窗 go -->\r\n\t\t<view class=\"yytnCon\" v-if=\"ljtkToggle\"><view class=\"yytnCon_n\"><image :src=\"imgbaseUrlOss + '/userreport/icon55.png'\"></image><text @click=\"ljktTap\"></text></view><image src=\"/static/images/icon56.png\" @click=\"ljtkToggle = false\"></image></view>\r\n\t\t<!-- 提示预约弹窗 end -->\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tstoreDetailApi,\r\n\tlscxCategoryApi,\r\n\tstoreCourseApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tloding:false,\r\n\t\t\tkcjsDetail:'<p style=\"color:#fff;background:red;font-size:14px;\">富文本内容</p><img src=\"https://luckvan.oss-cn-shanghai.aliyuncs.com/images/a7e913fe-3354-4d90-9f38-db5118aee62c-12.png\" />',\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\timgbaseUrlOss:'',\r\n\t\t\tjibLists:[],\r\n\t\t\tjibIndex:-1,\r\n\t\t\tjibText:'',\r\n\t\t\tjbToggle:false,\r\n\t\t\t\r\n\t\t\twuzLists:[],\r\n\t\t\twuzIndex:-1,\r\n\t\t\twuzText:'',\r\n\t\t\twuzToggle:false,\r\n\t\t\t\r\n\t\t\tlaosLists:[],\r\n\t\t\tlaosIndex:-1,\r\n\t\t\tlaosText:'',\r\n\t\t\tlaosToggle:false,\r\n\t\t\t\r\n\t\t\tstoreInfo:{},//门店详情\r\n\t\t\tstoreCourseLists:[],\r\n\t\t\tpage:1,//当前页数\r\n\t\t\ttotal_pages: 1,//总页数\r\n\t\t\tzanwsj:false,//是否有数据\r\n\t\t\tstatus: 'loading',//底部loding是否显示\r\n\t\t\tloadingText: '努力加载中',\r\n\t\t\tloadmoreText: '轻轻上拉',\r\n\t\t\tnomoreText: '实在没有了',\r\n\t\t\t\r\n\t\t\tstoreId:0,\r\n\t\t\tljtkToggle:false,\r\n\t\t\tqjbutton:'#fff',\r\n\t\t\tqjziti:'#F8F8FA'\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.imgbaseUrlOss = this.$baseUrlOss;\r\n\t\tthis.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.page = 1;\r\n\t\tthis.storeCourseLists = [];//门店课程\r\n\t\tthis.storeCourseData();//门店课程\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.storeId = option.id;\r\n\t\tthis.storeDetail();//门店详情\r\n\t\tthis.categoryData();//老师分类\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.qjziti = uni.getStorageSync('storeInfo').written_words\r\n\t},\r\n\tmethods: {\r\n\t\t//详情跳转\r\n\t\tstoresxqTap(item){\r\n\t\t\tconsole.log(this.isLogined,'this.isLogined');\r\n\t\t\tif(!this.isLogined){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// 未开启会员并且后端设置了必须开通会员方可查看详情\r\n\t\t\tif(item.course.view_type*1 == 0 && item.member == 0){\r\n\t\t\t\tthis.ljtkToggle = true\r\n\t\t\t}else{\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t// url:'/pages/Schedule/Schedulexq?id=' + item.id\r\n\t\t\t\t\turl:'/pages/mine/myCourse/myCoursexq?id=' + item.id\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t//预约约课/排队\r\n\t\tyypdTo(item){\r\n\t\t\tif(!this.isLogined){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// 未开启会员\r\n\t\t\tif(item.member == 0){\r\n\t\t\t\tthis.ljtkToggle = true\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/Schedule/confirmOrder?id=' + item.id + '&storeid=' + this.storeId\r\n\t\t\t})\r\n\t\t},\r\n\t\t//立即开通会员\r\n\t\tljktTap(){\r\n\t\t\tthis.ljtkToggle = false;\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl:'/pages/buy/buy'\r\n\t\t\t})\r\n\t\t},\r\n\t\t//导航\r\n\t\tdhTap(item){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.openLocation({\r\n\t\t\t\tname:that.storeInfo.address,\r\n\t\t\t\tlatitude: that.storeInfo.latitude*1,\r\n\t\t\t\tlongitude: that.storeInfo.longitude*1,\r\n\t\t\t\tsuccess: function () {\r\n\t\t\t\t\tconsole.log('success');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t//预约爆满\r\n\t\tkqhyts(){\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '预约课程已满',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 1000\r\n\t\t\t})\r\n\t\t},\r\n\t\t//门店课程\r\n\t\t/*storeCourseData(){\r\n\t\t\tconsole.log('门店课程12')\r\n\t\t\tlet that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tstoreCourseApi({\r\n\t\t\t\tid:that.storeId,\r\n\t\t\t\tlevel_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,\r\n\t\t\t\tdance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,\r\n\t\t\t\tteacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,\r\n\t\t\t\t// date:'2024-10-30',\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('门店课程',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.storeCourseLists = res.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},*/\r\n\t\t//门店课程\r\n\t\tstoreCourseData(jinz){\r\n\t\t\t\r\n\t\t\tlet that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tstoreCourseApi({\r\n\t\t\t\tpage:that.page,\r\n\t\t\t\tid:that.storeId,\r\n\t\t\t\tlevel_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,\r\n\t\t\t\tdance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,\r\n\t\t\t\tteacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('门店课程',res)\r\n\t\t\t\t/*if (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.storeCourseLists = res.data.data;\r\n\t\t\t\t}*/\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tvar obj = res.data.data;\r\n\t\t\t\t\tthat.storeCourseLists = that.storeCourseLists.concat(obj);\r\n\t\t\t\t\tthat.zanwsj = that.storeCourseLists.length ? true : false;\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t// that.total_pages = Math.ceil(res.total/20);\r\n\t\t\t\t\tthat.total_pages = res.data.last_page;\r\n\t\t\t\t\tif (that.page != 1) {\r\n\t\t\t\t\t\tif (that.total_pages >= that.page) {\r\n\t\t\t\t\t\t\tthat.status = 'loading'\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.storeCourseLists.length == 0){\r\n\t\t\t\t\t\tthat.zanwsj = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.zanwsj = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.data.total*1<=10){\r\n\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\t//console.log('到底了');\r\n\t\t\tif (this.page != 1) {\r\n\t\t\t\tif (this.total_pages >= this.page) {\r\n\t\t\t\t\tthis.storeCourseData();//门店课程\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t    console.log('我被下拉了');\r\n\t\t    this.page = 1;\r\n\t\t    this.storeCourseLists = [];//门店课程\r\n\t\t\tthis.storeCourseData();//门店课程\r\n\t\t},\r\n\t\t//门店详情\r\n\t\tstoreDetail(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tstoreDetailApi({\r\n\t\t\t\tlongitude:uni.getStorageSync('postion').longitude,\r\n\t\t\t\tlatitude:uni.getStorageSync('postion').latitude,\r\n\t\t\t\tid:that.storeId\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('门店详情',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\t// res.data.route_guidance = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734312077132_ces.mp4'\r\n\t\t\t\t\tif(res.data.route_guidance){\r\n\t\t\t\t\t\tres.data.isoss = res.data.route_guidance.substring(0,5) == 'https' ? true : false\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// console.log(res.data.route_guidance.substring(0,5),res.data.isoss,'ddd');\r\n\t\t\t\t\tthat.storeInfo = res.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//老师分类\r\n\t\tcategoryData(){\r\n\t\t\tlet that = this;\r\n\t\t\tlscxCategoryApi({}).then(res => {\r\n\t\t\t\tconsole.log('老师分类',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.jibLists = res.data.level;\r\n\t\t\t\t\tthat.wuzLists = res.data.dance;\r\n\t\t\t\t\tthat.laosLists = res.data.teacher;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//关闭所有弹窗\r\n\t\tgbTcTap(){\r\n\t\t\tthis.jbToggle = false;\r\n\t\t\tthis.wuzToggle = false;\r\n\t\t\tthis.laosToggle = false;\r\n\t\t},\r\n\t\t//级别弹窗开启\r\n\t\tjbStartTap(){\r\n\t\t\tthis.jbToggle = !this.jbToggle;\r\n\t\t\tthis.wuzToggle = false;\r\n\t\t\tthis.laosToggle = false;\r\n\t\t},\r\n\t\t//级别选择\r\n\t\tjibTap(index){\r\n\t\t\tthis.jibIndex = index;\r\n\t\t},\r\n\t\t//级别提交\r\n\t\tjibSubTap(){\r\n\t\t\tif(this.jibIndex == -1){\r\n\t\t\t\tthis.jibText = ''\r\n\t\t\t}else{\r\n\t\t\t\tthis.jibText = this.jibLists[this.jibIndex].name\r\n\t\t\t}\r\n\t\t\tthis.jbToggle = false;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\tthis.storeCourseData();//门店课程\r\n\t\t},\r\n\t\t//级别重置\r\n\t\tjibReact(){\r\n\t\t\tthis.jibIndex = -1;\r\n\t\t},\r\n\t\t//舞种弹窗开启\r\n\t\twuzStartTap(){\r\n\t\t\tthis.jbToggle = false;\r\n\t\t\tthis.wuzToggle = !this.wuzToggle;\r\n\t\t\tthis.laosToggle = false;\r\n\t\t},\r\n\t\t//舞种选择\r\n\t\twuzTap(index){\r\n\t\t\tthis.wuzIndex = index;\r\n\t\t},\r\n\t\t//舞种提交\r\n\t\twuzSubTap(){\r\n\t\t\tif(this.wuzIndex == -1){\r\n\t\t\t\tthis.wuzText = ''\r\n\t\t\t}else{\r\n\t\t\t\tthis.wuzText = this.wuzLists[this.wuzIndex].name\r\n\t\t\t}\r\n\t\t\tthis.wuzToggle = false;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\tthis.storeCourseData();//门店课程\r\n\t\t},\r\n\t\t//舞种重置\r\n\t\twuzReact(){\r\n\t\t\tthis.wuzIndex = -1;\r\n\t\t},\r\n\t\t//老师弹窗开启\r\n\t\tlaosStartTap(){\t\t\t\r\n\t\t\tthis.jbToggle = false;\r\n\t\t\tthis.wuzToggle = false;\r\n\t\t\tthis.laosToggle = !this.laosToggle;\r\n\t\t},\r\n\t\t//老师选择\r\n\t\tlaosTap(index){\r\n\t\t\tthis.laosIndex = index;\r\n\t\t},\r\n\t\t//老师提交\r\n\t\tlaosSubTap(){\r\n\t\t\tif(this.laosIndex == -1){\r\n\t\t\t\tthis.laosText = ''\r\n\t\t\t}else{\r\n\t\t\t\tthis.laosText = this.laosLists[this.laosIndex].name\r\n\t\t\t}\r\n\t\t\tthis.laosToggle = false;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\tthis.storeCourseData();//门店课程\r\n\t\t},\r\n\t\t//老师重置\r\n\t\tlaosReact(){\r\n\t\t\tthis.laosIndex = -1;\r\n\t\t},\r\n\t\t//打开图片\r\n\t\topenImg(idx, imgs) {\r\n\t\t\tlet arr = []\r\n\t\t\tfor (let i = 0; i < imgs.length; i++) {\r\n\t\t\t\tarr.push(this.imgbaseUrl + imgs[i])\r\n\t\t\t}\r\n\t\t\tconsole.log(idx, imgs);\r\n\t\t\tuni.previewImage({\r\n\t\t\t\tcurrent: idx,\r\n\t\t\t\turls: arr\r\n\t\t\t})\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.storesDetail{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./storesDetail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./storesDetail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753622574086\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}