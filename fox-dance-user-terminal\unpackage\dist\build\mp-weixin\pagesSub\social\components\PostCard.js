(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/components/PostCard"],{"29da":function(t,n,e){},"390e":function(t,n,e){"use strict";e.r(n);var o=e("5d39"),i=e("a853");for(var r in i)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(r);e("9ba0");var u=e("828b"),a=Object(u["a"])(i["default"],o["b"],o["c"],!1,null,"9c0482c4",null,!1,o["a"],void 0);n["default"]=a.exports},5860:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o={name:"PostCard",props:{post:{type:Object,required:!0}},data:function(){return{imageError:!1,defaultCover:"https://picsum.photos/300/200?random=default"}},methods:{formatLikeCount:function(t){return t&&0!==t?t<1e3?t.toString():t<1e4?(t/1e3).toFixed(1)+"k":t<1e6?Math.floor(t/1e3)+"k":(t/1e6).toFixed(1)+"M":"0"},onImageError:function(){this.imageError=!0},onImageLoad:function(){this.imageError=!1},goPostDetail:function(){this.$emit("click",this.post)},goUserProfile:function(){this.$emit("user-click",this.post)}}};n.default=o},"5d39":function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return r})),e.d(n,"a",(function(){return o}));var o={uIcon:function(){return e.e("components/uview-ui/components/u-icon/u-icon").then(e.bind(null,"8398"))},uAvatar:function(){return e.e("components/uview-ui/components/u-avatar/u-avatar").then(e.bind(null,"8d35"))}},i=function(){var t=this.$createElement,n=(this._self._c,this.formatLikeCount(this.post.likeCount));this.$mp.data=Object.assign({},{$root:{m0:n}})},r=[]},"9ba0":function(t,n,e){"use strict";var o=e("29da"),i=e.n(o);i.a},a853:function(t,n,e){"use strict";e.r(n);var o=e("5860"),i=e.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(r);n["default"]=i.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesSub/social/components/PostCard-create-component',
    {
        'pagesSub/social/components/PostCard-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("390e"))
        })
    },
    [['pagesSub/social/components/PostCard-create-component']]
]);
