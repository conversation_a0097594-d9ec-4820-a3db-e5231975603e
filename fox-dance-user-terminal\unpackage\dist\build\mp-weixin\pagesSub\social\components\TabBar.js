(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/components/TabBar"],{"45d2":function(e,t,a){"use strict";a.d(t,"b",(function(){return s})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={uTabbar:function(){return a.e("components/uview-ui/components/u-tabbar/u-tabbar").then(a.bind(null,"a4ea"))}},s=function(){var e=this.$createElement;this._self._c},i=[]},"5bed":function(e,t,a){"use strict";a.r(t);var n=a("45d2"),s=a("a802");for(var i in s)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return s[e]}))}(i);a("9232");var o=a("828b"),u=Object(o["a"])(s["default"],n["b"],n["c"],!1,null,"6edd0409",null,!1,n["a"],void 0);t["default"]=u.exports},8476:function(e,t,a){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={name:"TabBar",props:{currentTab:{type:Number,default:0}},data:function(){return{messageCount:0,tabbarList:[{pagePath:"/pagesSub/social/home/<USER>",text:"首页",iconPath:"home",selectedIconPath:"home-fill"},{pagePath:"/pagesSub/social/discover/index",text:"发现",iconPath:"search",selectedIconPath:"search"},{pagePath:"/pagesSub/social/publish/index",text:"发布",iconPath:"plus",selectedIconPath:"plus"},{pagePath:"/pagesSub/social/message/index",text:"消息",iconPath:"chat",selectedIconPath:"chat-fill",badge:0},{pagePath:"/pagesSub/social/profile/index",text:"我的",iconPath:"account",selectedIconPath:"account-fill"}]}},mounted:function(){this.startMessageMonitoring(),e.$on("clearMessageBadge",this.clearMessageBadge)},beforeDestroy:function(){this.stopMessageMonitoring(),e.$off("clearMessageBadge",this.clearMessageBadge)},methods:{handlePublish:function(){e.navigateTo({url:"/pagesSub/social/publish/index"})},tabbarChange:function(e){var t=this.tabbarList[e];t.midButton||this.$emit("tab-change",{index:e,item:t})},startMessageMonitoring:function(){var e=this;this.checkMessageCount(),this.messageTimer=setInterval((function(){e.checkMessageCount()}),3e4)},stopMessageMonitoring:function(){this.messageTimer&&(clearInterval(this.messageTimer),this.messageTimer=null)},checkMessageCount:function(){var e=this;this.getUnreadMessageCount().then((function(t){e.updateMessageBadge(t)}))},getUnreadMessageCount:function(){return new Promise((function(e){setTimeout((function(){var t=Math.floor(6*Math.random());e(t)}),100)}))},updateMessageBadge:function(e){this.messageCount=e;var t=this.tabbarList.findIndex((function(e){return"/pagesSub/social/message/index"===e.pagePath}));-1!==t&&(this.tabbarList[t].badge=e>0?e:0)},clearMessageBadge:function(){this.updateMessageBadge(0)}}};t.default=a}).call(this,a("df3c")["default"])},9232:function(e,t,a){"use strict";var n=a("9e89"),s=a.n(n);s.a},"9e89":function(e,t,a){},a802:function(e,t,a){"use strict";a.r(t);var n=a("8476"),s=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=s.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesSub/social/components/TabBar-create-component',
    {
        'pagesSub/social/components/TabBar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("5bed"))
        })
    },
    [['pagesSub/social/components/TabBar-create-component']]
]);
