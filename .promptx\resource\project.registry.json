{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-27T10:55:44.117Z", "updatedAt": "2025-07-27T10:55:44.137Z", "resourceCount": 8}, "resources": [{"id": "fox-development-workflow", "source": "project", "protocol": "execution", "name": "Fox Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fox-fullstack-developer/execution/fox-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-27T10:55:44.122Z", "updatedAt": "2025-07-27T10:55:44.122Z", "scannedAt": "2025-07-27T10:55:44.122Z", "path": "role/fox-fullstack-developer/execution/fox-development-workflow.execution.md"}}, {"id": "fox-fullstack-developer", "source": "project", "protocol": "role", "name": "Fox Fullstack Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fox-fullstack-developer/fox-fullstack-developer.role.md", "metadata": {"createdAt": "2025-07-27T10:55:44.123Z", "updatedAt": "2025-07-27T10:55:44.123Z", "scannedAt": "2025-07-27T10:55:44.123Z", "path": "role/fox-fullstack-developer/fox-fullstack-developer.role.md"}}, {"id": "fullstack-thinking", "source": "project", "protocol": "thought", "name": "Fullstack Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fullstack-developer/thought/fullstack-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T10:55:44.131Z", "updatedAt": "2025-07-27T10:55:44.131Z", "scannedAt": "2025-07-27T10:55:44.131Z", "path": "role/fullstack-developer/thought/fullstack-thinking.thought.md"}}, {"id": "fullstack-development", "source": "project", "protocol": "execution", "name": "Fullstack Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fullstack-developer/execution/fullstack-development.execution.md", "metadata": {"createdAt": "2025-07-27T10:55:44.129Z", "updatedAt": "2025-07-27T10:55:44.129Z", "scannedAt": "2025-07-27T10:55:44.129Z", "path": "role/fullstack-developer/execution/fullstack-development.execution.md"}}, {"id": "fullstack-developer", "source": "project", "protocol": "role", "name": "Fullstack Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fullstack-developer/fullstack-developer.role.md", "metadata": {"createdAt": "2025-07-27T10:55:44.129Z", "updatedAt": "2025-07-27T10:55:44.129Z", "scannedAt": "2025-07-27T10:55:44.129Z", "path": "role/fullstack-developer/fullstack-developer.role.md"}}, {"id": "project-analysis-workflow", "source": "project", "protocol": "execution", "name": "Project Analysis Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/project-analyst/execution/project-analysis-workflow.execution.md", "metadata": {"createdAt": "2025-07-27T10:55:44.134Z", "updatedAt": "2025-07-27T10:55:44.134Z", "scannedAt": "2025-07-27T10:55:44.134Z", "path": "role/project-analyst/execution/project-analysis-workflow.execution.md"}}, {"id": "project-analyst", "source": "project", "protocol": "role", "name": "Project Analyst 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/project-analyst/project-analyst.role.md", "metadata": {"createdAt": "2025-07-27T10:55:44.135Z", "updatedAt": "2025-07-27T10:55:44.135Z", "scannedAt": "2025-07-27T10:55:44.135Z", "path": "role/project-analyst/project-analyst.role.md"}}, {"id": "systematic-analysis", "source": "project", "protocol": "thought", "name": "Systematic Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/project-analyst/thought/systematic-analysis.thought.md", "metadata": {"createdAt": "2025-07-27T10:55:44.137Z", "updatedAt": "2025-07-27T10:55:44.137Z", "scannedAt": "2025-07-27T10:55:44.137Z", "path": "role/project-analyst/thought/systematic-analysis.thought.md"}}], "stats": {"totalResources": 8, "byProtocol": {"execution": 3, "role": 3, "thought": 2}, "bySource": {"project": 8}}}