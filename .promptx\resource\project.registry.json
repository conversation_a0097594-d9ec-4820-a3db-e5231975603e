{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-27T09:25:53.372Z", "updatedAt": "2025-07-27T09:25:53.379Z", "resourceCount": 3}, "resources": [{"id": "fullstack-development", "source": "project", "protocol": "execution", "name": "Fullstack Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fullstack-developer/execution/fullstack-development.execution.md", "metadata": {"createdAt": "2025-07-27T09:25:53.376Z", "updatedAt": "2025-07-27T09:25:53.376Z", "scannedAt": "2025-07-27T09:25:53.376Z", "path": "role/fullstack-developer/execution/fullstack-development.execution.md"}}, {"id": "fullstack-developer", "source": "project", "protocol": "role", "name": "Fullstack Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fullstack-developer/fullstack-developer.role.md", "metadata": {"createdAt": "2025-07-27T09:25:53.377Z", "updatedAt": "2025-07-27T09:25:53.377Z", "scannedAt": "2025-07-27T09:25:53.377Z", "path": "role/fullstack-developer/fullstack-developer.role.md"}}, {"id": "fullstack-thinking", "source": "project", "protocol": "thought", "name": "Fullstack Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fullstack-developer/thought/fullstack-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T09:25:53.379Z", "updatedAt": "2025-07-27T09:25:53.379Z", "scannedAt": "2025-07-27T09:25:53.379Z", "path": "role/fullstack-developer/thought/fullstack-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}