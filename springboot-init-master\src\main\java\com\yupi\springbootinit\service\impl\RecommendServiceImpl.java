package com.yupi.springbootinit.service.impl;

import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.model.entity.UserStats;
import com.yupi.springbootinit.model.vo.UserProfileVO;
import com.yupi.springbootinit.service.BaUserService;
import com.yupi.springbootinit.service.RecommendService;
import com.yupi.springbootinit.service.UserStatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 推荐服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
@Slf4j
public class RecommendServiceImpl implements RecommendService {

    @Resource
    private BaUserService baUserService;

    @Resource
    private UserStatsService userStatsService;

    /**
     * 默认推荐的用户ID列表
     * 项目初期先推荐这些用户，后续可以根据算法动态调整
     */
    private static final List<Long> DEFAULT_RECOMMEND_USER_IDS = Arrays.asList(18L);

    @Override
    public List<UserProfileVO> getRecommendUsers(Long currentUserId, Integer limit) {
        try {
            log.info("开始获取推荐用户 - currentUserId: {}, limit: {}", currentUserId, limit);

            List<UserProfileVO> recommendUsers = new ArrayList<>();

            // 获取默认推荐用户
            for (Long userId : DEFAULT_RECOMMEND_USER_IDS) {
                // 排除当前用户自己
                if (currentUserId != null && currentUserId.equals(userId)) {
                    continue;
                }

                // 查询用户信息
                BaUser user = baUserService.getById(userId);
                if (user == null) {
                    log.warn("推荐用户不存在 - userId: {}", userId);
                    continue;
                }

                log.info("找到推荐用户 - userId: {}, nickname: {}, avatar: {}",
                        user.getId(), user.getNickname(), user.getAvatar());

                // 构建用户资料VO
                UserProfileVO userProfile = buildUserProfileVO(user);
                recommendUsers.add(userProfile);

                // 达到限制数量就停止
                if (recommendUsers.size() >= limit) {
                    break;
                }
            }

            log.info("获取推荐用户完成 - 返回数量: {}", recommendUsers.size());
            return recommendUsers;

        } catch (Exception e) {
            log.error("获取推荐用户异常 - currentUserId: {}, error: {}", currentUserId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建用户资料VO
     */
    private UserProfileVO buildUserProfileVO(BaUser user) {
        UserProfileVO userProfile = new UserProfileVO();
        userProfile.setUserId(user.getId().longValue()); // 转换Integer到Long
        userProfile.setNickname(user.getNickname());
        userProfile.setSocialId(user.getSocial_id());
        userProfile.setAvatar(user.getAvatar());
        userProfile.setBio(user.getBio());
        userProfile.setDanceType(user.getDance_type()); // 修正方法名

        // 获取用户统计数据
        try {
            UserStats userStats = userStatsService.getUserStats(user.getId().longValue());
            if (userStats != null) {
                userProfile.setPostCount(userStats.getPostCount());
                userProfile.setFollowingCount(userStats.getFollowingCount());
                userProfile.setFollowerCount(userStats.getFollowerCount()); // 使用正确的字段名
                userProfile.setLikeReceivedCount(userStats.getLikeReceivedCount());
            } else {
                // 设置默认值
                userProfile.setPostCount(0);
                userProfile.setFollowingCount(0);
                userProfile.setFollowerCount(0);
                userProfile.setLikeReceivedCount(0);
            }
        } catch (Exception e) {
            log.warn("获取用户统计数据失败 - userId: {}, error: {}", user.getId(), e.getMessage());
            // 设置默认值
            userProfile.setPostCount(0);
            userProfile.setFollowingCount(0);
            userProfile.setFollowerCount(0);
            userProfile.setLikeReceivedCount(0);
        }

        // 设置推荐理由（项目初期的固定理由）
        userProfile.setRecommendReason("推荐用户");

        return userProfile;
    }
}
