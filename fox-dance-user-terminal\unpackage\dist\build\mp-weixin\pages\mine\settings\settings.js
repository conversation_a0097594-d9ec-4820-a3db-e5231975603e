(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/settings/settings"],{"0eb9":function(n,t,e){"use strict";e.r(t);var o=e("6f99"),c=e("3bcb");for(var u in c)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return c[n]}))}(u);e("440e");var i=e("828b"),a=Object(i["a"])(c["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=a.exports},"3b8b":function(n,t,e){"use strict";(function(n,t){var o=e("47a9");e("cff9");o(e("3240"));var c=o(e("0eb9"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(c.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"3bcb":function(n,t,e){"use strict";e.r(t);var o=e("edb9"),c=e.n(o);for(var u in o)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(u);t["default"]=c.a},"440e":function(n,t,e){"use strict";var o=e("8431"),c=e.n(o);c.a},"6f99":function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return c})),e.d(t,"a",(function(){}));var o=function(){var n=this.$createElement;this._self._c},c=[]},8431:function(n,t,e){},edb9:function(n,t,e){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e={data:function(){return{isLogined:!0}},onShow:function(){},methods:{logoutTap:function(){n.showModal({title:"温馨提示",content:"确定要退出登录吗？",success:function(t){t.confirm?(n.removeStorageSync("token"),n.removeStorageSync("userid"),n.showToast({icon:"none",title:"退出成功",duration:2e3}),setTimeout((function(){n.navigateBack()}),1e3)):t.cancel&&console.log("用户点击取消")}})},navTo:function(t){n.navigateTo({url:t})}}};t.default=e}).call(this,e("df3c")["default"])}},[["3b8b","common/runtime","common/vendor"]]]);