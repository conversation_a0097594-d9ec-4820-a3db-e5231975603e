(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/publish/index"],{4173:function(e,t,n){"use strict";n.r(t);var o=n("6e3f"),s=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=s.a},5129:function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={uAvatar:function(){return n.e("components/uview-ui/components/u-avatar/u-avatar").then(n.bind(null,"8d35"))},uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))},uPopup:function(){return n.e("components/uview-ui/components/u-popup/u-popup").then(n.bind(null,"b525"))},uInput:function(){return Promise.all([n.e("common/vendor"),n.e("components/uview-ui/components/u-input/u-input")]).then(n.bind(null,"d67d"))}},s=function(){var e=this,t=e.$createElement,n=(e._self._c,e.postTitle.length),o=e.postContent.length,s=e.selectedImages.length,a=e.selectedTopics.length,i=a?e.selectedTopics.map((function(e){return"#"+e})).join(" "):null,c=e.__map(e.filteredTopics,(function(t,n){var o=e.__get_orig(t),s=e.selectedTopics.includes(t.name);return{$orig:o,g5:s}}));e._isMounted||(e.e0=function(t){e.showTopicModal=!1},e.e1=function(t){e.showLocationModal=!1}),e.$mp.data=Object.assign({},{$root:{g0:n,g1:o,g2:s,g3:a,g4:i,l0:c}})},a=[]},"6e3f":function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=o(n("7eb4")),a=o(n("ee10")),i=n("41ad"),c={name:"SocialPublish",data:function(){return{userInfo:{avatar:"/static/images/toux.png",nickname:"加载中..."},postTitle:"",postContent:"",selectedImages:[],selectedTopics:[],selectedLocation:null,visibility:"public",publishing:!1,showTopicModal:!1,showLocationModal:!1,topicKeyword:"",allTopics:[{id:1,name:"街舞",postCount:1234},{id:2,name:"现代舞",postCount:856},{id:3,name:"芭蕾",postCount:642},{id:4,name:"拉丁舞",postCount:789},{id:5,name:"爵士舞",postCount:456},{id:6,name:"民族舞",postCount:321},{id:7,name:"古典舞",postCount:298},{id:8,name:"舞蹈教学",postCount:567},{id:9,name:"舞蹈比赛",postCount:234},{id:10,name:"舞蹈培训",postCount:189}],nearbyLocations:[{id:1,name:"星巴克咖啡",address:"北京市朝阳区三里屯太古里"},{id:2,name:"三里屯太古里",address:"北京市朝阳区三里屯路19号"},{id:3,name:"朝阳公园",address:"北京市朝阳区朝阳公园南路1号"}]}},computed:{canPublish:function(){return this.postTitle.trim().length>0||this.postContent.trim().length>0||this.selectedImages.length>0},visibilityText:function(){return{public:"公开",friends:"仅朋友可见",private:"仅自己可见"}[this.visibility]},filteredTopics:function(){var e=this;return this.topicKeyword?this.allTopics.filter((function(t){return t.name.includes(e.topicKeyword)})):this.allTopics}},onLoad:function(){console.log("=== 发布页面加载 ==="),console.log("onLoad方法被调用了"),this.checkUserLogin()&&(e.showToast({title:"onLoad被调用",icon:"none",duration:2e3}),this.testApiConnection(),this.loadUserInfo(),this.loadHotTopics())},mounted:function(){console.log("=== mounted生命周期被调用 ==="),"加载中..."===this.userInfo.nickname&&(console.log("onLoad可能没有执行，在mounted中重新执行"),this.testApiConnection(),this.loadUserInfo(),this.loadHotTopics())},methods:{checkUserLogin:function(){var t=e.getStorageSync("token"),n=e.getStorageSync("userid");return!(!t||!n)||(console.log("用户未登录，跳转到登录页"),e.showToast({title:"请先登录",icon:"none",duration:2e3}),setTimeout((function(){e.navigateTo({url:"/pages/login/login"})}),1500),!1)},testApiConnection:function(){return(0,a.default)(s.default.mark((function t(){var n;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("测试API连接..."),t.prev=1,t.next=4,e.request({url:"http://localhost:8101/api/health",method:"GET",timeout:5e3});case 4:n=t.sent,console.log("API连接测试结果:",n),200===n.statusCode?console.log("✅ 后端服务连接正常"):console.log("❌ 后端服务响应异常:",n.statusCode),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](1),console.error("❌ 后端服务连接失败:",t.t0),e.showToast({title:"后端服务连接失败",icon:"none",duration:3e3});case 13:case"end":return t.stop()}}),t,null,[[1,9]])})))()},loadUserInfo:function(){var t=this;return(0,a.default)(s.default.mark((function n(){var o,a;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return console.log("开始加载用户信息..."),n.prev=1,o=e.getStorageSync("userid"),console.log("调用getUserProfile，用户ID:",o),n.next=6,(0,i.getUserProfile)(o);case 6:a=n.sent,console.log("获取用户信息结果:",a),a&&0===a.code&&a.data?t.userInfo={avatar:a.data.avatar?a.data.avatar.startsWith("http")?a.data.avatar:"https://file.foxdance.com.cn"+a.data.avatar:"/static/images/toux.png",nickname:a.data.nickname||"用户"}:(console.warn("获取用户信息失败:",a),t.userInfo={avatar:"/static/images/toux.png",nickname:"用户"}),n.next=17;break;case 11:n.prev=11,n.t0=n["catch"](1),console.error("加载用户信息失败:",n.t0),console.error("错误详情:",n.t0.message||n.t0),e.showToast({title:"用户信息加载失败",icon:"none",duration:2e3}),t.userInfo={avatar:"/static/images/toux.png",nickname:"用户"};case 17:case"end":return n.stop()}}),n,null,[[1,11]])})))()},loadHotTopics:function(){var t=this;return(0,a.default)(s.default.mark((function n(){var o;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return console.log("开始加载热门话题..."),n.prev=1,n.next=4,(0,i.getHotTags)(20);case 4:o=n.sent,console.log("获取热门话题结果:",o),o&&0===o.code&&o.data&&o.data.length>0?(t.allTopics=o.data.map((function(e){return{id:e.id,name:e.name,postCount:e.useCount||0}})),console.log("话题加载成功，数量:",t.allTopics.length)):o&&o.length>0?(t.allTopics=o.map((function(e){return{id:e.id,name:e.name,postCount:e.useCount||0}})),console.log("话题加载成功（数组格式），数量:",t.allTopics.length)):console.log("没有获取到话题数据，hotTags:",o),n.next=16;break;case 9:n.prev=9,n.t0=n["catch"](1),console.error("加载热门话题失败:",n.t0),console.error("错误详情:",n.t0.message||n.t0),e.showToast({title:"话题加载失败",icon:"none",duration:2e3}),t.allTopics=[{id:1,name:"生活",postCount:0},{id:2,name:"美食",postCount:0},{id:3,name:"旅行",postCount:0},{id:4,name:"摄影",postCount:0},{id:5,name:"时尚",postCount:0}],console.log("使用默认话题列表");case 16:case"end":return n.stop()}}),n,null,[[1,9]])})))()},goBack:function(){this.postTitle||this.postContent||this.selectedImages.length?e.showModal({title:"提示",content:"确定要放弃编辑吗？",success:function(t){t.confirm&&e.navigateBack()}}):e.navigateBack()},chooseImage:function(){var t=this;return(0,a.default)(s.default.mark((function n(){var o;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:o=9-t.selectedImages.length,e.chooseImage({count:o,sizeType:["compressed"],sourceType:["album","camera"],success:function(){var n=(0,a.default)(s.default.mark((function n(o){var a,c,u,l,r,d,p;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:a=o.tempFilePaths,console.log("🔥 选择图片成功，开始上传到COS:",a),e.showLoading({title:"上传图片中...",mask:!0}),n.prev=3,c=0,u=0,l=0;case 7:if(!(l<a.length)){n.next=25;break}return r=a[l],console.log("🔥 上传第".concat(l+1,"张图片:"),r),n.prev=10,n.next=13,(0,i.uploadPostImage)(r,"file",{driver:"cos"});case 13:d=n.sent,console.log("🔥 第".concat(l+1,"张图片上传结果:"),d),0===d.code&&d.data?(p=d.data,t.selectedImages.push(p),c++,console.log("✅ 图片上传成功:",p)):(u++,console.error("❌ 图片上传失败，响应格式错误:",d)),n.next=22;break;case 18:n.prev=18,n.t0=n["catch"](10),u++,console.error("❌ 第".concat(l+1,"张图片上传异常:"),n.t0);case 22:l++,n.next=7;break;case 25:e.hideLoading(),c>0&&t.$u.toast("成功上传".concat(c,"张图片")),u>0&&t.$u.toast("".concat(u,"张图片上传失败")),n.next=35;break;case 30:n.prev=30,n.t1=n["catch"](3),e.hideLoading(),console.error("❌ 图片上传过程异常:",n.t1),t.$u.toast("图片上传失败");case 35:case"end":return n.stop()}}),n,null,[[3,30],[10,18]])})));return function(e){return n.apply(this,arguments)}}(),fail:function(e){console.error("❌ 选择图片失败:",e),t.$u.toast("选择图片失败")}});case 2:case"end":return n.stop()}}),n)})))()},removeImage:function(e){this.selectedImages.splice(e,1)},selectTopic:function(){this.showTopicModal=!0},toggleTopic:function(e){var t=this.selectedTopics.indexOf(e.name);t>-1?this.selectedTopics.splice(t,1):this.selectedTopics.length<3?this.selectedTopics.push(e.name):this.$u.toast("最多选择3个话题")},searchTopics:function(){},selectLocation:function(){var t=this;console.log("打开位置选择..."),e.chooseLocation({success:function(n){console.log("位置选择成功:",n),t.selectedLocation={name:n.name||n.address,address:n.address,latitude:n.latitude,longitude:n.longitude},e.showToast({title:"位置选择成功",icon:"success",duration:1500})},fail:function(n){console.error("位置选择失败:",n),n.errMsg&&n.errMsg.includes("cancel")||(e.showToast({title:"位置选择失败",icon:"none",duration:2e3}),t.showLocationModal=!0)}})},selectLocationItem:function(e){this.selectedLocation=e,this.showLocationModal=!1},clearLocation:function(){this.selectedLocation=null,e.showToast({title:"已清除位置",icon:"success",duration:1e3})},setVisibility:function(){var t=this;e.showActionSheet({itemList:["公开","仅朋友可见","仅自己可见"],success:function(e){t.visibility=["public","friends","private"][e.tapIndex]}})},publishPost:function(){var t=this;return(0,a.default)(s.default.mark((function n(){var o,a,c,u,l,r,d;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.canPublish&&!t.publishing){n.next=2;break}return n.abrupt("return");case 2:if(o=e.getStorageSync("userid"),o){n.next=7;break}return e.showToast({title:"请先登录",icon:"none",duration:2e3}),setTimeout((function(){e.navigateTo({url:"/pages/login/login"})}),1500),n.abrupt("return");case 7:return t.publishing=!0,n.prev=8,r={userId:Number(o),title:t.postTitle.trim()||null,content:t.postContent.trim(),images:t.selectedImages,tags:t.selectedTopics.map((function(e){return e.name||e})),locationName:(null===(a=t.selectedLocation)||void 0===a?void 0:a.name)||"",locationLatitude:(null===(c=t.selectedLocation)||void 0===c?void 0:c.latitude)||null,locationLongitude:(null===(u=t.selectedLocation)||void 0===u?void 0:u.longitude)||null,locationAddress:(null===(l=t.selectedLocation)||void 0===l?void 0:l.address)||"",isPublic:"public"===t.visibility?1:0,status:1},console.log("发布帖子数据:",r),n.next=13,(0,i.createPost)(r);case 13:d=n.sent,console.log("发布API返回结果:",d),d&&0===d.code?t.$u.toast("发布成功"):(t.$u.toast((null===d||void 0===d?void 0:d.message)||"发布失败，请重试"),t.publishing=!1),n.next=23;break;case 18:n.prev=18,n.t0=n["catch"](8),console.error("发布帖子失败:",n.t0),t.$u.toast("网络错误，请重试"),t.publishing=!1;case 23:case"end":return n.stop()}}),n,null,[[8,18]])})))()}}};t.default=c}).call(this,n("df3c")["default"])},acfe:function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("cff9");o(n("3240"));var s=o(n("b2fb"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(s.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},b2fb:function(e,t,n){"use strict";n.r(t);var o=n("5129"),s=n("4173");for(var a in s)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(a);n("ef7f");var i=n("828b"),c=Object(i["a"])(s["default"],o["b"],o["c"],!1,null,"254180e3",null,!1,o["a"],void 0);t["default"]=c.exports},e0ee:function(e,t,n){},ef7f:function(e,t,n){"use strict";var o=n("e0ee"),s=n.n(o);s.a}},[["acfe","common/runtime","common/vendor"]]]);