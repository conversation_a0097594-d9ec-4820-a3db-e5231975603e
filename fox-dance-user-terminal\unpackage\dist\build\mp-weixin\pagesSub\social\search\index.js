(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/search/index"],{"5cab":function(t,n,e){"use strict";(function(t,n){var o=e("47a9");e("cff9");o(e("3240"));var c=o(e("f844"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(c.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"9f77":function(t,n,e){},a013:function(t,n,e){"use strict";var o=e("9f77"),c=e.n(o);c.a},b130:function(t,n,e){"use strict";e.r(n);var o=e("fc0a"),c=e.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(r);n["default"]=c.a},cc44:function(t,n,e){"use strict";e.d(n,"b",(function(){return c})),e.d(n,"c",(function(){return r})),e.d(n,"a",(function(){return o}));var o={uSearch:function(){return e.e("components/uview-ui/components/u-search/u-search").then(e.bind(null,"ff15"))},uEmpty:function(){return e.e("components/uview-ui/components/u-empty/u-empty").then(e.bind(null,"8fa1"))},uIcon:function(){return e.e("components/uview-ui/components/u-icon/u-icon").then(e.bind(null,"8398"))},uTag:function(){return e.e("components/uview-ui/components/u-tag/u-tag").then(e.bind(null,"24b8"))}},c=function(){var t=this.$createElement,n=(this._self._c,this.searched?this.results.length:null),e=this.searched?null:this.searchHistory.length;this.$mp.data=Object.assign({},{$root:{g0:n,g1:e}})},r=[]},f844:function(t,n,e){"use strict";e.r(n);var o=e("cc44"),c=e("b130");for(var r in c)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return c[t]}))}(r);e("a013");var a=e("828b"),s=Object(a["a"])(c["default"],o["b"],o["c"],!1,null,"21be25e4",null,!1,o["a"],void 0);n["default"]=s.exports},fc0a:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o={name:"SearchPage",components:{PostCard:function(){e.e("pagesSub/social/components/PostCard").then(function(){return resolve(e("390e"))}.bind(null,e)).catch(e.oe)}},data:function(){return{keyword:"",searched:!1,searchHistory:["街舞","美食探店","周末去哪儿"],hotSearches:["即兴舞蹈","旅行vlog","健身打卡","美食制作","摄影技巧"],results:[]}},methods:{onSearch:function(t){t&&(this.searched=!0,this.updateHistory(t),this.results=this.generateMockResults(t))},updateHistory:function(t){var n=this.searchHistory.indexOf(t);n>-1&&this.searchHistory.splice(n,1),this.searchHistory.unshift(t),this.searchHistory.length>10&&this.searchHistory.pop()},clearHistory:function(){this.searchHistory=[]},onTagClick:function(t){this.keyword=t,this.onSearch(t)},generateMockResults:function(t){for(var n=[],e=0;e<5;e++)n.push({id:"search-".concat(e),title:"关于“".concat(t,"”的帖子标题 ").concat(e+1),username:"用户".concat(Math.floor(1e3*Math.random())),userAvatar:"https://picsum.photos/100/100?random=".concat(e),coverImage:"https://picsum.photos/300/400?random=".concat(e),likeCount:Math.floor(100*Math.random()),commentCount:Math.floor(20*Math.random())});return n}}};n.default=o}},[["5cab","common/runtime","common/vendor"]]]);