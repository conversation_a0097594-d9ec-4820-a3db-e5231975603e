(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/message/followers"],{"166b":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("cff9");a(n("3240"));var o=a(n("61ee"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},1763:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={uAvatar:function(){return n.e("components/uview-ui/components/u-avatar/u-avatar").then(n.bind(null,"8d35"))},uTag:function(){return n.e("components/uview-ui/components/u-tag/u-tag").then(n.bind(null,"24b8"))},uEmpty:function(){return n.e("components/uview-ui/components/u-empty/u-empty").then(n.bind(null,"8fa1"))},uLoading:function(){return n.e("components/uview-ui/components/u-loading/u-loading").then(n.bind(null,"2bf0"))}},o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.messageList&&e.messageList.length>0),a=n?e.__map(e.messageList,(function(t,n){var a=e.__get_orig(t),o=e.formatTime(t.createTime),r=t.userTags&&t.userTags.length>0,s={id:t.userId,nickname:t.userName};return{$orig:a,m0:o,g1:r,a0:s}})):null,o=e.hasMore&&e.messageList&&e.messageList.length>0;e.$mp.data=Object.assign({},{$root:{g0:n,l0:a,g2:o}})},r=[]},2250:function(e,t,n){"use strict";(function(e){var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("7eb4")),r=a(n("af34")),s=a(n("ee10")),i=n("41ad"),u={name:"NewFollowers",components:{FollowButton:function(){n.e("pagesSub/social/components/FollowButton").then(function(){return resolve(n("607e"))}.bind(null,n)).catch(n.oe)}},data:function(){return{isRefreshing:!1,hasMore:!0,page:1,pageSize:20,loading:!1,messageList:[],followBtnStyle:{width:"120rpx",height:"60rpx",fontSize:"24rpx"},followedBtnStyle:{width:"120rpx",height:"60rpx",fontSize:"24rpx",color:"#999",borderColor:"#e4e7ed"}}},onLoad:function(){this.loadMessages()},methods:{formatTime:function(e){var t=new Date,n=t-e,a=Math.floor(n/6e4),o=Math.floor(n/36e5),r=Math.floor(n/864e5);return a<60?"".concat(a,"分钟前关注了你"):o<24?"".concat(o,"小时前关注了你"):"".concat(r,"天前关注了你")},openUserProfile:function(t){var n=this.messageList.find((function(e){return e.userId===t}));n&&(n.isRead=!0),e.navigateTo({url:"/pagesSub/social/user/profile?userId=".concat(t,"&name=").concat(n?n.userName:"")})},onUserFollow:function(e){console.log("用户关注成功:",e),this.updateUserFollowStatus(e.user.id,!0)},onUserUnfollow:function(e){console.log("用户取消关注成功:",e),this.updateUserFollowStatus(e.user.id,!1)},onFollowChange:function(e){console.log("关注状态变化:",e),this.updateUserFollowStatus(e.user.id,e.isFollowed)},updateUserFollowStatus:function(e,t){var n=this.messageList.find((function(t){return t.userId===e}));n&&(n.isFollowBack=t)},onRefresh:function(){var e=this;this.isRefreshing=!0,this.page=1,this.loadMessages().finally((function(){e.isRefreshing=!1}))},loadMore:function(){this.hasMore&&(this.page++,this.loadMessages())},loadMessages:function(){var t=this;return(0,s.default)(o.default.mark((function n(){var a,s,u,l,c;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!t.loading){n.next=2;break}return n.abrupt("return");case 2:if(n.prev=2,t.loading=!0,a=t.getCurrentUserId(),a){n.next=8;break}return console.error("用户未登录，无法加载粉丝列表"),n.abrupt("return");case 8:return console.log("开始加载新粉丝列表..."),n.next=11,(0,i.getFollowersList)(a,{current:t.page,size:t.pageSize});case 11:s=n.sent,console.log("新粉丝API返回:",s),s&&0===s.code&&s.data?(u=Array.isArray(s.data)?s.data:s.data.records||[],l=u.map((function(e){return{id:e.userId||e.id,userId:e.userId||e.id,userName:e.nickname||"用户",userAvatar:t.formatAvatarUrl(e.avatar),userDesc:e.bio||"这个人很懒，什么都没有留下",userTags:t.parseUserTags(e.danceType),createTime:new Date(e.followTime||e.createTime||Date.now()),isRead:Math.random()>.5,isFollowBack:e.isFollowed||!1}})),1===t.page?t.messageList=l:(c=t.messageList).push.apply(c,(0,r.default)(l)),t.hasMore=u.length>=t.pageSize):(console.error("新粉丝API返回格式不正确:",s),1===t.page&&(t.messageList=[])),n.next=20;break;case 16:n.prev=16,n.t0=n["catch"](2),console.error("加载新粉丝失败:",n.t0),e.showToast({title:"加载失败，请重试",icon:"none"});case 20:return n.prev=20,t.loading=!1,n.finish(20);case 23:case"end":return n.stop()}}),n,null,[[2,16,20,23]])})))()},getCurrentUserId:function(){return e.getStorageSync("userid")},formatAvatarUrl:function(e){return e?e.startsWith("http")||e.startsWith("/")?e:"/".concat(e):"/static/images/toux.png"},parseUserTags:function(e){return e?[e]:[]}}};t.default=u}).call(this,n("df3c")["default"])},"61ee":function(e,t,n){"use strict";n.r(t);var a=n("1763"),o=n("f7a3");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("9f0e");var s=n("828b"),i=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"028c0d17",null,!1,a["a"],void 0);t["default"]=i.exports},7490:function(e,t,n){},"9f0e":function(e,t,n){"use strict";var a=n("7490"),o=n.n(a);o.a},f7a3:function(e,t,n){"use strict";n.r(t);var a=n("2250"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a}},[["166b","common/runtime","common/vendor"]]]);