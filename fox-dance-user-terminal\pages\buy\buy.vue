<template>
	<view class="buy" :style="{ '--qjbutton-color': qjbutton }">
		
			<view class="buy_one">
				<!-- <view :class="bigType == 0 ? 'buy_one_ac' : ''" @click="bigTab(0)">会员卡</view> -->
				<view :class="bigType == 2 ? 'buy_one_ac' : ''" @click="bigTab(2)">周边商城</view>
				<view :class="bigType == 1 ? 'buy_one_ac' : ''" @click="bigTab(1)">课包</view>
			</view>
			
			<!-- 会员卡 -->
			<membershipCard v-show="bigType == 0" ref="membershipCardRef" />
			<!-- 会员卡 -->
			
			<!-- 课包 -->
			<coursePackage v-show="bigType == 1" ref="coursePackageRef" />
			<!-- 课包 -->
			
			<!-- 积分商城 -->
			<pointsMall v-show="bigType == 2" ref="pointsMallRef" />
			<!-- 积分商城 -->

			<tabbar ref="tabbar" :current="1"></tabbar>
		
	</view>
</template>


<script>
import tabbar from '@/components/tabbar.vue'
import membershipCard from './membershipCard'
import coursePackage from '../buy/coursePackage/coursePackage.vue'
import pointsMall from '../buy/pointsMall/pointsMall.vue'

export default {
	components: {
		tabbar,
		membershipCard,
		coursePackage,
		pointsMall
	},                                                                          
	data() {
		return {
			bigType:2,
			bgColor:'#fff',
			qjbutton:'#131315',
		}
	},
	onShow() {
		// 安全获取storeInfo，避免undefined错误
		const storeInfo = uni.getStorageSync('storeInfo')
		this.qjbutton = (storeInfo && storeInfo.button) ? storeInfo.button : '#131315'

		// 使用$nextTick确保组件已挂载，并添加安全检查
		this.$nextTick(() => {
			if (this.$refs.tabbar && typeof this.$refs.tabbar.setColor === 'function') {
				this.$refs.tabbar.setColor();
			}
		})

		console.log(this.qjbutton,'this.qjbutton')
		uni.hideTabBar()

		if(this.bigType == 0){
			if (this.$refs.membershipCardRef && typeof this.$refs.membershipCardRef.onLoadData === 'function') {
				this.$refs.membershipCardRef.onLoadData();
			}
		}
		if(this.bigType == 1){
			if (this.$refs.coursePackageRef && typeof this.$refs.coursePackageRef.onLoadData === 'function') {
				this.$refs.coursePackageRef.onLoadData();
			}
		}
		if(this.bigType == 2){
			if (this.$refs.pointsMallRef && typeof this.$refs.pointsMallRef.onLoadData === 'function') {
				this.$refs.pointsMallRef.onLoadData();
			}
		}
		if(uni.getStorageSync('qbtz')){
			uni.removeStorageSync('qbtz')
			this.bigType = 1;
			if (this.$refs.coursePackageRef && typeof this.$refs.coursePackageRef.onLoadData === 'function') {
				this.$refs.coursePackageRef.onLoadData();
			}
		}
	},
	onLoad() {
		uni.hideTabBar()
	},
	onPageScroll(e) {
		if(this.bigType == 2){
			if (this.$refs.pointsMallRef && typeof this.$refs.pointsMallRef.onPageScrollData === 'function') {
				this.$refs.pointsMallRef.onPageScrollData(e.scrollTop)
			}
		}
	},
	onReachBottom() {
		console.log('到底了');
		if(this.bigType == 1){
			if (this.$refs.coursePackageRef && typeof this.$refs.coursePackageRef.onReachBottomData === 'function') {
				this.$refs.coursePackageRef.onReachBottomData();
			}
		}
		if(this.bigType == 2){
			if (this.$refs.pointsMallRef && typeof this.$refs.pointsMallRef.onReachBottomData === 'function') {
				this.$refs.pointsMallRef.onReachBottomData();
			}
		}
	},
	methods: {
		bigTab(index){
			this.bigType = index;
			if(this.bigType == 0){
				if (this.$refs.membershipCardRef && typeof this.$refs.membershipCardRef.onLoadData === 'function') {
					this.$refs.membershipCardRef.onLoadData();
				}
			}
			if(this.bigType == 1){
				if (this.$refs.coursePackageRef && typeof this.$refs.coursePackageRef.onLoadData === 'function') {
					this.$refs.coursePackageRef.onLoadData();
				}
			}
			if(this.bigType == 2){
				if (this.$refs.pointsMallRef && typeof this.$refs.pointsMallRef.onLoadData === 'function') {
					this.$refs.pointsMallRef.onLoadData();
				}
			}
		}
	}
}
</script>

<style lang="scss">
.buy{overflow:hidden;}
</style>