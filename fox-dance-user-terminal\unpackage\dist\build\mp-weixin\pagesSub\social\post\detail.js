(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/post/detail"],{"178f":function(e,t,o){"use strict";var n=o("1a26"),a=o.n(n);a.a},"1a26":function(e,t,o){},"4d21":function(e,t,o){"use strict";(function(e){var n=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a,s=n(o("7eb4")),r=n(o("7ca3")),i=n(o("3b2d")),c=n(o("ee10")),l=o("41ad"),u={name:"PostDetail",components:{FollowButton:function(){o.e("pagesSub/social/components/FollowButton").then(function(){return resolve(o("607e"))}.bind(null,o)).catch(o.oe)}},data:function(){return{postId:"",postData:{},commentList:[],commentText:"",sortType:"time",currentTab:0,tabList:[{name:"最新"},{name:"最热"}],currentUser:{id:null,avatar:"/static/images/toux.png",nickname:"加载中..."},replyingTo:null,isReplyMode:!1,currentReply:null,inputPlaceholder:"写评论...",showMorePopup:!1,currentMoreComment:null}},onLoad:function(e){console.log("详情页接收到的参数:",e),this.postId=e.id||e.postId||"7",console.log("最终使用的帖子ID:",this.postId),this.postId=String(this.postId),this.loadCurrentUser(),this.loadPostDetail(),this.loadComments()},methods:(a={loadCurrentUser:function(){var t=this;return(0,c.default)(s.default.mark((function o(){var n,a,r;return s.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(o.prev=0,n=e.getStorageSync("userid"),n){o.next=5;break}return console.log("用户未登录，使用默认头像"),o.abrupt("return");case 5:return console.log("加载当前用户信息，ID:",n),o.next=8,(0,l.getUserProfile)(n);case 8:a=o.sent,console.log("当前用户信息API返回:",a),a&&0===a.code&&a.data?(r=a.data,t.currentUser={id:r.id,avatar:r.avatar?r.avatar.startsWith("http")?r.avatar:"https://file.foxdance.com.cn"+r.avatar:"/static/images/toux.png",nickname:r.nickname||"用户"},console.log("当前用户信息加载成功:",t.currentUser)):console.error("获取用户信息失败:",a),o.next=16;break;case 13:o.prev=13,o.t0=o["catch"](0),console.error("加载当前用户信息失败:",o.t0);case 16:case"end":return o.stop()}}),o,null,[[0,13]])})))()},loadPostDetail:function(){var t=this;return(0,c.default)(s.default.mark((function o(){var n,a;return s.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(o.prev=0,console.log("加载帖子详情，ID:",t.postId),console.log("postId类型:",(0,i.default)(t.postId)),t.postId){o.next=5;break}throw new Error("帖子ID为空");case 5:return o.next=7,(0,l.getPostDetail)(t.postId);case 7:if(n=o.sent,console.log("帖子详情API返回:",n),!n||0!==n.code||!n.data){o.next=17;break}a=n.data,console.log("🔥 API返回的原始帖子数据:",a),console.log("🔥 API返回的isLiked值:",a.isLiked,"类型:",(0,i.default)(a.isLiked)),t.postData={id:a.id,userId:a.userId,username:a.nickname||"无名氏",userAvatar:"https://file.foxdance.com.cn"+a.avatar||!1,title:a.title||"",content:a.content,images:a.images||[],topics:a.tags||[],location:a.locationName||"",locationAddress:a.locationAddress||"",locationLatitude:a.locationLatitude||null,locationLongitude:a.locationLongitude||null,likeCount:a.likeCount||0,commentCount:a.commentCount||0,shareCount:a.shareCount||0,isLiked:Boolean(a.isLiked),isFollowed:a.isFollowed||!1,createTime:new Date(a.createTime)},console.log("帖子数据加载成功:",t.postData),o.next=19;break;case 17:throw console.error("API返回数据格式错误:",n),new Error("获取帖子详情失败 - API返回格式错误");case 19:o.next=26;break;case 21:o.prev=21,o.t0=o["catch"](0),console.error("加载帖子详情失败:",o.t0),e.showToast({title:"帖子加载失败",icon:"none"}),setTimeout((function(){e.navigateBack()}),1500);case 26:case"end":return o.stop()}}),o,null,[[0,21]])})))()},loadComments:function(){var t=this;return(0,c.default)(s.default.mark((function o(){var n,a;return s.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.prev=0,console.log("加载帖子评论列表，帖子ID:",t.postId),o.next=4,(0,l.getPostComments)(t.postId,{userId:1,filter:"time"===t.sortType?"latest":"hot",current:1,pageSize:50});case 4:n=o.sent,console.log("帖子评论列表API返回:",n),n&&0===n.code&&n.data?(a=[],n.data.comments?a=n.data.comments:Array.isArray(n.data)&&(a=n.data),t.commentList=a.map((function(e){return{id:e.id,userId:e.userId,username:e.nickname||"无名氏",userAvatar:"https://file.foxdance.com.cn"+e.avatar||!1,content:e.content,likeCount:e.likes||0,isLiked:e.isLiked||!1,level:e.level||0,createTime:new Date(e.createdAt||e.createTime),replies:(e.replies||[]).map((function(e){return{id:e.id,userId:e.userId,username:e.nickname||"无名氏",userAvatar:e.avatar?e.avatar.startsWith("http")?e.avatar:"https://file.foxdance.com.cn"+e.avatar:"/static/images/toux.png",content:e.content,likeCount:e.likes||0,isLiked:e.isLiked||!1,replyTo:e.replyTo?{userId:e.replyTo.id,username:e.replyTo.nickname||"用户"}:null,createTime:new Date(e.createdAt||e.createTime)}})),replyCount:e.replyCount||0}})),console.log("评论列表加载成功:",t.commentList.length)):(console.log("评论API返回格式不正确或无数据"),t.commentList=[]),o.next=14;break;case 9:o.prev=9,o.t0=o["catch"](0),console.error("加载评论失败:",o.t0),e.showToast({title:"评论加载失败",icon:"none"}),t.commentList=[];case 14:case"end":return o.stop()}}),o,null,[[0,9]])})))()},formatTime:function(e){var t=new Date,o=t-new Date(e),n=Math.floor(o/6e4),a=Math.floor(o/36e5),s=Math.floor(o/864e5);return n<60?"".concat(n,"分钟前"):a<24?"".concat(a,"小时前"):"".concat(s,"天前")},goBack:function(){e.navigateBack()},goUserProfile:function(){this.postData.userId?(console.log("跳转到帖子作者主页，用户ID:",this.postData.userId),e.navigateTo({url:"/pagesSub/social/user/profile?id=".concat(this.postData.userId)})):e.showToast({title:"用户信息错误",icon:"none"})},goCommentUserProfile:function(t){t.userId?(console.log("跳转到评论用户主页，用户ID:",t.userId,"用户名:",t.username),e.navigateTo({url:"/pagesSub/social/user/profile?id=".concat(t.userId)})):e.showToast({title:"用户信息错误",icon:"none"})},goReplyUserProfile:function(t){t.userId?(console.log("跳转到回复用户主页，用户ID:",t.userId,"用户名:",t.username),e.navigateTo({url:"/pagesSub/social/user/profile?id=".concat(t.userId)})):e.showToast({title:"用户信息错误",icon:"none"})},goReplyToUserProfile:function(t){t.userId?(console.log("跳转到被回复用户主页，用户ID:",t.userId,"用户名:",t.username),e.navigateTo({url:"/pagesSub/social/user/profile?id=".concat(t.userId)})):e.showToast({title:"用户信息错误",icon:"none"})},onUserFollow:function(e){console.log("关注操作:",e)},onFollowChange:function(e){this.postData.isFollowed=e.isFollowed,console.log("关注状态变化:",e)},toggleLike:function(){var t=this;return(0,c.default)(s.default.mark((function o(){var n,a,r,c,u,d;return s.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(console.log("=== 点赞功能开始 ==="),console.log("点赞按钮被点击"),console.log("当前帖子数据:",t.postData),e.showToast({title:"点赞功能被触发",icon:"none",duration:2e3}),t.postData&&t.postData.id){o.next=8;break}return console.error("帖子数据无效:",t.postData),e.showToast({title:"帖子信息错误",icon:"none"}),o.abrupt("return");case 8:if(n=t.postData.isLiked,a=t.postData.likeCount||0,o.prev=10,t.postData.isLiked=!t.postData.isLiked,t.postData.likeCount=t.postData.isLiked?a+1:Math.max(a-1,0),console.log("点赞操作 - 帖子ID:",t.postData.id,"类型:",(0,i.default)(t.postData.id),"操作:",t.postData.isLiked?"点赞":"取消点赞"),r=Number(t.postData.id),c=Number(e.getStorageSync("userid")),console.log("🔥 点赞操作详情:"),console.log("  - postId:",r,"userId:",c),console.log("  - 原始状态 isLiked:",n,"likeCount:",a),console.log("  - 新状态 isLiked:",t.postData.isLiked,"likeCount:",t.postData.likeCount),c){o.next=26;break}return console.error("用户未登录"),e.showToast({title:"请先登录",icon:"none"}),t.postData.isLiked=n,t.postData.likeCount=a,o.abrupt("return");case 26:if(!t.postData.isLiked){o.next=33;break}return console.log("调用点赞API"),o.next=30,(0,l.likePost)(r,c);case 30:u=o.sent,o.next=37;break;case 33:return console.log("调用取消点赞API"),o.next=36,(0,l.unlikePost)(r,c);case 36:u=o.sent;case 37:console.log("点赞API返回:",u),u&&0===u.code?(console.log("点赞操作成功"),e.showToast({title:t.postData.isLiked?"点赞成功":"取消点赞",icon:"success",duration:1e3})):(console.warn("点赞API调用失败:",u),t.postData.isLiked=n,t.postData.likeCount=a,e.showToast({title:(null===(d=u)||void 0===d?void 0:d.message)||"操作失败，请重试",icon:"none"})),o.next=47;break;case 41:o.prev=41,o.t0=o["catch"](10),console.error("点赞操作失败:",o.t0),t.postData.isLiked=n,t.postData.likeCount=a,e.showToast({title:"网络错误，请重试",icon:"none"});case 47:case"end":return o.stop()}}),o,null,[[10,41]])})))()},toggleCommentLike:function(e){e.isLiked=!e.isLiked,e.likeCount+=e.isLiked?1:-1},previewImage:function(t){e.previewImage({urls:this.postData.images,current:t})},sharePost:function(){var t=this;e.showActionSheet({itemList:["分享到微信","分享到朋友圈","复制链接"],success:function(){t.$u.toast("分享成功"),t.postData.shareCount++}})},goTopic:function(t){e.navigateTo({url:"/pagesSub/social/topic/detail?name=".concat(t)})},openLocation:function(){var t=this;this.postData.location&&(console.log("打开位置信息:",this.postData.location),this.postData.locationLatitude&&this.postData.locationLongitude?e.openLocation({latitude:Number(this.postData.locationLatitude),longitude:Number(this.postData.locationLongitude),name:this.postData.location,address:this.postData.locationAddress||this.postData.location,success:function(){console.log("打开地图成功")},fail:function(o){console.error("打开地图失败:",o),e.showModal({title:"位置信息",content:t.postData.location,showCancel:!1})}}):e.showModal({title:"位置信息",content:this.postData.location,showCancel:!1}))},changeSortType:function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];console.log("tabs点击参数:",t);var n="number"===typeof t[0]?t[0]:t[0]&&void 0!==t[0].index?t[0].index:0;this.currentTab=n,this.sortType=0===n?"time":"hot",this.loadComments(),this.loadComments()},focusComment:function(){this.$refs.commentInput.focus()},onInputFocus:function(){},onInputBlur:function(){},replyComment:function(e){var t=this;this.isReplyMode=!0,this.currentReply=e,this.inputPlaceholder="@".concat(e.username),this.$nextTick((function(){t.$refs.commentInput&&t.$refs.commentInput.focus()}))},sendComment:function(){var t=this;return(0,c.default)(s.default.mark((function o(){return s.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(t.commentText.trim()){o.next=3;break}return e.showToast({title:"请输入评论内容",icon:"none"}),o.abrupt("return");case 3:if(o.prev=3,console.log("发送评论，帖子ID:",t.postId,"内容:",t.commentText,"回复模式:",t.isReplyMode),!t.isReplyMode||!t.currentReply){o.next=10;break}return o.next=8,t.sendReplyComment();case 8:o.next=12;break;case 10:return o.next=12,t.sendNormalComment();case 12:o.next=18;break;case 14:o.prev=14,o.t0=o["catch"](3),console.error("发送评论失败:",o.t0),e.showToast({title:"发送失败",icon:"none"});case 18:case"end":return o.stop()}}),o,null,[[3,14]])})))()},sendNormalComment:function(){var e=this;return(0,c.default)(s.default.mark((function t(){var o,n,a;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return o={postId:String(e.postId),content:e.commentText.trim(),userId:1},console.log("发送普通评论数据:",o),t.prev=2,t.next=5,(0,l.createPostComment)(o);case 5:if(n=t.sent,console.log("普通评论API返回:",n),!n||0!==n.code){t.next=10;break}return e.handleCommentSuccess(),t.abrupt("return");case 10:t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](2),console.warn("普通评论API调用失败，使用临时方案:",t.t0);case 15:console.log("使用临时评论方案"),a={id:Date.now(),userId:e.currentUser.id||1,username:e.currentUser.nickname||"当前用户",userAvatar:e.currentUser.avatar||"/static/images/toux.png",content:e.commentText.trim(),likeCount:0,isLiked:!1,level:0,createTime:new Date,replyCount:0,replies:[]},e.commentList.unshift(a),e.handleCommentSuccess();case 19:case"end":return t.stop()}}),t,null,[[2,12]])})))()},sendReplyComment:function(){var e=this;return(0,c.default)(s.default.mark((function t(){var o,n;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return o={content:e.commentText.trim(),userId:1,replyToUserId:e.currentReply.userId,replyToUsername:e.currentReply.username},console.log("发送回复评论数据:",o,"目标评论ID:",e.currentReply.id),t.prev=2,t.next=5,(0,l.replyComment)(e.currentReply.id,o);case 5:if(n=t.sent,console.log("回复评论API返回:",n),!n||0!==n.code){t.next=10;break}return e.handleCommentSuccess(),t.abrupt("return");case 10:t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](2),console.warn("回复评论API调用失败，使用临时方案:",t.t0);case 15:console.log("回复API失败，重新加载评论列表"),e.handleCommentSuccess(),setTimeout((function(){e.loadComments()}),500);case 18:case"end":return t.stop()}}),t,null,[[2,12]])})))()},handleCommentSuccess:function(){var t=this,o=this.isReplyMode;this.commentText="",this.replyingTo=null,this.cancelReplyMode(),o||this.postData.commentCount++,e.showToast({title:o?"回复成功":"评论成功",icon:"success"}),setTimeout((function(){t.loadComments()}),500)}},(0,r.default)(a,"toggleCommentLike",(function(t){return(0,c.default)(s.default.mark((function o(){var n,a,r,i;return s.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(t&&t.id){o.next=3;break}return e.showToast({title:"评论信息错误",icon:"none"}),o.abrupt("return");case 3:if(n=t.isLiked,a=t.likeCount||0,o.prev=5,t.isLiked=!t.isLiked,t.likeCount=t.isLiked?a+1:Math.max(a-1,0),console.log("点赞评论 - 评论ID:",t.id,"操作:",t.isLiked?"点赞":"取消点赞"),r=Number(e.getStorageSync("userid")),r){o.next=16;break}return console.error("用户未登录"),e.showToast({title:"请先登录",icon:"none"}),t.isLiked=n,t.likeCount=a,o.abrupt("return");case 16:return o.next=18,(0,l.likeComment)(t.id,{userId:r,action:t.isLiked?"like":"unlike"});case 18:i=o.sent,console.log("评论点赞API返回:",i),i&&0===i.code?e.showToast({title:t.isLiked?"点赞成功":"取消点赞",icon:"success",duration:1e3}):(console.warn("评论点赞API调用失败:",i),t.isLiked=n,t.likeCount=a,e.showToast({title:"操作失败，请重试",icon:"none"})),o.next=29;break;case 23:o.prev=23,o.t0=o["catch"](5),console.error("评论点赞操作失败:",o.t0),t.isLiked=n,t.likeCount=a,e.showToast({title:"网络错误，请重试",icon:"none"});case 29:case"end":return o.stop()}}),o,null,[[5,23]])})))()})),(0,r.default)(a,"toggleContent",(function(e){this.$set(e,"showFullContent",!e.showFullContent)})),(0,r.default)(a,"getLevelColor",(function(e){var t=["#999","#2979ff","#67C23A","#E6A23C","#F56C6C","#9C27B0"];return t[Math.min(e,t.length-1)]||"#999"})),(0,r.default)(a,"showMoreOptions",(function(e){this.currentMoreComment=e,this.showMorePopup=!0})),(0,r.default)(a,"replyFromMore",(function(){var e=this;this.currentMoreComment&&(this.showMorePopup=!1,setTimeout((function(){e.replyComment(e.currentMoreComment)}),300))})),(0,r.default)(a,"copyComment",(function(){this.currentMoreComment&&(e.setClipboardData({data:this.currentMoreComment.content,success:function(){e.showToast({title:"复制成功",icon:"success"})}}),this.showMorePopup=!1)})),(0,r.default)(a,"deleteComment",(function(){var t=this;this.currentMoreComment&&(e.showModal({title:"确认删除",content:"确定要删除这条评论吗？",success:function(o){if(o.confirm){console.log("删除评论:",t.currentMoreComment.id);var n=t.commentList.findIndex((function(e){return e.id===t.currentMoreComment.id}));n>-1&&(t.commentList.splice(n,1),t.postData.commentCount--),e.showToast({title:"删除成功",icon:"success"})}}}),this.showMorePopup=!1)})),(0,r.default)(a,"reportComment",(function(){e.showToast({title:"举报成功",icon:"success"}),this.showMorePopup=!1})),(0,r.default)(a,"isCommentOwner",(function(e){return e&&e.userId===this.currentUser.id})),(0,r.default)(a,"cancelReplyMode",(function(){this.isReplyMode=!1,this.currentReply=null,this.inputPlaceholder="写评论...",e.showToast({title:"已取消回复",icon:"none",duration:1e3})})),(0,r.default)(a,"viewAllReplies",(function(t){var o=this;return(0,c.default)(s.default.mark((function n(){var a,r,i;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return console.log("查看全部回复:",t.id),n.prev=1,n.next=4,e.request({url:"".concat(o.$http.vote_baseUrl,"/comments/").concat(t.id,"/replies"),method:"GET",data:{userId:1,sort:"latest"}});case 4:a=n.sent,a.data&&0===a.data.code&&(r=a.data.data||[],i=o.commentList.find((function(e){return e.id===t.id})),i&&(i.replies=r.map((function(e){return{id:e.id,userId:e.userId,username:e.nickname||"无名氏",userAvatar:e.avatar?e.avatar.startsWith("http")?e.avatar:"https://file.foxdance.com.cn"+e.avatar:"/static/images/toux.png",content:e.content,likeCount:e.likes||0,isLiked:e.isLiked||!1,replyTo:e.replyTo?{userId:e.replyTo.id,username:e.replyTo.nickname||"用户"}:null,createTime:new Date(e.createdAt||e.createTime)}}))),e.showToast({title:"加载了".concat(r.length,"条回复"),icon:"success"})),n.next=12;break;case 8:n.prev=8,n.t0=n["catch"](1),console.error("获取回复列表失败:",n.t0),e.showToast({title:"加载回复失败",icon:"none"});case 12:case"end":return n.stop()}}),n,null,[[1,8]])})))()})),(0,r.default)(a,"showMoreActions",(function(){e.showActionSheet({itemList:["举报","不感兴趣","屏蔽用户"],success:function(e){console.log("更多操作:",e.tapIndex)}})})),a)};t.default=u}).call(this,o("df3c")["default"])},6162:function(e,t,o){"use strict";(function(e,t){var n=o("47a9");o("cff9");n(o("3240"));var a=n(o("991f"));e.__webpack_require_UNI_MP_PLUGIN__=o,t(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"991f":function(e,t,o){"use strict";o.r(t);var n=o("d70d"),a=o("ce8d");for(var s in a)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(s);o("178f");var r=o("828b"),i=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"2c523c20",null,!1,n["a"],void 0);t["default"]=i.exports},ce8d:function(e,t,o){"use strict";o.r(t);var n=o("4d21"),a=o.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(s);t["default"]=a.a},d70d:function(e,t,o){"use strict";o.d(t,"b",(function(){return a})),o.d(t,"c",(function(){return s})),o.d(t,"a",(function(){return n}));var n={uAvatar:function(){return o.e("components/uview-ui/components/u-avatar/u-avatar").then(o.bind(null,"8d35"))},uIcon:function(){return o.e("components/uview-ui/components/u-icon/u-icon").then(o.bind(null,"8398"))},uTabs:function(){return o.e("components/uview-ui/components/u-tabs/u-tabs").then(o.bind(null,"6916"))},uPopup:function(){return o.e("components/uview-ui/components/u-popup/u-popup").then(o.bind(null,"b525"))}},a=function(){var e=this,t=e.$createElement,o=(e._self._c,e.formatTime(e.postData.createTime)),n={id:e.postData.userId,nickname:e.postData.username},a=e.postData.topics&&e.postData.topics.length,s=e.postData.images&&e.postData.images.length,r=s?e.postData.images.length:null,i=e.commentList.length,c=e.__map(e.commentList,(function(t,o){var n=e.__get_orig(t),a=t.level>=0?e.getLevelColor(t.level):null,s=e.formatTime(t.createTime),r=t.showFullContent?null:t.content.length,i=!t.showFullContent&&r>100?t.content.slice(0,100):null,c=t.content.length,l=t.replies&&t.replies.length>0,u=l?e.__map(t.replies.slice(0,2),(function(t,o){var n=e.__get_orig(t),a=t.content.length,s=a>50?t.content.slice(0,50):null;return{$orig:n,g8:a,g9:s}})):null;return{$orig:n,m1:a,m2:s,g4:r,g5:i,g6:c,g7:l,l0:u}})),l=e.commentText.trim(),u=e.isCommentOwner(e.currentMoreComment);e.$mp.data=Object.assign({},{$root:{m0:o,a0:n,g0:a,g1:s,g2:r,g3:i,l1:c,g10:l,m3:u}})},s=[]}},[["6162","common/runtime","common/vendor"]]]);