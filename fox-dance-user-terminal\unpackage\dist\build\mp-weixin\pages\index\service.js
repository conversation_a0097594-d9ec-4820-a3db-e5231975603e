(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/service"],{"3ab1":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},i=[]},"419f":function(t,e,n){},"59f5":function(t,e,n){"use strict";(function(t,e){var o=n("47a9");n("cff9");o(n("3240"));var i=o(n("e275"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"7bd9":function(t,e,n){"use strict";n.r(e);var o=n("8b2d"),i=n.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(s);e["default"]=i.a},"8b2d":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n("6061"),i={data:function(){return{isLogined:!0,loding:!1,servLists:[],userInfo:{},kefuInfo:{to_user_id:0,to_user_avatar:"",to_user_nickname:"",wechat_number:"",wechat_or_code:""},store_id:0,imgbaseUrl:"",msn:"",iscxhh:!1}},onShow:function(){this.imgbaseUrl=this.$baseUrl},onLoad:function(e){this.store_id=e.id,this.serData(),this.kefuzRecordData("chudi"),this.userData(),t.onSocketMessage((function(t){console.log("收到服务器内容："+t.data,t)}))},methods:{cxfqTap:function(){this.serData(),this.kefuzRecordData("chudi")},sendTap:function(){if(0==this.msn.split(" ").join("").length)return t.showToast({icon:"none",title:"请输入要发送的内容",duration:2e3}),!1;if(this.iscxhh)return t.showToast({icon:"none",title:"当前会话已断开，请重新连接会话",duration:2e3}),!1;t.showLoading({title:"加载中"});var e=this;(0,o.sendMessageApi)({to_user_id:e.kefuInfo.to_user_id,msn_type:1,msn:e.msn,user_id:t.getStorageSync("userid"),store_id:e.store_id}).then((function(n){console.log("发送消息",n),200==n.status&&(e.msn="",t.hideLoading(),e.kefuzRecordData("chudi"))}))},userData:function(){t.showLoading({title:"加载中"});var e=this;(0,o.userInfoApi)({}).then((function(n){console.log("个人中心",n),1==n.code&&(e.userInfo=n.data,t.hideLoading())}))},kefuzRecordData:function(e){t.showLoading({title:"加载中"});var n=this;(0,o.kefuzRecordApi)({uid:t.getStorageSync("userid"),store_id:n.store_id,page:1,limit:9999}).then((function(o){console.log("获取聊天记录",o),t.hideLoading(),200==o.status&&(n.servLists=o.data.serviceList,n.loding=!0,e&&setTimeout((function(){t.pageScrollTo({scrollTop:999999},0)}),200)),n.iscxhh=200!=o.status}))},serData:function(){var e=this,n=t.getStorageSync("server_token");t.connectSocket({url:"wss://dancekefu.xinzhiyukeji.cn/ws?token=".concat(n,"&type=user&form=pc"),success:function(t){console.log(t,"success"),e.kefuzrgData()},fail:function(t){console.log(t,"fail")},complete:function(t){console.log(t,"complete")}})},kefuzrgData:function(){t.showLoading({title:"加载中"});var e=this;t.getStorageSync("server_token");console.log(e.store_id,"that.store_id1111"),(0,o.kefuzrgApi)({uid:t.getStorageSync("userid"),store_id:e.store_id}).then((function(t){console.log("转人工连接客服",t),200==t.status&&(e.kefuInfo=t.data)}))},openImg:function(e){t.previewImage({current:0,urls:[e]})},copyText:function(e){t.setClipboardData({data:e,success:function(){t.showToast({title:"复制成功",icon:"success",duration:2e3})}})},navTo:function(e){t.navigateTo({url:e})}}};e.default=i}).call(this,n("df3c")["default"])},b392:function(t,e,n){"use strict";var o=n("419f"),i=n.n(o);i.a},e275:function(t,e,n){"use strict";n.r(e);var o=n("3ab1"),i=n("7bd9");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("b392");var a=n("828b"),c=Object(a["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=c.exports}},[["59f5","common/runtime","common/vendor"]]]);