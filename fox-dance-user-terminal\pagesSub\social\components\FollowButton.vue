<template>
  <u-button
    :type="isFollowed ? 'default' : 'primary'"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :customStyle="buttonStyle"
    @click="handleFollow"
  >
    {{ isFollowed ? '已关注' : '关注' }}
  </u-button>
</template>

<script>
import { followUser, unfollowUser, checkFollowStatus } from '@/utils/socialApi.js'

export default {
  name: 'FollowButton',
  props: {
    // 用户信息
    user: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 关注状态
    followed: {
      type: Boolean,
      default: false
    },
    // 按钮尺寸
    size: {
      type: String,
      default: 'mini',
      validator: (value) => ['large', 'normal', 'mini'].includes(value)
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 自定义样式
    customStyle: {
      type: [String, Object],
      default: ''
    },
    // 是否显示加载状态
    showLoading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isFollowed: false,
      loading: false
    }
  },
  computed: {
    buttonStyle() {
      const baseStyle = {
        fontSize: this.getFontSize(),
        height: this.getHeight(),
        minWidth: this.getMinWidth(),
        borderRadius: this.getBorderRadius()
      }
      
      if (typeof this.customStyle === 'string') {
        return `${this.customStyle}; ${this.objectToStyle(baseStyle)}`
      } else if (typeof this.customStyle === 'object') {
        return { ...baseStyle, ...this.customStyle }
      }
      
      return baseStyle
    }
  },
  watch: {
    followed: {
      immediate: true,
      handler(newVal) {
        this.isFollowed = newVal
      }
    }
  },
  methods: {
    async handleFollow() {
      if (this.disabled || this.loading) return
      
      if (this.showLoading) {
        this.loading = true
      }
      
      try {
        const currentUserId = uni.getStorageSync('userid')
        if (!currentUserId) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          })
          return
        }

        const targetUserId = this.user.id || this.user.userId
        if (!targetUserId) {
          uni.showToast({
            title: '用户信息错误',
            icon: 'none'
          })
          return
        }

        if (currentUserId == targetUserId) {
          uni.showToast({
            title: '不能关注自己',
            icon: 'none'
          })
          return
        }

        let result
        if (this.isFollowed) {
          // 取消关注
          result = await unfollowUser(targetUserId)
          if (result && result.code === 0) {
            this.isFollowed = false
            uni.showToast({
              title: '已取消关注',
              icon: 'success'
            })
            // 触发事件通知父组件
            this.$emit('unfollow', {
              user: this.user,
              isFollowed: false
            })
          } else {
            throw new Error(result?.message || '取消关注失败')
          }
        } else {
          // 关注
          result = await followUser(targetUserId)
          if (result && result.code === 0) {
            this.isFollowed = true
            uni.showToast({
              title: '关注成功',
              icon: 'success'
            })
            // 触发事件通知父组件
            this.$emit('follow', {
              user: this.user,
              isFollowed: true
            })
          } else {
            throw new Error(result?.message || '关注失败')
          }
        }

        // 触发状态变化事件
        this.$emit('change', {
          user: this.user,
          isFollowed: this.isFollowed
        })

      } catch (error) {
        console.error('关注操作失败:', error)

        uni.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        })

        // 触发错误事件
        this.$emit('error', {
          user: this.user,
          error: error
        })
      } finally {
        if (this.showLoading) {
          setTimeout(() => {
            this.loading = false
          }, 500) // 延迟500ms隐藏加载状态，提供更好的用户体验
        }
      }
    },
    
    getFontSize() {
      const sizeMap = {
        large: '32rpx',
        normal: '28rpx',
        mini: '24rpx'
      }
      return sizeMap[this.size] || sizeMap.mini
    },
    
    getHeight() {
      const heightMap = {
        large: '80rpx',
        normal: '64rpx',
        mini: '56rpx'
      }
      return heightMap[this.size] || heightMap.mini
    },
    
    getMinWidth() {
      const widthMap = {
        large: '120rpx',
        normal: '100rpx',
        mini: '80rpx'
      }
      return widthMap[this.size] || widthMap.mini
    },
    
    getBorderRadius() {
      const radiusMap = {
        large: '40rpx',
        normal: '32rpx',
        mini: '28rpx'
      }
      return radiusMap[this.size] || radiusMap.mini
    },
    
    objectToStyle(obj) {
      return Object.keys(obj).map(key => {
        const kebabKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()
        return `${kebabKey}: ${obj[key]}`
      }).join('; ')
    },
    
    // 外部调用方法：手动设置关注状态
    setFollowStatus(status) {
      this.isFollowed = status
    },
    
    // 外部调用方法：获取当前关注状态
    getFollowStatus() {
      return this.isFollowed
    }
  }
}
</script>

<style lang="scss" scoped>
// 组件内部不需要额外样式，所有样式通过uview组件和customStyle处理
</style>
