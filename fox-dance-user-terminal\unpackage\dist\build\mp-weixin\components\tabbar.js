(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/tabbar"],{"2aba":function(t,e,i){"use strict";i.r(e);var o=i("ca45"),a=i.n(o);for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);e["default"]=a.a},3005:function(t,e,i){"use strict";var o=i("7c4e"),a=i.n(o);a.a},"7c4e":function(t,e,i){},ca45:function(t,e,i){"use strict";(function(t,o){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"tabbar",components:{PreviewCard:function(){i.e("components/PreviewCard").then(function(){return resolve(i("e818"))}.bind(null,i)).catch(i.oe)}},props:{current:{type:[Number],default:0}},data:function(){return{list:[{iconPath:"/static/tabbar/tab_home.png",selectedIconPath:"/static/tabbar/tab_home_x.png",icon:"home",text:"首页",count:0,isDot:!1,customIcon:!1,pagePath:"/pages/index/index"},{iconPath:"/static/tabbar/tab_buy.png",selectedIconPath:"/static/tabbar/tab_buy_x.png",icon:"bag",text:"购买",count:0,isDot:!1,customIcon:!1,pagePath:"/pages/buy/buy"},{iconPath:"/static/tabbar/tab_fox1.png",selectedIconPath:"/static/tabbar/tab_fox1.png",text:"作品",count:0,isDot:!1,customIcon:!1,pagePath:"/pages/index/index"},{iconPath:"/static/tabbar/tab_schedule.png",selectedIconPath:"/static/tabbar/tab_schedule_x.png",icon:"tags",text:"约课",count:0,isDot:!1,customIcon:!1,pagePath:"/pages/Schedule/Schedule"},{iconPath:"/static/tabbar/tab_mine.png",selectedIconPath:"/static/tabbar/tab_mine_x.png",icon:"account",text:"我的",count:0,isDot:!1,customIcon:!1,pagePath:"/pages/mine/mine"}],bottomHeight2:4,bottomHeight:10,ecology:"",noIndex:0,showBox:!1,showFixed:!1,selColor:t.getStorageSync("storeInfo")?t.getStorageSync("storeInfo").button:"#131315",currentPage:0,showBoundaryHint:!1,totalCards:2,products:[{id:1,name:"新店投票",tag:"Fox - New store voting",image:"https://dance-**********.cos.ap-guangzhou.myqcloud.com/static/images/vote-card-image.jpg",targetPage:"/pagesSub/switch/vote"},{id:2,name:"Fox社区",tag:"Fox - Topic square",image:"https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg",targetPage:"/pagesSub/social/main/index"}],pageScrollLocked:!1,isTabBarHidden:!1,isTabBarReturning:!1,scrollLeft:0,cardWidth:0,windowWidth:0,touchStartX:0,touchStartY:0,touchStartTime:0,touchMoved:!1,isSwiping:!1,swipeThreshold:10,cardTouchTimer:null,touchDirection:"",directionLocked:!1,minSwipeDistance:5,directionThreshold:.5,verticalAngleThreshold:5,horizontalAngleThreshold:85,minVerticalDistance:20,rightCardOffsetX:0,leftCardOffsetX:0,rightCardScale:.85,leftCardScale:.85,rightCardOpacity:.7,leftCardOpacity:.7,isCardPulling:!1,pullDistance:0,isClosing:!1,isCardExiting:!1,cardTipText:"随便点一下，可能会发现新大陆"}},computed:{cardContainerStyle:function(){return this.showBox?{transform:"translateY(-20px)",transition:"transform 450ms cubic-bezier(0.4, 0, 0.2, 1)"}:{}}},created:function(){var e=this,i=this;t.getSystemInfo({success:function(t){t.safeAreaInsets.bottom&&(i.bottomHeight=t.safeAreaInsets.bottom,i.bottomHeight2=t.safeAreaInsets.bottom-7),i.windowWidth=t.windowWidth,i.cardWidth=.8*t.windowWidth}}),t.$on("touchmove",(function(i){if(e.showBox||e.pageScrollLocked)return i.preventDefault&&i.preventDefault(),i.stopPropagation&&i.stopPropagation(),t.pageScrollTo({scrollTop:0,duration:0}),!1})),"undefined"!==typeof this.$scope&&this.$scope&&(this.$scope.preventCardClick=this.preventCardClick,this.$scope.handleContainerClick=this.handleContainerClick,this.$scope.swiperBackgroundClick=this.swiperBackgroundClick)},mounted:function(){var t=this;this.$nextTick((function(){setTimeout((function(){t.showBox&&t.scrollToCard(t.currentPage)}),300)})),"undefined"!==typeof this.$scope&&this.$scope&&(this.$scope.preventCardClick=this.preventCardClick,this.$scope.handleContainerClick=this.handleContainerClick,this.$scope.swiperBackgroundClick=this.swiperBackgroundClick),this.getCardTip()},beforeDestroy:function(){t.$off("touchmove")},methods:{onSwiperChange:function(t){this.currentPage=t.detail.current},setColor:function(e){try{this.ecology=e||"敬请期待~";var i=t.getStorageSync("storeInfo");this.selColor=i&&i.button?i.button:"#131315"}catch(o){console.warn("setColor error:",o),this.ecology="敬请期待~",this.selColor="#131315"}},tabbarChange:function(e,i){var a=this;if(this.noIndex=i,2==i){if(3===this.current||1===this.current){var n=3===this.current?"约课":"购买";return void t.showToast({title:"".concat(n,"页面不可用"),icon:"none",duration:2e3})}this.noIndex=i,this.showBox=!0,this.showFixed=!0,this.isTabBarReturning=!1,this.preventScroll(!0);try{o.setPageStyle({style:{overflow:"hidden"}})}catch(c){}return setTimeout((function(){a.isTabBarHidden=!1}),400),void this.getCardTip()}t.switchTab({url:e.pagePath})},preventScroll:function(t){this.pageScrollLocked=t,getApp().globalData=getApp().globalData||{},getApp().globalData.pageScrollLocked=t;try{o.setPageStyle({style:{overflow:t?"hidden":"auto"}})}catch(e){}},preventTouchMove:function(t){return!(!this.directionDetermined||"horizontal"!==this.initialSwipeDirection)||(this.pageScrollLocked?(t.preventDefault&&t.preventDefault(),t.stopPropagation&&t.stopPropagation(),!1):void 0)},handleCardPulling:function(t){"vertical"===this.touchDirection&&(this.isCardPulling=!0,this.pullDistance=t.distance)},onCardTouchStart:function(t){var e=t.touches[0];this.touchStartX=e.clientX,this.touchStartY=e.clientY,this.touchStartTime=Date.now(),this.touchMoved=!1,this.directionLocked=!1,this.touchDirection="",this.sampledTouches=[],this.initialSwipeDirection="",this.directionDetermined=!1,this.lastDeltaX=0,this.lastDeltaY=0,this.accumulatedHorizontalDistance=0,this.accumulatedVerticalDistance=0,this.lastTouchTime=Date.now(),this.sampledTouches.push({x:e.clientX,y:e.clientY,time:Date.now()})},onCardTouchMove:function(t){this.pageScrollLocked&&(t.preventDefault&&t.preventDefault(),t.stopPropagation&&t.stopPropagation());var e=Date.now();if(!(e-this.lastTouchTime<this.touchThrottleDelay)){this.lastTouchTime=e;var i=t.touches[0],o=i.clientX,a=i.clientY,n=o-this.touchStartX,c=a-this.touchStartY,s=Math.abs(n)-Math.abs(this.lastDeltaX),r=Math.abs(c)-Math.abs(this.lastDeltaY);s>0&&(this.accumulatedHorizontalDistance+=s),r>0&&(this.accumulatedVerticalDistance+=r),this.lastDeltaX=Math.abs(n),this.lastDeltaY=Math.abs(c);var h=Math.sqrt(n*n+c*c);if(!this.directionDetermined&&h>=this.swipeStartDistance){var l=Math.abs(n)/(Math.abs(c)||.1);l>=this.horizontalDirectionThreshold?(this.initialSwipeDirection="horizontal",this.touchDirection="horizontal",this.directionLocked=!0):l<=1/this.horizontalDirectionThreshold?(this.initialSwipeDirection="vertical",this.touchDirection="vertical",this.directionLocked=!0):this.accumulatedHorizontalDistance>this.accumulatedVerticalDistance&&(this.initialSwipeDirection="horizontal",this.touchDirection="horizontal",this.directionLocked=!0),this.directionDetermined=!0}"horizontal"===this.touchDirection&&this.isCardPulling&&(this.isCardPulling=!1,this.pullDistance=0),"vertical"===this.touchDirection&&c>0&&(this.isCardPulling=!0,this.pullDistance=c),this.touchMoved=!0,this.sampledTouches.push({x:o,y:a,time:e}),this.sampledTouches.length>5&&this.sampledTouches.shift()}},onCardTouchEnd:function(t){if(this.touchMoved){Date.now(),this.touchStartTime;var e=0,i=0;if(this.sampledTouches.length>=2){var o=this.sampledTouches[this.sampledTouches.length-1],a=this.sampledTouches[0],n=o.time-a.time;n>0&&(e=(o.x-a.x)/n,i=(o.y-a.y)/n)}var c=Math.abs(e)>.5&&Math.abs(e)>1.5*Math.abs(i);if(c){var s=e<0?1:-1,r=Math.max(0,Math.min(this.products.length-1,this.currentPage+s));r!==this.currentPage&&(this.currentPage=r)}this.isCardPulling&&(this.isCardPulling=!1,this.pullDistance=0),this.directionLocked=!1,this.touchDirection="",this.touchMoved=!1,this.sampledTouches=[],this.directionDetermined=!1,this.initialSwipeDirection=""}},onHomeClick:function(){var t=this;console.log("点击背景关闭预览"),this.isCardExiting=!0,setTimeout((function(){t.preventScroll(!1);try{o.setPageStyle({style:{overflow:"auto"}})}catch(e){}t.showBox=!1,t.showFixed=!1,t.isCardExiting=!1,t.isTabBarReturning=!0,t.isTabBarHidden=!1,t.currentPage=0,t.noIndex=t.current,setTimeout((function(){t.isTabBarReturning=!1}),400)}),300)},handleDotTap:function(t){this.currentPage=t},handleContainerClick:function(){console.log("容器点击关闭预览"),this.onHomeClick()},preventCardClick:function(){return console.log("卡片点击，阻止冒泡"),!1},swiperBackgroundClick:function(){console.log("swiper背景点击，关闭预览"),t.showToast({title:"点击了背景区域",icon:"none"}),this.onHomeClick()},closePreview:function(){this.onHomeClick()},getBaseUrl:function(){return"https://vote.foxdance.com.cn"},getCardTip:function(){var e=this,i=this.getBaseUrl();t.request({url:"".concat(i,"/api/vote-info/1/card-tip"),method:"GET",success:function(t){console.log("获取卡片提示成功:",t),t.data&&0===t.data.code&&t.data.data&&(e.cardTipText=t.data.data)},fail:function(t){console.error("获取卡片提示失败:",t)}})}}};e.default=a}).call(this,i("df3c")["default"],i("3223")["default"])},dfcb:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return o}));var o={uIcon:function(){return i.e("components/uview-ui/components/u-icon/u-icon").then(i.bind(null,"8398"))}},a=function(){var t=this,e=t.$createElement,i=(t._self._c,t.showBox?t.__map(t.products,(function(e,i){var o=t.__get_orig(e),a=Math.abs(t.currentPage-i);return{$orig:o,g0:a}})):null);t.$mp.data=Object.assign({},{$root:{l0:i}})},n=[]},eeb7:function(t,e,i){"use strict";i.r(e);var o=i("dfcb"),a=i("2aba");for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);i("3005");var c=i("828b"),s=Object(c["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=s.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/tabbar-create-component',
    {
        'components/tabbar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("eeb7"))
        })
    },
    [['components/tabbar-create-component']]
]);
