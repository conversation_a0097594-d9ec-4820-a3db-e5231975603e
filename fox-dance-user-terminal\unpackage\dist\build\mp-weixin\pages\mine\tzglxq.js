(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/tzglxq"],{"053c":function(t,n,e){"use strict";(function(t,n){var i=e("47a9");e("cff9");i(e("3240"));var a=i(e("91c2"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"196b":function(t,n,e){},"398e":function(t,n,e){"use strict";var i=e("196b"),a=e.n(i);a.a},"6c6a":function(t,n,e){"use strict";e.r(n);var i=e("791a"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},"791a":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=e("6061"),a={data:function(){return{isLogined:!0,lists:[],xzIndex:-1}},onShow:function(){},onLoad:function(){this.detailSettingData()},methods:{detailSettingData:function(){t.showLoading({title:"加载中"});var n=this;(0,i.detailSettingApi)({}).then((function(e){if(console.log("详细设置",e),t.hideLoading(),1==e.code){for(var i=0;i<e.data.length;i++)e.data[i].select=0!=e.data[i].status;n.lists=e.data}}))},xzTap:function(t){console.log(t,"index"),this.xzIndex=t},switch1Change:function(n,e){t.showLoading({title:"加载中"});var a=this;(0,i.setStatusApi)({type:e.type}).then((function(e){console.log("设置通知状态",e),1==e.code&&(t.hideLoading(),a.lists[n].select=!a.lists[n].select)}))},navTo:function(n){t.navigateTo({url:n})}}};n.default=a}).call(this,e("df3c")["default"])},"91c2":function(t,n,e){"use strict";e.r(n);var i=e("cb49"),a=e("6c6a");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("398e");var c=e("828b"),u=Object(c["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=u.exports},cb49:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},a=[]}},[["053c","common/runtime","common/vendor"]]]);