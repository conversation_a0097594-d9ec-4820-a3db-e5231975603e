(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/buy/specification"],{"0c78":function(t,i,e){"use strict";var n=e("d576"),s=e.n(n);s.a},4421:function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){}));var n=function(){var t=this,i=t.$createElement,e=(t._self._c,t.gwcToggle?t.__map(t.guige,(function(i,e){var n=t.__get_orig(i),s=t.guige.length;return{$orig:n,g0:s}})):null);t._isMounted||(t.e0=function(i){t.gwcToggle=!1},t.e1=function(i){t.gwcToggle=!1}),t.$mp.data=Object.assign({},{$root:{l0:e}})},s=[]},"830a":function(t,i,e){"use strict";e.r(i);var n=e("4421"),s=e("ddce");for(var o in s)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(o);e("0c78");var g=e("828b"),u=Object(g["a"])(s["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);i["default"]=u.exports},c561:function(t,i,e){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var e={data:function(){return{isLogined:!0,gwcToggle:!1,selnum:1,xuanzText:"",guige:[],goodsDetial:{},carData:{},price:0,imgbaseUrl:"",dqggItem:{id:0}}},onShow:function(){},methods:{ljdhTap:function(){var i=this;if(0==this.dqggItem.id)return t.showToast({icon:"none",title:"请选择完整的规格",duration:2e3}),!1;if(0==this.dqggItem.stock)return t.showToast({icon:"none",title:"当前规格暂无库存",duration:2e3}),!1;if(this.selnum>this.dqggItem.stock)return t.showToast({icon:"none",title:"暂无更多库存",duration:2e3}),!1;var e=JSON.stringify({id:this.goodsDetial.id,name:this.goodsDetial.name,image:this.dqggItem.image,redeem_points:this.price,num:this.selnum,xuanztext:this.xuanzText,skuid:this.dqggItem.id});t.navigateTo({url:"/pages/buy/pointsMall/confirmOrder?productxq="+e,success:function(t){i.gwcToggle=!1}})},startTanc:function(t){this.selnum=1,this.dqggItem={id:0};for(var i=0;i<t.spec_list.length;i++)t.spec_list[i].indexSel=-1;this.imgbaseUrl=this.$baseUrl,this.gwcToggle=!0,this.goodsDetial=t,this.guige=t.spec_list,this.price=t.redeem_points,this.xuanzText="请选择",console.log(t)},selguigClick:function(t,i){console.log(t,i),this.guige[t].indexSel==i?this.guige[t].indexSel=-1:this.guige[t].indexSel=i;for(var e=[],n=[],s=0;s<this.guige.length;s++)-1!=this.guige[s].indexSel&&(e.push(this.guige[s].value[this.guige[s].indexSel]),n.push(this.guige[s].name+":"+this.guige[s].value[this.guige[s].indexSel]));this.xuanzText=e.join(","),0==n.length||n.length!=this.guige.length?(this.price=this.goodsDetial.redeem_points,this.dqggItem={id:0}):this.goodspriceApi(n)},goodspriceApi:function(t){console.log(t,"newsArr");for(var i=t.join(";"),e=this.goodsDetial.spec_data.skuList,n=0;n<e.length;n++)e[n].spec==i&&(this.dqggItem=e[n]);console.log(this.dqggItem,"this.dqggItem")},jian:function(t){if(1==this.selnum)return!1;this.selnum--},add:function(t){this.selnum++},navTo:function(i){t.navigateTo({url:i})}}};i.default=e}).call(this,e("df3c")["default"])},d576:function(t,i,e){},ddce:function(t,i,e){"use strict";e.r(i);var n=e("c561"),s=e.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(o);i["default"]=s.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages/buy/specification-create-component',
    {
        'pages/buy/specification-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("830a"))
        })
    },
    [['pages/buy/specification-create-component']]
]);
