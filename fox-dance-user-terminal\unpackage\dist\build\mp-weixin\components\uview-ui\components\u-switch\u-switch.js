(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uview-ui/components/u-switch/u-switch"],{"394f":function(t,e,i){"use strict";i.r(e);var n=i("a8bc"),a=i.n(n);for(var u in n)["default"].indexOf(u)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(u);e["default"]=a.a},"4de7":function(t,e,i){"use strict";var n=i("5645"),a=i.n(n);a.a},5645:function(t,e,i){},a8bc:function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-switch",props:{loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},size:{type:[Number,String],default:50},activeColor:{type:String,default:"#2979ff"},inactiveColor:{type:String,default:"#ffffff"},value:{type:Boolean,default:!1},vibrateShort:{type:Boolean,default:!1},activeValue:{type:[Number,String,Boolean],default:!0},inactiveValue:{type:[Number,String,Boolean],default:!1}},data:function(){return{}},computed:{switchStyle:function(){var t={};return t.fontSize=this.size+"rpx",t.backgroundColor=this.value?this.activeColor:this.inactiveColor,t},loadingColor:function(){return this.value?this.activeColor:null}},methods:{onClick:function(){var e=this;this.disabled||this.loading||(this.vibrateShort&&t.vibrateShort(),this.$emit("input",!this.value),this.$nextTick((function(){e.$emit("change",e.value?e.activeValue:e.inactiveValue)})))}}};e.default=i}).call(this,i("df3c")["default"])},af28:function(t,e,i){"use strict";i.r(e);var n=i("c703"),a=i("394f");for(var u in a)["default"].indexOf(u)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(u);i("4de7");var o=i("828b"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"ddbe4f62",null,!1,n["a"],void 0);e["default"]=l.exports},c703:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return u})),i.d(e,"a",(function(){return n}));var n={uLoading:function(){return i.e("components/uview-ui/components/u-loading/u-loading").then(i.bind(null,"2bf0"))}},a=function(){var t=this.$createElement,e=(this._self._c,this.__get_style([this.switchStyle])),i=this.$u.addUnit(this.size),n=this.$u.addUnit(this.size);this.$mp.data=Object.assign({},{$root:{s0:e,g0:i,g1:n}})},u=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uview-ui/components/u-switch/u-switch-create-component',
    {
        'components/uview-ui/components/u-switch/u-switch-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("af28"))
        })
    },
    [['components/uview-ui/components/u-switch/u-switch-create-component']]
]);
