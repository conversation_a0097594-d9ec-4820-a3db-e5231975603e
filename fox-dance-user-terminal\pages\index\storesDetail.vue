<template>
	<view class="storesDetail"  :style="{ '--qjbutton-color': qjbutton,'--qjziti-color': qjziti }" v-if="loding">
		
		<view class="stor_one">
			<view class="stor_one_t">
				<image :src="imgbaseUrl + storeInfo.image" mode="aspectFill" class="stor_one_t_l"></image>
				<view class="stor_one_t_r">
					<view class="stor_one_t_r_a">{{storeInfo.name}}</view>
					<view class="stor_one_t_r_b">{{storeInfo.introduce}}</view>
					<view class="stor_one_t_r_c"><image src="/static/images/icon18.png"></image>距离你{{storeInfo.distance}}km</view>
				</view>
			</view>
			<view class="stor_one_b">
				<view>{{storeInfo.address}}</view>
				<text @click.stop="dhTap">导航前往</text>
			</view>
		</view>
		
		<view class="stor_two">
			<view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>路线指引</text><text></text></view></view>
			<view class="stor_two_b">
				<!-- <video src="https://www.runoob.com/try/demo_source/movie.mp4" controls></video>
					<rich-text :nodes="kcjsDetail"></rich-text>
				 -->
				<video v-if="storeInfo.route_guidance" :src="storeInfo.isoss ? storeInfo.route_guidance : imgbaseUrl + storeInfo.route_guidance" controls></video>
				<image :src="imgbaseUrl + item" v-for="(item,index) in storeInfo.poster" :key="index" mode="widthFix" @click="openImg(index,storeInfo.poster)"></image>
			</view>
		</view>
		
		<view class="stor_thr">
			<view class="kcxq_two_t" style="margin:0 52rpx;"><view class="kcxq_two_t_n"><text>门店课程</text><text></text></view></view>
			
			<view class="stor_thr_c">
				<view class="stor_thr_c_n" :style="(jbToggle || wuzToggle || laosToggle) ? 'border-radius: 20rpx 20rpx 0 0;' : ''">
					<view class="stor_thr_c_li" :class="jbToggle ? 'stor_thr_c_li_ac' : ''" @click="jbStartTap">{{jibText == '' ? '级别' : jibText}}<text></text></view>
					<view class="stor_thr_c_li" :class="wuzToggle ? 'stor_thr_c_li_ac' : ''" @click="wuzStartTap">{{wuzText == '' ? '舞种' : wuzText}}<text></text></view>
					<view class="stor_thr_c_li" :class="laosToggle ? 'stor_thr_c_li_ac' : ''" @click="laosStartTap">{{laosText == '' ? '老师' : laosText}}<text></text></view>
				</view>
				<view class="gg_rgba" v-if="jbToggle || wuzToggle || laosToggle" @click="gbTcTap"></view>
				<!-- 级别 go -->
				<view class="teaxzTanc" v-if="jbToggle">
					<view class="teaxzTanc_t">
						<view v-for="(item,index) in jibLists" :key="index" :class="jibIndex == index ? 'teaxzTanc_t_ac' : ''" @click="jibTap(index)">{{item.name}}</view>
					</view>
					<view class="teaxzTanc_b"><view @click="jibReact">重置</view><text @click="jibSubTap">提交</text></view>
				</view>
				<!-- 级别 end -->
				<!-- 舞种 go -->
				<view class="teaxzTanc" v-if="wuzToggle">
					<view class="teaxzTanc_t">
						<view v-for="(item,index) in wuzLists" :key="index" :class="wuzIndex == index ? 'teaxzTanc_t_ac' : ''" @click="wuzTap(index)">{{item.name}}</view>
					</view>
					<view class="teaxzTanc_b"><view @click="wuzReact">重置</view><text @click="wuzSubTap">提交</text></view>
				</view>
				<!-- 舞种 end -->
				<!-- 老师 go -->
				<view class="teaxzTanc" v-if="laosToggle">
					<view class="teaxzTanc_t">
						<view v-for="(item,index) in laosLists" :key="index" :class="laosIndex == index ? 'teaxzTanc_t_ac' : ''" @click="laosTap(index)">{{item.name}}</view>
					</view>
					<view class="teaxzTanc_b"><view @click="laosReact">重置</view><text @click="laosSubTap">提交</text></view>
				</view>
				<!-- 老师 end -->
			</view>
			
			<view class="teaCon">
				<view class="teaCon_li" v-for="(item,index) in storeCourseLists" :key="index" @click="storesxqTap(item)">
					<view class="teaCon_li_a">{{item.course.name}}</view>
					<view class="teaCon_li_b">
						<image :src="imgbaseUrl + item.teacher.image" mode="aspectFill" class="teaCon_li_b_l"></image>
						<view class="teaCon_li_b_c">
							<view class="teaCon_li_b_c_a">{{item.start_time}}-{{item.end_time}}</view>
							<view class="teaCon_li_b_c_b">上课老师：{{item.teacher.name}}</view>
							<view class="teaCon_li_b_c_b" v-if="item.frequency*1 > 0">次卡消耗：{{item.frequency*1}}次</view>
							<view class="teaCon_li_b_c_c"><text v-if="item.level_name">{{item.level_name}}</text><text>{{item.dance_name}}</text></view>
						</view>
						
						
						<view class="teaCon_li_b_r" style="background:#BEBEBE" v-if="item.status == 1" @click.stop>待开课</view>
						<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 2" @click.stop>授课中</view>
						<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 3" @click.stop>已完成</view>
						<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 4" @click.stop>等位中</view>
						<!-- <view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 6" @click.stop>未开始预约</view> -->
						<view class="teaCon_li_b_r yysj" style="background:#BEBEBE" v-else-if="item.status == 6" @click.stop><text>{{item.start_reservation}}</text><text>开始预约</text></view>
						<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.status == 7" @click.stop>截止预约</view>
						<!-- 未开启等位并且课程预约爆满 (课程预约满员时开启等位:0=关闭,1=开启)-->
						<view class="teaCon_li_b_r" style="background:#BEBEBE" v-else-if="item.equivalent*1 == 0 && (item.appointment_number*1 >= item.maximum_reservation*1)" @click.stop="kqhyts">预约</view>
						<view class="teaCon_li_b_r" :style="item.member == 0 ? 'background:#BEBEBE' : ''" v-else-if="item.member == 0" @click.stop="ljtkToggle = true">预约</view>
						<!-- 开启等位 -->
						<view class="teaCon_li_b_r" v-else @click.stop="yypdTo(item,'/pages/Schedule/Schedulexq?id' + item.id)">{{item.waiting_number*1 > 0 ? '去排队' : '预约'}}</view>
					</view>
					<view class="teaCon_li_c"  v-if="item.appointment_number > 0">
						<view class="teaCon_li_c_l">
							<!-- /static/images/toux.png -->
							<image :src="imgbaseUrl + item.avatar" v-for="(item,index) in item.appointment_people" :key="index" mode="aspectFit"></image>
						</view>
						<view class="teaCon_li_c_r">已预约：<text>{{item.appointment_number}}</text>人;<template v-if="item.waiting_number*1 > 0"><text>{{item.waiting_number}}</text>人在等位</template></view>
					</view>
				</view>
			</view>
			
			<view class="gg_zwsj" style="margin-bottom:60rpx;" v-if="storeCourseLists.length == 0">
				<view class="gg_zwsj_w">
					<image src="/static/images/wusj.png" mode="widthFix"></image>
					<text>暂无课程</text>
				</view>
			</view>
			
		</view>
		
		<!-- 提示预约弹窗 go -->
		<view class="yytnCon" v-if="ljtkToggle"><view class="yytnCon_n"><image :src="imgbaseUrlOss + '/userreport/icon55.png'"></image><text @click="ljktTap"></text></view><image src="/static/images/icon56.png" @click="ljtkToggle = false"></image></view>
		<!-- 提示预约弹窗 end -->
		
		<!-- ios底部安全距离 go -->
		<view class="aqjlViw"></view>
		<!-- ios底部安全距离 end -->
		
	</view>
</template>


<script>
import {
	storeDetailApi,
	lscxCategoryApi,
	storeCourseApi
} from '@/config/http.achieve.js'
export default {
	data() {
		return {
			isLogined:true,
			loding:false,
			kcjsDetail:'<p style="color:#fff;background:red;font-size:14px;">富文本内容</p><img src="https://luckvan.oss-cn-shanghai.aliyuncs.com/images/a7e913fe-3354-4d90-9f38-db5118aee62c-12.png" />',
			imgbaseUrl:'',//图片地址
			imgbaseUrlOss:'',
			jibLists:[],
			jibIndex:-1,
			jibText:'',
			jbToggle:false,
			
			wuzLists:[],
			wuzIndex:-1,
			wuzText:'',
			wuzToggle:false,
			
			laosLists:[],
			laosIndex:-1,
			laosText:'',
			laosToggle:false,
			
			storeInfo:{},//门店详情
			storeCourseLists:[],
			page:1,//当前页数
			total_pages: 1,//总页数
			zanwsj:false,//是否有数据
			status: 'loading',//底部loding是否显示
			loadingText: '努力加载中',
			loadmoreText: '轻轻上拉',
			nomoreText: '实在没有了',
			
			storeId:0,
			ljtkToggle:false,
			qjbutton:'#fff',
			qjziti:'#F8F8FA'
		}
	},
	onShow() {
		this.imgbaseUrlOss = this.$baseUrlOss;
		this.isLogined = uni.getStorageSync('token') ? true : false;
		this.imgbaseUrl = this.$baseUrl;
		this.page = 1;
		this.storeCourseLists = [];//门店课程
		this.storeCourseData();//门店课程
	},
	onLoad(option) {
		this.storeId = option.id;
		this.storeDetail();//门店详情
		this.categoryData();//老师分类
		this.qjbutton = uni.getStorageSync('storeInfo').button
		this.qjziti = uni.getStorageSync('storeInfo').written_words
	},
	methods: {
		//详情跳转
		storesxqTap(item){
			console.log(this.isLogined,'this.isLogined');
			if(!this.isLogined){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			// 未开启会员并且后端设置了必须开通会员方可查看详情
			if(item.course.view_type*1 == 0 && item.member == 0){
				this.ljtkToggle = true
			}else{
				uni.navigateTo({
					// url:'/pages/Schedule/Schedulexq?id=' + item.id
					url:'/pages/mine/myCourse/myCoursexq?id=' + item.id
				})
			}
		},
		//预约约课/排队
		yypdTo(item){
			if(!this.isLogined){
				uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
				setTimeout(function(){
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1000)
				return false;
			}
			// 未开启会员
			if(item.member == 0){
				this.ljtkToggle = true
				return false;
			}
			uni.navigateTo({
				url:'/pages/Schedule/confirmOrder?id=' + item.id + '&storeid=' + this.storeId
			})
		},
		//立即开通会员
		ljktTap(){
			this.ljtkToggle = false;
			uni.switchTab({
				url:'/pages/buy/buy'
			})
		},
		//导航
		dhTap(item){
			var that = this;
			uni.openLocation({
				name:that.storeInfo.address,
				latitude: that.storeInfo.latitude*1,
				longitude: that.storeInfo.longitude*1,
				success: function () {
					console.log('success');
				}
			});
		},
		//预约爆满
		kqhyts(){
			uni.showToast({
				title: '预约课程已满',
				icon: 'none',
				duration: 1000
			})
		},
		//门店课程
		/*storeCourseData(){
			console.log('门店课程12')
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			storeCourseApi({
				id:that.storeId,
				level_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,
				dance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,
				teacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,
				// date:'2024-10-30',
			}).then(res => {
				console.log('门店课程',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.storeCourseLists = res.data;
				}
			})
		},*/
		//门店课程
		storeCourseData(jinz){
			
			let that = this;
			uni.showLoading({
				title: '加载中'
			});
			storeCourseApi({
				page:that.page,
				id:that.storeId,
				level_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,
				dance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,
				teacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,
			}).then(res => {
				console.log('门店课程',res)
				/*if (res.code == 1) {
					uni.hideLoading();
					that.storeCourseLists = res.data.data;
				}*/
				if (res.code == 1) {
					var obj = res.data.data;
					that.storeCourseLists = that.storeCourseLists.concat(obj);
					that.zanwsj = that.storeCourseLists.length ? true : false;
					that.page++;
					// that.total_pages = Math.ceil(res.total/20);
					that.total_pages = res.data.last_page;
					if (that.page != 1) {
						if (that.total_pages >= that.page) {
							that.status = 'loading'
						}else{
							that.status = 'nomore'
						}
					}
					if(that.storeCourseLists.length == 0){
						that.zanwsj = true;
					}else{
						that.zanwsj = false;
					}
					if(res.data.total*1<=10){
						that.status = 'nomore'
					}
					that.loding = true;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}
			})
		},
		onReachBottom() {
			//console.log('到底了');
			if (this.page != 1) {
				if (this.total_pages >= this.page) {
					this.storeCourseData();//门店课程
				}
			}
		},
		onPullDownRefresh: function() {
		    console.log('我被下拉了');
		    this.page = 1;
		    this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
		},
		//门店详情
		storeDetail(){
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			storeDetailApi({
				longitude:uni.getStorageSync('postion').longitude,
				latitude:uni.getStorageSync('postion').latitude,
				id:that.storeId
			}).then(res => {
				console.log('门店详情',res)
				if (res.code == 1) {
					uni.hideLoading();
					that.loding = true;
					// res.data.route_guidance = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734312077132_ces.mp4'
					if(res.data.route_guidance){
						res.data.isoss = res.data.route_guidance.substring(0,5) == 'https' ? true : false
					}
					// console.log(res.data.route_guidance.substring(0,5),res.data.isoss,'ddd');
					that.storeInfo = res.data;
				}
			})
		},
		//老师分类
		categoryData(){
			let that = this;
			lscxCategoryApi({}).then(res => {
				console.log('老师分类',res)
				if (res.code == 1) {
					that.jibLists = res.data.level;
					that.wuzLists = res.data.dance;
					that.laosLists = res.data.teacher;
				}
			})
		},
		//关闭所有弹窗
		gbTcTap(){
			this.jbToggle = false;
			this.wuzToggle = false;
			this.laosToggle = false;
		},
		//级别弹窗开启
		jbStartTap(){
			this.jbToggle = !this.jbToggle;
			this.wuzToggle = false;
			this.laosToggle = false;
		},
		//级别选择
		jibTap(index){
			this.jibIndex = index;
		},
		//级别提交
		jibSubTap(){
			if(this.jibIndex == -1){
				this.jibText = ''
			}else{
				this.jibText = this.jibLists[this.jibIndex].name
			}
			this.jbToggle = false;
			this.page = 1;
			this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
		},
		//级别重置
		jibReact(){
			this.jibIndex = -1;
		},
		//舞种弹窗开启
		wuzStartTap(){
			this.jbToggle = false;
			this.wuzToggle = !this.wuzToggle;
			this.laosToggle = false;
		},
		//舞种选择
		wuzTap(index){
			this.wuzIndex = index;
		},
		//舞种提交
		wuzSubTap(){
			if(this.wuzIndex == -1){
				this.wuzText = ''
			}else{
				this.wuzText = this.wuzLists[this.wuzIndex].name
			}
			this.wuzToggle = false;
			this.page = 1;
			this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
		},
		//舞种重置
		wuzReact(){
			this.wuzIndex = -1;
		},
		//老师弹窗开启
		laosStartTap(){			
			this.jbToggle = false;
			this.wuzToggle = false;
			this.laosToggle = !this.laosToggle;
		},
		//老师选择
		laosTap(index){
			this.laosIndex = index;
		},
		//老师提交
		laosSubTap(){
			if(this.laosIndex == -1){
				this.laosText = ''
			}else{
				this.laosText = this.laosLists[this.laosIndex].name
			}
			this.laosToggle = false;
			this.page = 1;
			this.storeCourseLists = [];//门店课程
			this.storeCourseData();//门店课程
		},
		//老师重置
		laosReact(){
			this.laosIndex = -1;
		},
		//打开图片
		openImg(idx, imgs) {
			let arr = []
			for (let i = 0; i < imgs.length; i++) {
				arr.push(this.imgbaseUrl + imgs[i])
			}
			console.log(idx, imgs);
			uni.previewImage({
				current: idx,
				urls: arr
			})
		},
		navTo(url){
			uni.navigateTo({
				url:url
			})
		}
		
	}
}
</script>

<style lang="scss">
.storesDetail{overflow: hidden;}
page{padding-bottom: 0;}
</style>