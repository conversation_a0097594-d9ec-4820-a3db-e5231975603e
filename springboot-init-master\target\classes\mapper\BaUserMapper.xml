<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.BaUserMapper">

    <resultMap id="BaseResultMap" type="com.yupi.springbootinit.model.entity.BaUser">
            <id property="id" column="id" />
            <result property="username" column="username" />
            <result property="nickname" column="nickname" />
            <result property="social_id" column="social_id" />
            <result property="mobile" column="mobile" />
            <result property="avatar" column="avatar" />
            <result property="level" column="level" />
            <result property="is_member" column="is_member" />
            <result property="remaining_votes" column="remaining_votes" />
            <result property="bio" column="bio" />
            <result property="dance_type" column="dance_type" />
    </resultMap>

    <sql id="Base_Column_List">
        id,group_id,username,nickname,social_id,email,mobile,
        avatar,gender,birthday,money,score,
        last_login_time,last_login_ip,login_failure,join_ip,join_time,
        motto,password,salt,status,update_time,
        create_time,frequency,share_code,sale_id,online,
        introduction,pid,openid,store_id,register_time,
        luck_draw_frequency,experience_value,level,payment_code,is_member,
        remaining_votes,bio,dance_type
    </sql>
</mapper>
