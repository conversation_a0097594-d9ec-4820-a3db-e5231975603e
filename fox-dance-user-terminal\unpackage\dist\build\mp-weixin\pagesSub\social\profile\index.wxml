<view class="profile-container data-v-7bb4fb94"><view class="header-section data-v-7bb4fb94"><view class="header-bg data-v-7bb4fb94"></view><view class="header-actions data-v-7bb4fb94"><u-icon vue-id="709747e7-1" name="scan" color="#fff" size="48rpx" data-event-opts="{{[['^click',[['scanCode']]]]}}" bind:click="__e" class="data-v-7bb4fb94" bind:__l="__l"></u-icon><u-icon vue-id="709747e7-2" name="setting" color="#fff" size="48rpx" data-event-opts="{{[['^click',[['goSettings']]]]}}" bind:click="__e" class="data-v-7bb4fb94" bind:__l="__l"></u-icon></view><view class="user-info-section data-v-7bb4fb94"><view class="user-avatar-container data-v-7bb4fb94"><u-avatar vue-id="709747e7-3" src="{{userInfo.avatar}}" size="120" data-event-opts="{{[['^click',[['editAvatar']]]]}}" bind:click="__e" class="data-v-7bb4fb94" bind:__l="__l"></u-avatar></view><view class="user-info-content data-v-7bb4fb94"><block wx:if="{{loading}}"><view class="loading-container data-v-7bb4fb94"><view class="loading-spinner data-v-7bb4fb94"></view><text class="loading-text data-v-7bb4fb94">加载中...</text></view></block><block wx:else><view class="user-info-row data-v-7bb4fb94"><view class="user-details data-v-7bb4fb94"><text class="nickname data-v-7bb4fb94">{{userInfo.nickname||'加载中...'}}</text><text class="user-id data-v-7bb4fb94">{{"ID: "+(userInfo.userId||'--')}}</text><text class="dance-type data-v-7bb4fb94">{{"舞种："+(userInfo.danceType||'--')}}</text><text class="bio data-v-7bb4fb94">{{userInfo.bio||'暂无个人简介'}}</text></view><view class="edit-section data-v-7bb4fb94"><text data-event-opts="{{[['tap',[['editProfile',['$event']]]]]}}" class="edit-link data-v-7bb4fb94" bindtap="__e">编辑资料</text></view></view></block><view class="stats-row data-v-7bb4fb94"><view data-event-opts="{{[['tap',[['switchTab',[0]]]]]}}" class="stat-item data-v-7bb4fb94" bindtap="__e"><text class="stat-number data-v-7bb4fb94">{{userInfo.postCount}}</text><text class="stat-label data-v-7bb4fb94">帖子</text></view><view data-event-opts="{{[['tap',[['switchTab',[2]]]]]}}" class="stat-item data-v-7bb4fb94" bindtap="__e"><text class="stat-number data-v-7bb4fb94">{{userInfo.followingCount}}</text><text class="stat-label data-v-7bb4fb94">关注</text></view><view data-event-opts="{{[['tap',[['switchTab',[3]]]]]}}" class="stat-item data-v-7bb4fb94" bindtap="__e"><text class="stat-number data-v-7bb4fb94">{{userInfo.followersCount}}</text><text class="stat-label data-v-7bb4fb94">粉丝</text></view><view class="stat-item data-v-7bb4fb94"><text class="stat-number data-v-7bb4fb94">{{userInfo.likeCount}}</text><text class="stat-label data-v-7bb4fb94">获赞</text></view></view></view></view></view><scroll-view class="content data-v-7bb4fb94" scroll-y="{{true}}"><view class="tabs-container data-v-7bb4fb94"><u-tabs vue-id="709747e7-4" list="{{tabs}}" current="{{currentTab}}" lineWidth="30" lineColor="#303133" activeStyle="{{({color:'#303133',fontWeight:'bold'})}}" inactiveStyle="{{({color:'#606266'})}}" data-event-opts="{{[['^change',[['switchTab']]]]}}" bind:change="__e" class="data-v-7bb4fb94" bind:__l="__l"></u-tabs></view><view class="posts-content data-v-7bb4fb94"><block wx:if="{{$root.g0}}"><view class="data-v-7bb4fb94"><block wx:if="{{currentTab===0||currentTab===1||currentTab===4}}"><view class="post-grid data-v-7bb4fb94"><block wx:for="{{tabs[currentTab].data}}" wx:for-item="post" wx:for-index="__i0__" wx:key="id"><post-card class="post-card-item data-v-7bb4fb94" vue-id="{{'709747e7-5-'+__i0__}}" post="{{post}}" data-event-opts="{{[['^click',[['goPostDetail']]],['^userClick',[['goUserProfile']]],['^like',[['onPostLike']]]]}}" bind:click="__e" bind:userClick="__e" bind:like="__e" bind:__l="__l"></post-card></block></view></block><block wx:else><block wx:if="{{currentTab===2||currentTab===3}}"><view class="user-list data-v-7bb4fb94"><block wx:for="{{$root.l0}}" wx:for-item="user" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['goUserProfile',['$0'],[[['tabs.'+currentTab+'.data','id',user.$orig.id]]]]]]]}}" class="user-item data-v-7bb4fb94" bindtap="__e"><u-avatar class="user-avatar data-v-7bb4fb94" vue-id="{{'709747e7-6-'+__i1__}}" src="{{user.$orig.avatar}}" size="80" bind:__l="__l"></u-avatar><view class="user-info data-v-7bb4fb94"><text class="user-nickname data-v-7bb4fb94">{{user.$orig.nickname}}</text><text class="user-bio data-v-7bb4fb94">{{user.$orig.bio}}</text><text class="user-stats data-v-7bb4fb94">{{user.$orig.followersCount+" 粉丝"}}</text></view><view class="user-action data-v-7bb4fb94"><follow-button vue-id="{{'709747e7-7-'+__i1__}}" user="{{user.a0}}" followed="{{user.$orig.isFollowed}}" size="mini" data-event-opts="{{[['^follow',[['onUserFollow']]],['^unfollow',[['onUserUnfollow']]],['^change',[['onFollowChange']]],['^click',[['',['$event']]]]]}}" bind:follow="__e" bind:unfollow="__e" bind:change="__e" catch:click="__e" class="data-v-7bb4fb94" bind:__l="__l"></follow-button></view></view></block></view></block></block></view></block><block wx:else><view class="empty-state data-v-7bb4fb94"><u-empty vue-id="709747e7-8" mode="list" text="{{$root.m0}}" class="data-v-7bb4fb94" bind:__l="__l"></u-empty></view></block></view></scroll-view></view>