{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-icon/u-icon.vue?a6f7", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-icon/u-icon.vue?1a48", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-icon/u-icon.vue?f0cf", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-icon/u-icon.vue?44d3", "uni-app:///components/uview-ui/components/u-icon/u-icon.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-icon/u-icon.vue?6cf4", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-icon/u-icon.vue?730c"], "names": ["name", "props", "type", "default", "color", "size", "bold", "index", "hoverClass", "customPrefix", "label", "labelPos", "labelSize", "labelColor", "marginLeft", "marginTop", "marginRight", "marginBottom", "imgMode", "customStyle", "width", "height", "top", "showDecimalIcon", "inactiveColor", "percent", "computed", "customClass", "classes", "iconStyle", "style", "fontSize", "fontWeight", "isImg", "imgStyle", "decimalIconStyle", "decimalIconClass", "methods", "click", "touchstart"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACkM;AAClM,gBAAgB,6LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,gsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuBlxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9BA,gBA+BA;EACAA;EACAC;IACA;IACAD;MACAE;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;QACA;MACA;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;EACA;EACAuB;IACAC;MACA;MACAC;MACA;MACA;QACAA;MACA;QACAA;MACA;MACA;MACA;QACAA;MACA;MACA;MACA;;MAIA;IACA;IACAC;MACA;MACAC;QACAC;QACAC;QACA;QACAV;MACA;MACA;MACA;QACAQ;MACA;MAEA;IACA;IACA;IACAG;MACA;IACA;IACAC;MACA;MACA;MACAJ;MACAA;MACA;IACA;IACAK;MACA;MACAL;QACAC;QACAC;QACA;QACAV;QACAF;MACA;MACA;MACA;MACA;IACA;IACAgB;MACA;MACAR;MACA;MACA;QACAA;MACA;QACAA;MACA;MACA;MACA,6GACAA;MACA;MACA;;MAIA;IACA;EACA;EACAS;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACvQA;AAAA;AAAA;AAAA;AAA67C,CAAgB,uwCAAG,EAAC,C;;;;;;;;;;;ACAj9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-icon/u-icon.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-icon.vue?vue&type=template&id=7a019853&scoped=true&\"\nvar renderjs\nimport script from \"./u-icon.vue?vue&type=script&lang=js&\"\nexport * from \"./u-icon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-icon.vue?vue&type=style&index=0&id=7a019853&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7a019853\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-icon/u-icon.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=template&id=7a019853&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.customStyle])\n  var s1 = _vm.isImg ? _vm.__get_style([_vm.imgStyle]) : null\n  var s2 = !_vm.isImg ? _vm.__get_style([_vm.iconStyle]) : null\n  var s3 =\n    !_vm.isImg && _vm.showDecimalIcon\n      ? _vm.__get_style([_vm.decimalIconStyle])\n      : null\n  var g0 = _vm.label !== \"\" ? _vm.$u.addUnit(_vm.labelSize) : null\n  var g1 =\n    _vm.label !== \"\" && _vm.labelPos == \"right\"\n      ? _vm.$u.addUnit(_vm.marginLeft)\n      : null\n  var g2 =\n    _vm.label !== \"\" && _vm.labelPos == \"bottom\"\n      ? _vm.$u.addUnit(_vm.marginTop)\n      : null\n  var g3 =\n    _vm.label !== \"\" && _vm.labelPos == \"left\"\n      ? _vm.$u.addUnit(_vm.marginRight)\n      : null\n  var g4 =\n    _vm.label !== \"\" && _vm.labelPos == \"top\"\n      ? _vm.$u.addUnit(_vm.marginBottom)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :style=\"[customStyle]\" class=\"u-icon\" @tap=\"click\" :class=\"['u-icon--' + labelPos]\">\r\n\t\t<image class=\"u-icon__img\" v-if=\"isImg\" :src=\"name\" :mode=\"imgMode\" :style=\"[imgStyle]\"></image>\r\n\t\t<text v-else class=\"u-icon__icon\" :class=\"customClass\" :style=\"[iconStyle]\" :hover-class=\"hoverClass\"\r\n\t\t\t  @touchstart=\"touchstart\">\r\n\t\t\t<text v-if=\"showDecimalIcon\" :style=\"[decimalIconStyle]\" :class=\"decimalIconClass\" :hover-class=\"hoverClass\"\r\n\t\t\t\t  class=\"u-icon__decimal\">\r\n\t\t\t</text>\r\n\t\t</text>\r\n\t\t<!-- 这里进行空字符串判断，如果仅仅是v-if=\"label\"，可能会出现传递0的时候，结果也无法显示 -->\r\n\t\t<text v-if=\"label !== ''\" class=\"u-icon__label\" :style=\"{\r\n\t\t\tcolor: labelColor,\r\n\t\t\tfontSize: $u.addUnit(labelSize),\r\n\t\t\tmarginLeft: labelPos == 'right' ? $u.addUnit(marginLeft) : 0,\r\n\t\t\tmarginTop: labelPos == 'bottom' ? $u.addUnit(marginTop) : 0,\r\n\t\t\tmarginRight: labelPos == 'left' ? $u.addUnit(marginRight) : 0,\r\n\t\t\tmarginBottom: labelPos == 'top' ? $u.addUnit(marginBottom) : 0,\r\n\t\t}\">{{ label }}\r\n\t\t</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * icon 图标\r\n * @description 基于字体的图标集，包含了大多数常见场景的图标。\r\n * @tutorial https://www.uviewui.com/components/icon.html\r\n * @property {String} name 图标名称，见示例图标集\r\n * @property {String} color 图标颜色（默认inherit）\r\n * @property {String | Number} size 图标字体大小，单位rpx（默认32）\r\n * @property {String | Number} label-size label字体大小，单位rpx（默认28）\r\n * @property {String} label 图标右侧的label文字（默认28）\r\n * @property {String} label-pos label文字相对于图标的位置，只能right或bottom（默认right）\r\n * @property {String} label-color label字体颜色（默认#606266）\r\n * @property {Object} custom-style icon的样式，对象形式\r\n * @property {String} custom-prefix 自定义字体图标库时，需要写上此值\r\n * @property {String | Number} margin-left label在右侧时与图标的距离，单位rpx（默认6）\r\n * @property {String | Number} margin-top label在下方时与图标的距离，单位rpx（默认6）\r\n * @property {String | Number} margin-bottom label在上方时与图标的距离，单位rpx（默认6）\r\n * @property {String | Number} margin-right label在左侧时与图标的距离，单位rpx（默认6）\r\n * @property {String} label-pos label相对于图标的位置，只能right或bottom（默认right）\r\n * @property {String} index 一个用于区分多个图标的值，点击图标时通过click事件传出\r\n * @property {String} hover-class 图标按下去的样式类，用法同uni的view组件的hover-class参数，详情见官网\r\n * @property {String} width 显示图片小图标时的宽度\r\n * @property {String} height 显示图片小图标时的高度\r\n * @property {String} top 图标在垂直方向上的定位\r\n * @property {String} top 图标在垂直方向上的定位\r\n * @property {String} top 图标在垂直方向上的定位\r\n * @property {Boolean} show-decimal-icon 是否为DecimalIcon\r\n * @property {String} inactive-color 背景颜色，可接受主题色，仅Decimal时有效\r\n * @property {String | Number} percent 显示的百分比，仅Decimal时有效\r\n * @event {Function} click 点击图标时触发\r\n * @example <u-icon name=\"photo\" color=\"#2979ff\" size=\"28\"></u-icon>\r\n */\r\nexport default {\r\n\tname: 'u-icon',\r\n\tprops: {\r\n\t\t// 图标类名\r\n\t\tname: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 图标颜色，可接受主题色\r\n\t\tcolor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 字体大小，单位rpx\r\n\t\tsize: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 'inherit'\r\n\t\t},\r\n\t\t// 是否显示粗体\r\n\t\tbold: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 点击图标的时候传递事件出去的index（用于区分点击了哪一个）\r\n\t\tindex: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 触摸图标时的类名\r\n\t\thoverClass: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 自定义扩展前缀，方便用户扩展自己的图标库\r\n\t\tcustomPrefix: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'uicon'\r\n\t\t},\r\n\t\t// 图标右边或者下面的文字\r\n\t\tlabel: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// label的位置，只能右边或者下边\r\n\t\tlabelPos: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'right'\r\n\t\t},\r\n\t\t// label的大小\r\n\t\tlabelSize: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: '28'\r\n\t\t},\r\n\t\t// label的颜色\r\n\t\tlabelColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#606266'\r\n\t\t},\r\n\t\t// label与图标的距离(横向排列)\r\n\t\tmarginLeft: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: '6'\r\n\t\t},\r\n\t\t// label与图标的距离(竖向排列)\r\n\t\tmarginTop: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: '6'\r\n\t\t},\r\n\t\t// label与图标的距离(竖向排列)\r\n\t\tmarginRight: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: '6'\r\n\t\t},\r\n\t\t// label与图标的距离(竖向排列)\r\n\t\tmarginBottom: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: '6'\r\n\t\t},\r\n\t\t// 图片的mode\r\n\t\timgMode: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'widthFix'\r\n\t\t},\r\n\t\t// 自定义样式\r\n\t\tcustomStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 用于显示图片小图标时，图片的宽度\r\n\t\twidth: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 用于显示图片小图标时，图片的高度\r\n\t\theight: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 用于解决某些情况下，让图标垂直居中的用途\r\n\t\ttop: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 是否为DecimalIcon\r\n\t\tshowDecimalIcon: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 背景颜色，可接受主题色，仅Decimal时有效\r\n\t\tinactiveColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#ececec'\r\n\t\t},\r\n\t\t// 显示的百分比，仅Decimal时有效\r\n\t\tpercent: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: '50'\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tcustomClass() {\r\n\t\t\tlet classes = []\r\n\t\t\tclasses.push(this.customPrefix + '-' + this.name)\r\n\t\t\t// uView的自定义图标类名为u-iconfont\r\n\t\t\tif (this.customPrefix == 'uicon') {\r\n\t\t\t\tclasses.push('u-iconfont')\r\n\t\t\t} else {\r\n\t\t\t\tclasses.push(this.customPrefix)\r\n\t\t\t}\r\n\t\t\t// 主题色，通过类配置\r\n\t\t\tif (this.showDecimalIcon && this.inactiveColor && this.$u.config.type.includes(this.inactiveColor)) {\r\n\t\t\t\tclasses.push('u-icon__icon--' + this.inactiveColor)\r\n\t\t\t} else if (this.color && this.$u.config.type.includes(this.color)) classes.push('u-icon__icon--' + this.color)\r\n\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\r\n\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\r\n\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\r\n\t\t\tclasses = classes.join(' ')\r\n\t\t\t//#endif\r\n\t\t\treturn classes\r\n\t\t},\r\n\t\ticonStyle() {\r\n\t\t\tlet style = {}\r\n\t\t\tstyle = {\r\n\t\t\t\tfontSize: this.size == 'inherit' ? 'inherit' : this.$u.addUnit(this.size),\r\n\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\r\n\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\r\n\t\t\t\ttop: this.$u.addUnit(this.top)\r\n\t\t\t}\r\n\t\t\t// 非主题色值时，才当作颜色值\r\n\t\t\tif (this.showDecimalIcon && this.inactiveColor && !this.$u.config.type.includes(this.inactiveColor)) {\r\n\t\t\t\tstyle.color = this.inactiveColor\r\n\t\t\t} else if (this.color && !this.$u.config.type.includes(this.color)) style.color = this.color\r\n\r\n\t\t\treturn style\r\n\t\t},\r\n\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\r\n\t\tisImg() {\r\n\t\t\treturn this.name.indexOf('/') !== -1\r\n\t\t},\r\n\t\timgStyle() {\r\n\t\t\tlet style = {}\r\n\t\t\t// 如果设置width和height属性，则优先使用，否则使用size属性\r\n\t\t\tstyle.width = this.width ? this.$u.addUnit(this.width) : this.$u.addUnit(this.size)\r\n\t\t\tstyle.height = this.height ? this.$u.addUnit(this.height) : this.$u.addUnit(this.size)\r\n\t\t\treturn style\r\n\t\t},\r\n\t\tdecimalIconStyle() {\r\n\t\t\tlet style = {}\r\n\t\t\tstyle = {\r\n\t\t\t\tfontSize: this.size == 'inherit' ? 'inherit' : this.$u.addUnit(this.size),\r\n\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\r\n\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\r\n\t\t\t\ttop: this.$u.addUnit(this.top),\r\n\t\t\t\twidth: this.percent + '%'\r\n\t\t\t}\r\n\t\t\t// 非主题色值时，才当作颜色值\r\n\t\t\tif (this.color && !this.$u.config.type.includes(this.color)) style.color = this.color\r\n\t\t\treturn style\r\n\t\t},\r\n\t\tdecimalIconClass() {\r\n\t\t\tlet classes = []\r\n\t\t\tclasses.push(this.customPrefix + '-' + this.name)\r\n\t\t\t// uView的自定义图标类名为u-iconfont\r\n\t\t\tif (this.customPrefix == 'uicon') {\r\n\t\t\t\tclasses.push('u-iconfont')\r\n\t\t\t} else {\r\n\t\t\t\tclasses.push(this.customPrefix)\r\n\t\t\t}\r\n\t\t\t// 主题色，通过类配置\r\n\t\t\tif (this.color && this.$u.config.type.includes(this.color)) classes.push('u-icon__icon--' + this.color)\r\n\t\t\telse classes.push('u-icon__icon--primary')\r\n\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\r\n\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\r\n\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\r\n\t\t\tclasses = classes.join(' ')\r\n\t\t\t//#endif\r\n\t\t\treturn classes\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tclick() {\r\n\t\t\tthis.$emit('click', this.index)\r\n\t\t},\r\n\t\ttouchstart() {\r\n\t\t\tthis.$emit('touchstart', this.index)\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"../../libs/css/style.components.scss\";\r\n@import '../../iconfont.css';\r\n\r\n.u-icon {\r\n\tdisplay: inline-flex;\r\n\talign-items: center;\r\n\r\n\t&--left {\r\n\t\tflex-direction: row-reverse;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t&--right {\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t&--top {\r\n\t\tflex-direction: column-reverse;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t&--bottom {\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t&__icon {\r\n\t\tposition: relative;\r\n\r\n\t\t&--primary {\r\n\t\t\tcolor: $u-type-primary;\r\n\t\t}\r\n\r\n\t\t&--success {\r\n\t\t\tcolor: $u-type-success;\r\n\t\t}\r\n\r\n\t\t&--error {\r\n\t\t\tcolor: $u-type-error;\r\n\t\t}\r\n\r\n\t\t&--warning {\r\n\t\t\tcolor: $u-type-warning;\r\n\t\t}\r\n\r\n\t\t&--info {\r\n\t\t\tcolor: $u-type-info;\r\n\t\t}\r\n\t}\r\n\r\n\t&__decimal {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tdisplay: inline-block;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t&__img {\r\n\t\theight: auto;\r\n\t\twill-change: transform;\r\n\t}\r\n\r\n\t&__label {\r\n\t\tline-height: 1;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=style&index=0&id=7a019853&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-icon.vue?vue&type=style&index=0&id=7a019853&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753500075018\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}