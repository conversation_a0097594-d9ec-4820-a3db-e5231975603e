(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uview-ui/components/u-navbar/u-navbar"],{"4ff9":function(t,e,n){"use strict";var i=n("fd3d"),a=n.n(i);a.a},"835e":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,i=t.getSystemInfoSync();n=t.getMenuButtonBoundingClientRect();var a={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backBg:{type:String,default:"transparent"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"42"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:n,statusBarHeight:i.statusBarHeight,isHome:!1}},computed:{navbarInnerStyle:function(){var t={};t.height=this.navbarHeight+"px";var e=i.windowWidth-n.left;return t.paddingRight=e+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var e={},a=i.windowWidth-n.left;return e.left=(i.windowWidth-t.upx2px(this.titleWidth))/2+"px",e.right=a-(i.windowWidth-t.upx2px(this.titleWidth))/2+a+"px",e.width=t.upx2px(this.titleWidth)+"px",e},navbarHeight:function(){var t="ios"==i.platform||"devtools"==i.platform?44:48;return this.height?this.height:t}},created:function(){var t=getCurrentPages().length;1==t&&(this.isHome=!0)},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():this.isHome?t.switchTab({url:"/pages/index/index"}):t.navigateBack()}}};e.default=a}).call(this,n("df3c")["default"])},8564:function(t,e,n){"use strict";n.r(e);var i=n("c274"),a=n("cb9d");for(var u in a)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(u);n("4ff9");var r=n("828b"),o=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"52abd0da",null,!1,i["a"],void 0);e["default"]=o.exports},c274:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){return i}));var i={uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))}},a=function(){var t=this,e=t.$createElement,n=(t._self._c,t.__get_style([t.navbarStyle])),i=t.__get_style([t.navbarInnerStyle]),a=t.isBack&&t.backText?t.__get_style([t.backTextStyle]):null,u=t.title?t.__get_style([t.titleStyle]):null,r=t.isFixed&&!t.immersive?Number(t.navbarHeight):null;t.$mp.data=Object.assign({},{$root:{s0:n,s1:i,s2:a,s3:u,m0:r}})},u=[]},cb9d:function(t,e,n){"use strict";n.r(e);var i=n("835e"),a=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(u);e["default"]=a.a},fd3d:function(t,e,n){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uview-ui/components/u-navbar/u-navbar-create-component',
    {
        'components/uview-ui/components/u-navbar/u-navbar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("8564"))
        })
    },
    [['components/uview-ui/components/u-navbar/u-navbar-create-component']]
]);
