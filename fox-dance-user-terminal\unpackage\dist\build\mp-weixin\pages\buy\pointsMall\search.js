(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/buy/pointsMall/search"],{"32f3":function(t,s,e){"use strict";(function(t){var n=e("47a9");Object.defineProperty(s,"__esModule",{value:!0}),s.default=void 0;var o,i=n(e("7ca3")),a={data:function(){return{isLogined:!0,hotLists:["大蒜","胡萝卜","大蒜","胡萝卜","大蒜","胡萝卜"],keywords:"",keywordsLists:[]}},onLoad:function(){this.keywordsLists=""==t.getStorageSync("keywordsLists")?[]:JSON.parse(t.getStorageSync("keywordsLists"))},onShow:function(){},methods:(o={searchTap:function(){t.navigateTo({url:"/pages/buy/pointsMall/searchResults?keywords="+this.keywords})},clearTap:function(){var s=this;t.showModal({title:"提示",content:"确认要清空历史记录吗？",success:function(e){e.confirm?(t.removeStorageSync("keywordsLists"),s.keywordsLists=[]):e.cancel&&console.log("用户点击取消")}})}},(0,i.default)(o,"searchTap",(function(s){for(var e=[],n=0;n<this.keywordsLists.length;n++)this.keywordsLists[n].name==s&&e.push(n);0==s.split(" ").join("").length&&(s=""),this.keywordsLists=0==e.length&&0!=s.length?this.keywordsLists.concat({name:s}):this.keywordsLists,t.setStorageSync("keywordsLists",JSON.stringify(this.keywordsLists)),console.log(s.length,"keywords",this.keywordsLists),t.navigateTo({url:"/pages/buy/pointsMall/searchResults?keywords="+s})})),(0,i.default)(o,"navTo",(function(s){t.navigateTo({url:s})})),o)};s.default=a}).call(this,e("df3c")["default"])},"7e85":function(t,s,e){},9492:function(t,s,e){"use strict";var n=e("7e85"),o=e.n(n);o.a},c630:function(t,s,e){"use strict";e.r(s);var n=e("32f3"),o=e.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){e.d(s,t,(function(){return n[t]}))}(i);s["default"]=o.a},ca84:function(t,s,e){"use strict";(function(t,s){var n=e("47a9");e("cff9");n(e("3240"));var o=n(e("ff51"));t.__webpack_require_UNI_MP_PLUGIN__=e,s(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},cce5:function(t,s,e){"use strict";e.d(s,"b",(function(){return n})),e.d(s,"c",(function(){return o})),e.d(s,"a",(function(){}));var n=function(){var t=this.$createElement,s=(this._self._c,this.keywordsLists.length),e=this.keywordsLists.length;this.$mp.data=Object.assign({},{$root:{g0:s,g1:e}})},o=[]},ff51:function(t,s,e){"use strict";e.r(s);var n=e("cce5"),o=e("c630");for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(s,t,(function(){return o[t]}))}(i);e("9492");var a=e("828b"),r=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);s["default"]=r.exports}},[["ca84","common/runtime","common/vendor"]]]);