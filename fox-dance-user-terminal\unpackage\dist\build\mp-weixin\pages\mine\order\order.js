(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/order/order"],{1654:function(t,e,o){"use strict";(function(t,e){var n=o("47a9");o("cff9");n(o("3240"));var a=n(o("bd0b"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"6bab":function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},a=[]},"8de4":function(t,e,o){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o("6061"),a={data:function(){return{isLogined:!0,type:0,orderLists:[],page:1,total_pages:1,zanwsj:!1,status:"loading",loadingText:"努力加载中",loadmoreText:"轻轻上拉",nomoreText:"实在没有了",imgbaseUrl:"",qjbutton:"#131315"}},onLoad:function(){this.qjbutton=t.getStorageSync("storeInfo").button},onShow:function(){this.imgbaseUrl=this.$baseUrl,this.page=1,this.orderLists=[],this.orderData()},methods:{navTap:function(t){this.type=t,this.page=1,this.orderLists=[],this.orderData()},orderData:function(){t.showLoading({title:"加载中"});var e=this;(0,n.myOrderApi)({page:e.page,size:10,type:e.type}).then((function(o){if(console.log("订单列表",o),1==o.code){var n=o.data.data;e.orderLists=e.orderLists.concat(n),e.zanwsj=!!e.orderLists.length,e.page++,e.total_pages=o.data.last_page,1!=e.page&&(e.total_pages>=e.page?e.status="loading":e.status="nomore"),0==e.orderLists.length?e.zanwsj=!0:e.zanwsj=!1,1*o.data.total<=10&&(e.status="nomore"),e.loding=!0,t.hideLoading(),t.stopPullDownRefresh()}}))},onReachBottom:function(){1!=this.page&&this.total_pages>=this.page&&this.orderData()},onPullDownRefresh:function(){console.log("我被下拉了"),this.page=1,this.orderLists=[],this.orderData()},confirmSubTap:function(e){var o=this;t.showModal({title:"提示",content:"确认要收货吗？",success:function(a){a.confirm?(t.showLoading({title:"加载中"}),(0,n.confirmOrderApi)({id:e}).then((function(e){console.log("确认收货",e),1==e.code&&(t.hideLoading(),o.page=1,o.orderLists=[],o.orderData(),t.showToast({icon:"success",title:"收货成功",duration:2e3}))}))):a.cancel&&console.log("用户点击取消")}})},navTo:function(e){t.navigateTo({url:e})}}};e.default=a}).call(this,o("df3c")["default"])},bd0b:function(t,e,o){"use strict";o.r(e);var n=o("6bab"),a=o("f568");for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(i);var s=o("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"ddc86f42",null,!1,n["a"],void 0);e["default"]=r.exports},f568:function(t,e,o){"use strict";o.r(e);var n=o("8de4"),a=o.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(i);e["default"]=a.a}},[["1654","common/runtime","common/vendor"]]]);