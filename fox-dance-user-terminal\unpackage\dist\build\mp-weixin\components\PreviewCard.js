(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/PreviewCard"],{"2d0c":function(t,i,a){"use strict";a.d(i,"b",(function(){return n})),a.d(i,"c",(function(){return e})),a.d(i,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},e=[]},"34e5":function(t,i,a){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a={name:"PreviewCard",props:{title:{type:String,default:""},tag:{type:String,default:""},image:{type:String,default:""},targetPage:{type:String,default:"/pagesSub/switch/vote"}},computed:{cardStyle:function(){if(!this.animationData&&this.pullDistance>0){var t=1+this.pullDistance/this.pullThreshold*.1,i=1-this.pullDistance/this.pullThreshold*.3;return{transform:"translateY(".concat(this.pullDistance,"px) scale(").concat(t,")"),opacity:i}}return{}},imageStyle:function(){return!this.imageAnimationData&&this.imageScale>1?{transform:"scale(".concat(this.imageScale,")")}:{}}},data:function(){return{touchStartY:0,isPulling:!1,pullThreshold:80,pullDistance:0,animationData:{},animation:null,navigating:!1,imageAnimationData:{},imageScale:1}},created:function(){try{"function"===typeof t.createAnimation&&(this.animation=t.createAnimation({duration:300,timingFunction:"ease-out",delay:0,transformOrigin:"50% 50% 0"}))}catch(i){console.error("创建动画实例失败:",i)}},methods:{handleTouchStart:function(t){if(!this.navigating){var i;if(t.touches&&t.touches[0])i=t.touches[0].clientY;else if(t.changedTouches&&t.changedTouches[0])i=t.changedTouches[0].clientY;else{if(!t.detail)return;i=t.detail.y}this.touchStartY=i,this.isPulling=!1,this.pullDistance=0,this.imageScale=1;try{this.animation&&"function"===typeof this.animation.translateY&&(this.animation.translateY(0).scale(1).opacity(1).step({duration:0}),this.animationData=this.animation.export())}catch(t){console.error("重置动画失败:",t)}}},handleTouchMove:function(t){if(!this.navigating){var i;if(t.touches&&t.touches[0])i=t.touches[0].clientY;else if(t.changedTouches&&t.changedTouches[0])i=t.changedTouches[0].clientY;else{if(!t.detail)return;i=t.detail.y}var a=i-this.touchStartY;if(a>0){this.isPulling=!0,this.pullDistance=Math.min(.5*a,this.pullThreshold);var n=1,e=1,s=1;if(this.pullDistance>.5*this.pullThreshold){var o=(this.pullDistance-.5*this.pullThreshold)/(.5*this.pullThreshold);n=1+.1*o,e=1-.3*o,s=n,this.setImageTransform(s)}try{this.animation&&"function"===typeof this.animation.translateY&&(this.animation.translateY(this.pullDistance).scale(n).opacity(e).step({duration:0}),this.animationData=this.animation.export())}catch(t){console.error("应用动画失败:",t)}t.preventDefault&&t.preventDefault(),this.$emit("pulling",{distance:this.pullDistance,scale:n,imageScale:s})}}},setImageTransform:function(i){var a=this;try{if("function"===typeof t.createAnimation){var n=t.createAnimation({duration:0,timingFunction:"ease-out",transformOrigin:"50% 50% 0"});n.scale(i).step(),this.$nextTick((function(){a.imageAnimationData=n.export()}))}else this.imageScale=i}catch(e){console.error("创建图片动画失败:",e)}},handleTouchEnd:function(){if(!this.navigating&&this.isPulling)try{this.pullDistance>=.6*this.pullThreshold?(console.log("下拉距离满足条件，准备导航"),this.navigateToVote()):this.resetPosition(),this.$emit("touchEnded")}catch(t){console.error("触摸结束处理失败:",t),this.resetPosition()}},resetPosition:function(){var t=this;this.animation.translateY(0).scale(1).opacity(1).step(),this.animationData=this.animation.export(),this.resetImageScale(),setTimeout((function(){t.isPulling=!1,t.pullDistance=0}),300)},resetImageScale:function(){var i=this,a=t.createAnimation({duration:300,timingFunction:"ease-out",transformOrigin:"50% 50% 0"});a.scale(1).step(),this.$nextTick((function(){i.imageAnimationData=a.export()}))},navigateToVote:function(){var i=this;this.navigating=!0;try{this.animation&&"function"===typeof this.animation.translateY&&(this.animation.translateY(this.pullDistance+30).scale(1.2).opacity(0).step(),this.animationData=this.animation.export()),this.resetImageScale(),setTimeout((function(){(function(){console.log("准备导航到:",i.targetPage),t.navigateTo({url:i.targetPage,success:function(){console.log("导航成功"),setTimeout((function(){i.resetAfterNavigation()}),100)},fail:function(t){console.error("导航失败",t),i.resetAfterNavigation()}})})()}),300)}catch(a){console.error("导航过程中出错:",a),t.navigateTo({url:this.targetPage})}},resetAfterNavigation:function(){this.animation.translateY(0).scale(1).opacity(1).step({duration:0}),this.animationData=this.animation.export(),this.isPulling=!1,this.pullDistance=0,this.navigating=!1}},beforeDestroy:function(){this.animation&&(this.animation.translateY(0).scale(1).opacity(1).step({duration:0}),this.animationData=this.animation.export()),this.isPulling=!1,this.pullDistance=0,this.navigating=!1}};i.default=a}).call(this,a("df3c")["default"])},"3c92":function(t,i,a){"use strict";a.r(i);var n=a("34e5"),e=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(i,t,(function(){return n[t]}))}(s);i["default"]=e.a},e818:function(t,i,a){"use strict";a.r(i);var n=a("2d0c"),e=a("3c92");for(var s in e)["default"].indexOf(s)<0&&function(t){a.d(i,t,(function(){return e[t]}))}(s);a("fe93");var o=a("828b"),c=Object(o["a"])(e["default"],n["b"],n["c"],!1,null,"2d6692b0",null,!1,n["a"],void 0);i["default"]=c.exports},eb57:function(t,i,a){},fe93:function(t,i,a){"use strict";var n=a("eb57"),e=a.n(n);e.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/PreviewCard-create-component',
    {
        'components/PreviewCard-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("e818"))
        })
    },
    [['components/PreviewCard-create-component']]
]);
