(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/message/index"],{"25cf":function(e,t,n){},"60de":function(e,t,n){"use strict";n.r(t);var a=n("9be7"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"853c":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("cff9");a(n("3240"));var o=a(n("e80d"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"9be7":function(e,t,n){"use strict";(function(e){var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("7eb4")),r=a(n("7ca3")),s=a(n("ee10")),i=n("41ad");function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,r.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={name:"SocialMessage",data:function(){return{chatList:[],loading:!1,refreshing:!1,systemUnreadCount:3,likeUnreadCount:12,followUnreadCount:5}},onLoad:function(){this.loadUnreadCounts(),this.loadChatList()},onShow:function(){this.chatList&&0!==this.chatList.length||(console.log("消息页显示时重新加载数据"),this.loadUnreadCounts(),this.loadChatList())},activated:function(){this.chatList&&0!==this.chatList.length||(console.log("消息页激活时重新加载数据"),this.loadUnreadCounts(),this.loadChatList())},methods:{loadUnreadCounts:function(){var e=this;return(0,s.default)(o.default.mark((function t(){var n;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,i.getMessageUnreadCount)();case 3:n=t.sent,n&&0===n.code&&n.data&&(e.systemUnreadCount=n.data.total||0,e.likeUnreadCount=0,e.followUnreadCount=0),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("加载未读消息统计失败:",t.t0);case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},loadChatList:function(){var t=this;return(0,s.default)(o.default.mark((function n(){var a,r,s;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(console.log("开始加载聊天列表..."),t.loading=!0,n.prev=2,a=e.getStorageSync("userid"),a){n.next=8;break}return console.error("用户未登录，无法加载聊天列表"),t.chatList=[],n.abrupt("return");case 8:return n.next=10,(0,i.getConversations)({current:1,size:50});case 10:r=n.sent,console.log("聊天列表API返回:",r),s=[],r&&0===r.code&&r.data&&Array.isArray(r.data)?s=r.data:r&&Array.isArray(r)?s=r:(console.log("没有找到聊天列表或格式不正确"),s=[]),s.length>0?(t.chatList=s.map((function(e){return{id:e.id||e.conversationId,userId:e.otherUserId||e.userId,name:e.otherUserNickname||e.nickname||"用户"+(e.otherUserId||e.userId),avatar:t.formatAvatarUrl(e.otherUserAvatar||e.avatar),lastMessage:e.lastMessageContent||"",lastMessageTime:e.lastMessageTime?new Date(e.lastMessageTime):new Date,lastMessageType:t.getMessageTypeString(e.lastMessageType||1),unreadCount:e.unreadCount||0,isOnline:e.isOnline||!1,isMuted:e.isMuted||!1}})),console.log("会话列表",t.chatList),console.log("聊天列表处理完成，会话数量:",t.chatList.length)):(console.log("没有聊天记录，显示空状态"),t.chatList=[]),n.next=22;break;case 17:n.prev=17,n.t0=n["catch"](2),console.error("加载聊天列表失败:",n.t0),e.showToast({title:"加载失败",icon:"none"}),t.chatList=[];case 22:return n.prev=22,t.loading=!1,t.refreshing=!1,n.finish(22);case 26:case"end":return n.stop()}}),n,null,[[2,17,22,26]])})))()},formatAvatarUrl:function(e){return e?e.startsWith("http")?e:"https://file.foxdance.com.cn"+e:"/static/images/toux.png"},getMessageTypeString:function(e){return{1:"text",2:"image",3:"voice",4:"video",5:"file"}[e]||"text"},formatTime:function(e){var t=new Date,n=t-new Date(e),a=Math.floor(n/6e4),o=Math.floor(n/36e5),r=Math.floor(n/864e5);if(a<1)return"刚刚";if(a<60)return"".concat(a,"分钟前");if(o<24)return"".concat(o,"小时前");if(r<7)return"".concat(r,"天前");var s=new Date(e);return"".concat(s.getMonth()+1,"/").concat(s.getDate())},onRefresh:function(){this.refreshing=!0,this.loadChatList()},openChat:function(t){var n=this;return(0,s.default)(o.default.mark((function a(){var r;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(console.log("打开聊天:",t),a.prev=1,!(t.unreadCount>0)){a.next=8;break}return a.next=5,(0,i.markMessageAsRead)({senderId:t.userId,receiverId:e.getStorageSync("userid")});case 5:t.unreadCount=0,r=n.chatList.findIndex((function(e){return e.id===t.id})),-1!==r&&n.$set(n.chatList,r,u({},t));case 8:e.navigateTo({url:"/pagesSub/social/chat/detail?userId=".concat(t.userId,"&nickname=").concat(encodeURIComponent(t.name),"&avatar=").concat(encodeURIComponent(t.avatar))}),a.next=15;break;case 11:a.prev=11,a.t0=a["catch"](1),console.error("打开聊天失败:",a.t0),e.navigateTo({url:"/pagesSub/social/chat/detail?userId=".concat(t.userId,"&nickname=").concat(encodeURIComponent(t.name),"&avatar=").concat(encodeURIComponent(t.avatar))});case 15:case"end":return a.stop()}}),a,null,[[1,11]])})))()},showChatActions:function(t){var n=this,a=["置顶",t.isMuted?"取消免打扰":"免打扰","删除聊天"];e.showActionSheet({itemList:a,success:function(e){switch(e.tapIndex){case 0:n.toggleChatTop(t);break;case 1:n.toggleChatMute(t);break;case 2:n.deleteChat(t);break}}})},toggleChatTop:function(e){this.$u.toast("置顶成功")},toggleChatMute:function(e){e.isMuted=!e.isMuted,this.$u.toast(e.isMuted?"已开启免打扰":"已关闭免打扰")},deleteChat:function(t){var n=this;e.showModal({title:"确认删除",content:"确定要删除这个聊天吗？",success:function(e){if(e.confirm){var a=n.chatList.findIndex((function(e){return e.id===t.id}));a>-1&&(n.chatList.splice(a,1),n.$u.toast("删除成功"))}}})},goSearch:function(){e.navigateTo({url:"/pagesSub/social/search/chat"})},startNewChat:function(){e.navigateTo({url:"/pagesSub/social/contact/select"})},goSystemMessages:function(){e.navigateTo({url:"/pagesSub/social/message/system"})},goLikeMessages:function(){e.navigateTo({url:"/pagesSub/social/message/likes"})},goFollowMessages:function(){e.navigateTo({url:"/pagesSub/social/message/followers"})},forceRefresh:function(){console.log("强制刷新消息页数据..."),this.chatList=[],this.loadUnreadCounts(),this.loadChatList()}}};t.default=l}).call(this,n("df3c")["default"])},e80d:function(e,t,n){"use strict";n.r(t);var a=n("f014"),o=n("60de");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("eff6");var s=n("828b"),i=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"36f9cf8e",null,!1,a["a"],void 0);t["default"]=i.exports},eff6:function(e,t,n){"use strict";var a=n("25cf"),o=n.n(a);o.a},f014:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))},uAvatar:function(){return n.e("components/uview-ui/components/u-avatar/u-avatar").then(n.bind(null,"8d35"))},uLoading:function(){return n.e("components/uview-ui/components/u-loading/u-loading").then(n.bind(null,"2bf0"))}},o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.chatList,(function(t,n){var a=e.__get_orig(t),o=e.formatTime(t.lastMessageTime);return{$orig:a,m0:o}}))),a=!e.chatList.length&&!e.loading;e.$mp.data=Object.assign({},{$root:{l0:n,g0:a}})},r=[]}},[["853c","common/runtime","common/vendor"]]]);