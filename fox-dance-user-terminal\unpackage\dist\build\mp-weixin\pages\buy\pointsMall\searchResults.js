(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/buy/pointsMall/searchResults"],{"3f8c":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},o=[]},4985:function(t,e,a){"use strict";(function(t,e){var n=a("47a9");a("cff9");n(a("3240"));var o=n(a("8923"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"669d":function(t,e,a){"use strict";var n=a("e4b4"),o=a.n(n);o.a},8923:function(t,e,a){"use strict";a.r(e);var n=a("3f8c"),o=a("ce30");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("669d");var s=a("828b"),l=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=l.exports},"9a8d":function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a("6061"),o={data:function(){return{isLogined:!0,shopCateIndex:0,keywords:"",shopCate:[],mallLists:[],page:1,total_pages:1,zanwsj:!1,status:"loading",loadingText:"努力加载中",loadmoreText:"轻轻上拉",nomoreText:"实在没有了",scrollTop:0,score:0,imgbaseUrl:"",qjbutton:"#131315"}},onShow:function(){this.isLogined=!!t.getStorageSync("token"),this.isLogined?this.userData():this.loding=!0,this.imgbaseUrl=this.$baseUrl,this.mallLists=[],this.page=1,this.mallData()},onLoad:function(e){this.qjbutton=t.getStorageSync("storeInfo").button,this.keywords=e.keywords,t.setNavigationBarTitle({title:this.keywords})},methods:{searchTap:function(){this.mallLists=[],this.page=1,this.mallData()},userData:function(){var t=this;(0,n.userInfoApi)({}).then((function(e){console.log("个人中心",e),1==e.code&&(t.score=e.data.score)}))},mallData:function(){t.showLoading({title:"加载中"});var e=this;(0,n.mallListsApi)({name:e.keywords,page:e.page,size:10}).then((function(a){if(console.log("积分商城",a),1==a.code){var n=a.data.data;e.score=a.data.score,e.mallLists=e.mallLists.concat(n),e.zanwsj=!!e.mallLists.length,e.page++,e.total_pages=a.data.last_page,1!=e.page&&(e.total_pages>=e.page?e.status="loading":e.status="nomore"),0==e.mallLists.length?e.zanwsj=!0:e.zanwsj=!1,1*a.data.total<=10&&(e.status="nomore"),e.loding=!0,t.hideLoading(),t.stopPullDownRefresh()}}))},onReachBottom:function(){console.log("到底了"),1!=this.page&&this.total_pages>=this.page&&this.mallData()},onPullDownRefresh:function(){this.page=1,this.mallLists=[],this.mallData()},dhTap:function(e){if(this.isLogined){if(this.score<1*e.redeem_points)return t.showToast({icon:"none",title:"积分不足",duration:2e3}),!1;var a=JSON.stringify(e);t.navigateTo({url:"/pages/buy/pointsMall/confirmOrder?productxq="+a})}else t.showToast({icon:"none",title:"请先登录",duration:2e3}),setTimeout((function(){t.navigateTo({url:"/pages/login/login"})}),1e3)},navTo:function(e,a){if(a)return t.navigateTo({url:e}),!1;""!=t.getStorageSync("token")&&void 0!=t.getStorageSync("token")&&t.getStorageSync("token")?t.navigateTo({url:e}):(t.showToast({icon:"none",title:"请先登录"}),setTimeout((function(){t.navigateTo({url:"/pages/login/login"})}),1e3))}}};e.default=o}).call(this,a("df3c")["default"])},ce30:function(t,e,a){"use strict";a.r(e);var n=a("9a8d"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},e4b4:function(t,e,a){}},[["4985","common/runtime","common/vendor"]]]);