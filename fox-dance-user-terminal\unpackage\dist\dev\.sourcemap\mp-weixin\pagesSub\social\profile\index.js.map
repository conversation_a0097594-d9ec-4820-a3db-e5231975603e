{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?2fa0", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?306b", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?eccc", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?16a1", "uni-app:///pagesSub/social/profile/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?c704", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?e870", "uni-app:///main.js"], "names": ["name", "components", "PostCard", "FollowButton", "data", "userInfo", "userId", "nickname", "avatar", "bio", "danceType", "postCount", "privatePostCount", "followingCount", "followersCount", "likeCount", "draftCount", "loading", "currentTab", "isInitialized", "tabs", "onLoad", "console", "setTimeout", "onShow", "methods", "initializeData", "loadUserInfo", "currentUserId", "uni", "title", "icon", "result", "userProfile", "loadPrivatePostCount", "current", "size", "isPublic", "getCurrentUserId", "formatAvatarUrl", "loadTabData", "tabIndex", "loadUserPosts", "sortField", "sortOrder", "posts", "id", "coverImage", "username", "userAvatar", "post", "content", "commentCount", "isLiked", "status", "createTime", "loadPrivatePosts", "loadLikedPosts", "loadFollowingUsers", "users", "isFollowed", "type", "loadFollowersUsers", "switchTab", "scanCode", "success", "goSettings", "url", "editAvatar", "count", "sizeType", "sourceType", "editProfile", "goLikeList", "viewPost", "goPostDetail", "goUserProfile", "onPostLike", "duration", "currentTabData", "index", "forceRefresh", "tab", "getEmptyText", "onUserFollow", "onUserUnfollow", "onFollowChange", "updateUserFollowStatus", "followingUser", "followerUser", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsIlwB;AASA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC,OACA;QAAApB;QAAAI;QAAAa;MAAA,GACA;QAAAjB;QAAAI;QAAAa;MAAA,GACA;QAAAjB;QAAAI;QAAAa;MAAA,GACA;QAAAjB;QAAAI;QAAAa;MAAA,GACA;QAAAjB;QAAAI;QAAAa;MAAA;IAEA;EACA;EACAI;IAAA;IACAC;IACA;IACA;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAF;IACA;IACA;MACA;IACA;EACA;EACAG;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAJ;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAN;gBACAO;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAT;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAU;gBACAV;gBAAA,MAEAU;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;kBACA3B;kBACAC;kBACAC;kBACAC,KACAwB,mBACAA,2BACA;kBACAvB;kBACAC;kBACAC;kBAAA;kBACAC;kBACAC,gBACAmB;kBACAlB,WACAkB;kBACAjB;gBACA;gBAEAM;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAEAA;gBACAO;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAT;gBACAO;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAC;kBACAC;kBAAA;kBACA9B;kBACA+B;gBACA;cAAA;gBALAL;gBAOA;kBACA;kBACA;oBACA;kBACA;oBACA;oBACA;oBACA;kBACA;gBACA;gBAEAV;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgB;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA,OACA,iCACA/B,SACA;MAEA;MACA;IACA;IAEAgC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGApC;gBAAA,eAEAqC;gBAAA,kCACA,yBAGA,0BAGA,0BAGA,0BAGA;gBAAA;cAAA;gBAAA;gBAAA,OAXA;cAAA;gBAAArC;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAkB;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAd;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAN;gBAAA,kCACA;cAAA;gBAAA;gBAAA,OAGA;kBACAa;kBACAC;kBACA9B;kBACA+B;kBAAA;kBACAM;kBACAC;gBACA;cAAA;gBAPAZ;gBASAV;gBAAA,MAEAU;kBAAA;kBAAA;gBAAA;gBACAa,qCACAb,cACAA;gBAAA,kCACAa;kBAAA;oBACAC;oBACAhB;oBACAiB;oBACAC;oBACAC,YACA,iCACAC,cACA;oBACAC;oBACApC;oBACAqC;oBACAC;oBACAhB;oBAAA;oBACAiB;oBAAA;oBACAC;kBACA;gBAAA;cAAA;gBAEAjC;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA5B;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAN;gBAAA,kCACA;cAAA;gBAAA;gBAAA,OAGA;kBACAa;kBACAC;kBACA9B;kBACA+B;kBAAA;kBACAM;kBACAC;gBACA;cAAA;gBAPAZ;gBASAV;gBAAA,MAEAU;kBAAA;kBAAA;gBAAA;gBACAa,qCACAb,cACAA;gBAAA,kCACAa;kBAAA;oBACAC;oBACAhB;oBACAiB;oBACAC;oBACAC;oBACAE;oBACApC;oBACAqC;oBACAC;oBACAhB;oBAAA;oBACAiB;oBACAC;kBACA;gBAAA;cAAA;gBAEAjC;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA7B;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAN;gBAAA,kCACA;cAAA;gBAGA;gBACAA;gBACA;gBAAA,kCACA;cAAA;gBAAA;gBAAA;gBAEAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA9B;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAN;gBAAA,kCACA;cAAA;gBAGAA;gBAAA;gBAAA,OACA;kBACAa;kBACAC;gBACA;cAAA;gBAHAJ;gBAIAV;gBAAA,MAEAU;kBAAA;kBAAA;gBAAA;gBACA2B,qCACA3B,cACAA;gBAAA,kCACA2B;kBAAA;oBACAb;oBACAxC;oBAAA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAI;oBACA8C;oBAAA;oBACAC;kBACA;gBAAA;cAAA;gBAEAvC;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAlC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAN;gBAAA,kCACA;cAAA;gBAGAA;gBAAA;gBAAA,OACA;kBACAa;kBACAC;gBACA;cAAA;gBAHAJ;gBAIAV;gBAAA,MAEAU;kBAAA;kBAAA;gBAAA;gBACA2B,qCACA3B,cACAA;gBAAA,kCACA2B;kBAAA;oBACAb;oBACAxC;oBAAA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAI;oBACA8C;oBAAA;oBACAC;kBACA;gBAAA;cAAA;gBAEAvC;gBAAA,kCACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAyC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACAnC;QACAoC;UACA3C;QACA;MACA;IACA;IAEA4C;MACArC;QACAsC;MACA;IACA;IAEAC;MAAA;MACAvC;QACAwC;QACAC;QACAC;QACAN;UACA;UACA;QACA;MACA;IACA;IAEAO;MACA3C;QACAsC;MACA;IACA;IAEAM;MACA5C;QACAsC;MACA;IACA;IAEAO;MACA7C;QACAsC;MACA;IACA;IAEA;IACAQ;MACA9C;QACAsC;MACA;IACA;IAEAS;MACA;MACA;QACA;QACA;QACA;UACA/C;YACAsC;UACA;QACA;MACA;QACA;QACA;QACA;QAEAtC;UACAsC;QACA;MACA;IACA;IAEAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEA3B;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAA;gBACAA;gBACArB;kBACAC;kBACAC;kBACA+C;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACA5B;gBACAA;gBACArB;kBACAC;kBACAC;kBACA+C;gBACA;cAAA;gBAGA;gBACAC;gBACAC;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1D;gBACAO;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkD;MACA3D;MACA;MACA;MACA;;MAEA;MACA;QACAhB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAE;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;QACAkE;QACAA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA,gBACA,QACA,UACA,QACA,QACA,OACA;MACA;IACA;IAEA;IACAC;MACA9D;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACA+D;MACA/D;MACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAgE;MACAhE;MACA;MACA;IACA;IAEA;IACAiE;MACA;MACA;QACA;UAAA;QAAA;QACA;UACAC;QACA;MACA;;MAEA;MACA;QACA;UAAA;QAAA;QACA;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1vBA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/profile/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4df458ff&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4df458ff\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4df458ff&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-empty/u-empty\" */ \"@/components/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.tabs[_vm.currentTab] && _vm.tabs[_vm.currentTab].data.length > 0\n  var l0 =\n    g0 &&\n    !(_vm.currentTab === 0 || _vm.currentTab === 1 || _vm.currentTab === 4) &&\n    (_vm.currentTab === 2 || _vm.currentTab === 3)\n      ? _vm.__map(_vm.tabs[_vm.currentTab].data, function (user, __i1__) {\n          var $orig = _vm.__get_orig(user)\n          var a0 = {\n            id: user.id,\n            nickname: user.nickname,\n          }\n          return {\n            $orig: $orig,\n            a0: a0,\n          }\n        })\n      : null\n  var m0 = !g0 ? _vm.getEmptyText() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"profile-container\">\n    <!-- 蓝色渐变背景头部 -->\n    <view class=\"header-section\">\n      <view class=\"header-bg\"></view>\n\n      <!-- 顶部操作按钮 -->\n      <view class=\"header-actions\">\n        <u-icon name=\"scan\" color=\"#fff\" size=\"48rpx\" @click=\"scanCode\"></u-icon>\n        <u-icon name=\"setting\" color=\"#fff\" size=\"48rpx\" @click=\"goSettings\"></u-icon>\n      </view>\n\n      <!-- 用户信息 -->\n      <view class=\"user-info-section\">\n        <view class=\"user-avatar-container\">\n          <u-avatar :src=\"userInfo.avatar\" size=\"120\" @click=\"editAvatar\"></u-avatar>\n        </view>\n\n        <!-- 用户信息内容容器 -->\n        <view class=\"user-info-content\">\n          <!-- 加载状态 -->\n          <view v-if=\"loading\" class=\"loading-container\">\n            <view class=\"loading-spinner\"></view>\n            <text class=\"loading-text\">加载中...</text>\n          </view>\n\n          <!-- 用户信息 -->\n          <view v-else class=\"user-info-row\">\n            <!-- 左侧用户信息 -->\n            <view class=\"user-details\">\n              <text class=\"nickname\">{{ userInfo.nickname || '加载中...' }}</text>\n              <text class=\"dance-type\">舞种：{{ userInfo.danceType || '--' }}</text>\n              <text class=\"bio\">{{ userInfo.bio || '暂无个人简介' }}</text>\n            </view>\n\n            <!-- 右侧编辑链接 -->\n            <view class=\"edit-section\">\n              <text class=\"edit-link\" @click=\"editProfile\">编辑资料</text>\n            </view>\n          </view>\n\n          <!-- 数据统计 -->\n          <view class=\"stats-row\">\n            <view class=\"stat-item\" @click=\"switchTab(0)\">\n              <text class=\"stat-number\">{{ userInfo.postCount }}</text>\n              <text class=\"stat-label\">帖子</text>\n            </view>\n            <view class=\"stat-item\" @click=\"switchTab(2)\">\n              <text class=\"stat-number\">{{ userInfo.followingCount }}</text>\n              <text class=\"stat-label\">关注</text>\n            </view>\n            <view class=\"stat-item\" @click=\"switchTab(3)\">\n              <text class=\"stat-number\">{{ userInfo.followersCount }}</text>\n              <text class=\"stat-label\">粉丝</text>\n            </view>\n            <view class=\"stat-item\">\n              <text class=\"stat-number\">{{ userInfo.likeCount }}</text>\n              <text class=\"stat-label\">获赞</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 操作按钮 -->\n      <view class=\"tabs-container\">\n        <u-tabs\n          :list=\"tabs\"\n          :current=\"currentTab\"\n          @change=\"switchTab\"\n          lineWidth=\"30\"\n          lineColor=\"#303133\"\n          :activeStyle=\"{ color: '#303133', fontWeight: 'bold' }\"\n          :inactiveStyle=\"{ color: '#606266' }\"\n        ></u-tabs>\n      </view>\n\n      <!-- 内容列表 -->\n      <view class=\"posts-content\">\n        <view v-if=\"tabs[currentTab] && tabs[currentTab].data.length > 0\">\n          <!-- 帖子列表 (作品、私密作品、喜欢) -->\n          <view v-if=\"currentTab === 0 || currentTab === 1 || currentTab === 4\" class=\"post-grid\">\n            <PostCard\n              v-for=\"post in tabs[currentTab].data\"\n              :key=\"post.id\"\n              :post=\"post\"\n              class=\"post-card-item\"\n              @click=\"goPostDetail\"\n              @user-click=\"goUserProfile\"\n              @like=\"onPostLike\"\n            />\n          </view>\n\n          <!-- 用户列表 (关注、粉丝) -->\n          <view v-else-if=\"currentTab === 2 || currentTab === 3\" class=\"user-list\">\n            <view\n              v-for=\"user in tabs[currentTab].data\"\n              :key=\"user.id\"\n              class=\"user-item\"\n              @click=\"goUserProfile(user)\"\n            >\n              <u-avatar :src=\"user.avatar\" size=\"80\" class=\"user-avatar\"></u-avatar>\n              <view class=\"user-info\">\n                <text class=\"user-nickname\">{{ user.nickname }}</text>\n                <text class=\"user-bio\">{{ user.bio }}</text>\n                <text class=\"user-stats\">{{ user.followersCount }} 粉丝</text>\n              </view>\n              <view class=\"user-action\">\n                <FollowButton\n                  :user=\"{ id: user.id, nickname: user.nickname }\"\n                  :followed=\"user.isFollowed\"\n                  size=\"mini\"\n                  @follow=\"onUserFollow\"\n                  @unfollow=\"onUserUnfollow\"\n                  @change=\"onFollowChange\"\n                  @click.stop\n                />\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <view v-else class=\"empty-state\">\n          <u-empty mode=\"list\" :text=\"getEmptyText()\"></u-empty>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport PostCard from \"../components/PostCard.vue\";\nimport FollowButton from \"../components/FollowButton.vue\";\nimport {\n  getUserProfile,\n  updateUserProfile,\n  getPostList,\n  likePost,\n  unlikePost,\n  getFollowingList,\n  getFollowersList,\n  getUserPostStats\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialProfile\",\n  components: {\n    PostCard,\n    FollowButton\n  },\n  data() {\n    return {\n      userInfo: {\n        userId: \"\",\n        nickname: \"\",\n        avatar: \"\",\n        bio: \"\",\n        danceType: \"\",\n        postCount: 0,\n        privatePostCount: 0, // 私密作品数\n        followingCount: 0,\n        followersCount: 0,\n        likeCount: 0,\n        draftCount: 0\n      },\n      loading: true,\n      currentTab: 0,\n      isInitialized: false,\n      tabs: [\n        { name: \"作品\", data: [], loading: false },\n        { name: \"私密作品\", data: [], loading: false },\n        { name: \"关注\", data: [], loading: false },\n        { name: \"粉丝\", data: [], loading: false },\n        { name: \"喜欢\", data: [], loading: false }\n      ]\n    };\n  },\n  onLoad() {\n    console.log(\"Profile页面 onLoad\");\n    // 延迟加载，确保页面完全初始化\n    this.$nextTick(() => {\n      setTimeout(() => {\n        this.initializeData();\n      }, 100);\n    });\n  },\n  onShow() {\n    console.log(\"Profile页面 onShow\");\n    // 页面显示时刷新数据\n    if (this.isInitialized) {\n      this.loadUserInfo();\n    }\n  },\n  methods: {\n    // 初始化数据\n    async initializeData() {\n      console.log(\"初始化Profile页面数据...\");\n      try {\n        await this.loadUserInfo();\n        this.isInitialized = true;\n      } catch (error) {\n        console.error(\"初始化数据失败:\", error);\n      }\n    },\n\n    async loadUserInfo() {\n      try {\n        // 获取当前用户ID\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载用户信息\");\n          uni.showToast({\n            title: \"请先登录\",\n            icon: \"none\"\n          });\n          return;\n        }\n\n        console.log(\"开始加载用户信息 - userId:\", currentUserId);\n        this.loading = true;\n\n        const result = await getUserProfile(currentUserId);\n        console.log(\"用户信息API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const userProfile = result.data;\n          this.userInfo = {\n            userId: userProfile.id || userProfile.userId || currentUserId,\n            nickname: userProfile.nickname || \"舞蹈爱好者\",\n            avatar: this.formatAvatarUrl(userProfile.avatar),\n            bio:\n              userProfile.bio ||\n              userProfile.userProfile ||\n              \"热爱舞蹈，享受生活\",\n            danceType: userProfile.danceType || \"街舞\",\n            postCount: userProfile.postCount || 0,\n            privatePostCount: 0, // 先设为0，后面会更新\n            followingCount: userProfile.followingCount || 0,\n            followersCount:\n              userProfile.followersCount || userProfile.followerCount || 0,\n            likeCount:\n              userProfile.likeReceivedCount || userProfile.likeCount || 0,\n            draftCount: userProfile.draftCount || 0\n          };\n\n          console.log(\"用户信息加载成功:\", this.userInfo);\n\n          // 加载私密作品数量\n          await this.loadPrivatePostCount(currentUserId);\n\n          // 加载用户帖子数据\n          this.loadTabData(this.currentTab);\n        } else {\n          console.error(\"用户信息API返回格式不正确:\", result);\n          uni.showToast({\n            title: \"加载用户信息失败\",\n            icon: \"none\"\n          });\n        }\n      } catch (error) {\n        console.error(\"加载用户信息失败:\", error);\n        uni.showToast({\n          title: \"加载失败，请重试\",\n          icon: \"none\"\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 加载私密作品数量\n    async loadPrivatePostCount(userId) {\n      try {\n        // 使用getPostList API获取私密作品数量\n        const result = await getPostList({\n          current: 1,\n          size: 1, // 只需要获取数量，不需要具体数据\n          userId: userId,\n          isPublic: 0 // 只查询私密作品\n        });\n\n        if (result && result.code === 0 && result.data) {\n          // 如果返回的是分页对象，获取total字段\n          if (result.data.total !== undefined) {\n            this.userInfo.privatePostCount = result.data.total;\n          } else if (Array.isArray(result.data)) {\n            // 如果返回的是数组，需要再次查询获取准确数量\n            // 这里暂时设为数组长度，实际应该有total字段\n            this.userInfo.privatePostCount = result.data.length;\n          }\n        }\n\n        console.log(\"私密作品数量:\", this.userInfo.privatePostCount);\n      } catch (error) {\n        console.error(\"加载私密作品数量失败:\", error);\n        this.userInfo.privatePostCount = 0;\n      }\n    },\n\n    // 获取当前用户ID\n    getCurrentUserId() {\n      const userId = uni.getStorageSync(\"userid\");\n      return userId ? Number(userId) : null;\n    },\n\n    // 格式化头像URL\n    formatAvatarUrl(avatar) {\n      if (avatar) {\n        return (\n          \"https://file.foxdance.com.cn\" +\n          avatar +\n          \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\"\n        );\n      }\n      return \"static/images/toux.png\";\n    },\n\n    async loadTabData(tabIndex) {\n      if (this.tabs[tabIndex].loading) return;\n\n      this.$set(this.tabs[tabIndex], \"loading\", true);\n\n      try {\n        let data = [];\n\n        switch (tabIndex) {\n          case 0: // 作品\n            data = await this.loadUserPosts();\n            break;\n          case 1: // 私密作品\n            data = await this.loadPrivatePosts();\n            break;\n          case 2: // 关注\n            data = await this.loadFollowingUsers();\n            break;\n          case 3: // 粉丝\n            data = await this.loadFollowersUsers();\n            break;\n          case 4: // 喜欢\n            data = await this.loadLikedPosts();\n            break;\n        }\n\n        this.$set(this.tabs[tabIndex], \"data\", data);\n      } catch (error) {\n        console.error(`加载标签页${tabIndex}数据失败:`, error);\n        this.$set(this.tabs[tabIndex], \"data\", []);\n      } finally {\n        this.$set(this.tabs[tabIndex], \"loading\", false);\n      }\n    },\n\n    // 加载用户发布的公开帖子\n    async loadUserPosts() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载帖子\");\n          return [];\n        }\n\n        const result = await getPostList({\n          current: 1,\n          size: 20,\n          userId: currentUserId,\n          isPublic: 1, // 只加载公开帖子\n          sortField: \"createTime\",\n          sortOrder: \"desc\"\n        });\n\n        console.log(\"用户帖子API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const posts = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          return posts.map(post => ({\n            id: post.id,\n            title: post.title || \"\",\n            coverImage: post.coverImage,\n            username: post.nickname || this.userInfo.nickname,\n            userAvatar:\n              \"https://file.foxdance.com.cn\" +\n              post.avatar +\n              \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\",\n            content: post.content,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            isPublic: post.isPublic, // 添加私密状态字段\n            status: post.status, // 添加帖子状态字段\n            createTime: new Date(post.createTime)\n          }));\n        } else {\n          console.log(\"用户帖子API返回格式不正确:\", result);\n          return [];\n        }\n      } catch (error) {\n        console.error(\"加载用户帖子失败:\", error);\n        return [];\n      }\n    },\n\n    // 加载用户发布的私密帖子\n    async loadPrivatePosts() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载私密帖子\");\n          return [];\n        }\n\n        const result = await getPostList({\n          current: 1,\n          size: 20,\n          userId: currentUserId,\n          isPublic: 0, // 只加载私密帖子\n          sortField: \"createTime\",\n          sortOrder: \"desc\"\n        });\n\n        console.log(\"用户私密帖子API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const posts = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          return posts.map(post => ({\n            id: post.id,\n            title: post.title || \"\",\n            coverImage: post.coverImage,\n            username: post.nickname || this.userInfo.nickname,\n            userAvatar: \"https://file.foxdance.com.cn\" + post.avatar,\n            content: post.content,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            isPublic: post.isPublic, // 私密帖子标识\n            status: post.status,\n            createTime: new Date(post.createTime)\n          }));\n        } else {\n          console.log(\"用户私密帖子API返回格式不正确:\", result);\n          return [];\n        }\n      } catch (error) {\n        console.error(\"加载用户私密帖子失败:\", error);\n        return [];\n      }\n    },\n\n    // 加载用户点赞的帖子\n    async loadLikedPosts() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载点赞帖子\");\n          return [];\n        }\n\n        // TODO: 实现获取用户点赞帖子的API\n        console.log(\"加载用户点赞的帖子 - userId:\", currentUserId);\n        // 暂时返回空数组，等待后端API实现\n        return [];\n      } catch (error) {\n        console.error(\"加载点赞帖子失败:\", error);\n        return [];\n      }\n    },\n\n    // 加载关注的用户\n    async loadFollowingUsers() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载关注列表\");\n          return [];\n        }\n\n        console.log(\"开始加载关注用户列表...\");\n        const result = await getFollowingList(currentUserId, {\n          current: 1,\n          size: 50\n        });\n        console.log(\"关注用户API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const users = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          return users.map(user => ({\n            id: user.userId || user.id,\n            userId: user.userId || user.id, // 确保有userId字段\n            nickname: user.nickname || \"用户\",\n            avatar: this.formatAvatarUrl(user.avatar),\n            bio: user.bio || \"暂无简介\",\n            danceType: user.danceType || \"\",\n            followersCount: user.followerCount || 0,\n            isFollowed: true, // 关注列表中的用户都是已关注的\n            type: \"user\" // 标识这是用户类型数据\n          }));\n        } else {\n          console.error(\"关注用户API返回格式不正确:\", result);\n          return [];\n        }\n      } catch (error) {\n        console.error(\"加载关注用户失败:\", error);\n        return [];\n      }\n    },\n\n    // 加载粉丝用户\n    async loadFollowersUsers() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载粉丝列表\");\n          return [];\n        }\n\n        console.log(\"开始加载粉丝用户列表...\");\n        const result = await getFollowersList(currentUserId, {\n          current: 1,\n          size: 50\n        });\n        console.log(\"粉丝用户API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const users = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          return users.map(user => ({\n            id: user.userId || user.id,\n            userId: user.userId || user.id, // 确保有userId字段\n            nickname: user.nickname || \"用户\",\n            avatar: this.formatAvatarUrl(user.avatar),\n            bio: user.bio || \"暂无简介\",\n            danceType: user.danceType || \"\",\n            followersCount: user.followerCount || 0,\n            isFollowed: user.isFollowed || false, // 需要检查是否互相关注\n            type: \"user\" // 标识这是用户类型数据\n          }));\n        } else {\n          console.error(\"粉丝用户API返回格式不正确:\", result);\n          return [];\n        }\n      } catch (error) {\n        console.error(\"加载粉丝用户失败:\", error);\n        return [];\n      }\n    },\n\n    switchTab(item) {\n      const index = typeof item === \"object\" ? item.index : item;\n      if (this.currentTab === index) return;\n      this.currentTab = index;\n      this.loadTabData(index);\n    },\n\n    scanCode() {\n      uni.scanCode({\n        success: res => {\n          console.log(\"扫码结果:\", res);\n        }\n      });\n    },\n\n    goSettings() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/settings/index\"\n      });\n    },\n\n    editAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: [\"compressed\"],\n        sourceType: [\"album\", \"camera\"],\n        success: res => {\n          // 上传头像\n          this.userInfo.avatar = res.tempFilePaths[0];\n        }\n      });\n    },\n\n    editProfile() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/profile/edit\"\n      });\n    },\n\n    goLikeList() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/like/list\"\n      });\n    },\n\n    viewPost(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      });\n    },\n\n    // PostCard组件需要的方法\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      });\n    },\n\n    goUserProfile(data) {\n      // 判断是帖子数据还是用户数据\n      if (data.type === \"user\") {\n        // 用户数据，直接跳转\n        const userId = data.id || data.userId;\n        if (userId) {\n          uni.navigateTo({\n            url: `/pagesSub/social/user/profile?userId=${userId}&name=${data.nickname}`\n          });\n        }\n      } else {\n        // 帖子数据，跳转到帖子作者页面\n        // 如果是自己的帖子，不需要跳转\n        if (data.username === this.userInfo.nickname) return;\n\n        uni.navigateTo({\n          url: `/pagesSub/social/user/profile?id=${data.userId || data.id}`\n        });\n      }\n    },\n\n    async onPostLike(post) {\n      try {\n        if (post.isLiked) {\n          // 取消点赞\n          await unlikePost(post.id);\n          post.isLiked = false;\n          post.likeCount = Math.max(0, post.likeCount - 1);\n          uni.showToast({\n            title: \"取消点赞\",\n            icon: \"none\",\n            duration: 1000\n          });\n        } else {\n          // 点赞\n          await likePost(post.id);\n          post.isLiked = true;\n          post.likeCount += 1;\n          uni.showToast({\n            title: \"点赞成功\",\n            icon: \"success\",\n            duration: 1000\n          });\n        }\n\n        // 更新对应标签页中的帖子数据\n        const currentTabData = this.tabs[this.currentTab].data;\n        const index = currentTabData.findIndex(p => p.id === post.id);\n        if (index !== -1) {\n          this.$set(currentTabData, index, { ...post });\n        }\n      } catch (error) {\n        console.error(\"点赞操作失败:\", error);\n        uni.showToast({\n          title: \"操作失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    // 强制刷新页面数据（供父组件调用）\n    forceRefresh() {\n      console.log(\"强制刷新个人资料页面数据...\");\n      // 重置数据状态\n      this.isInitialized = false;\n      this.loading = true;\n\n      // 清空当前数据\n      this.userInfo = {\n        userId: \"\",\n        nickname: \"\",\n        avatar: \"\",\n        bio: \"\",\n        danceType: \"\",\n        postCount: 0,\n        followingCount: 0,\n        followersCount: 0,\n        likeCount: 0,\n        draftCount: 0\n      };\n\n      // 清空tabs数据\n      this.tabs.forEach(tab => {\n        tab.data = [];\n        tab.loading = false;\n      });\n\n      // 重新初始化数据\n      this.initializeData();\n    },\n\n    // 获取空状态文本\n    getEmptyText() {\n      const tabNames = [\n        \"暂无作品\",\n        \"暂无私密作品\",\n        \"暂无关注\",\n        \"暂无粉丝\",\n        \"暂无喜欢\"\n      ];\n      return tabNames[this.currentTab] || \"暂无内容\";\n    },\n\n    // 用户关注成功事件\n    onUserFollow(data) {\n      console.log(\"用户关注成功:\", data);\n      // 更新本地数据\n      this.updateUserFollowStatus(data.user.id, true);\n\n      // 更新关注数统计\n      this.userInfo.followingCount += 1;\n    },\n\n    // 用户取消关注成功事件\n    onUserUnfollow(data) {\n      console.log(\"用户取消关注成功:\", data);\n      // 更新本地数据\n      this.updateUserFollowStatus(data.user.id, false);\n\n      // 更新关注数统计\n      if (this.userInfo.followingCount > 0) {\n        this.userInfo.followingCount -= 1;\n      }\n    },\n\n    // 关注状态变化事件\n    onFollowChange(data) {\n      console.log(\"关注状态变化:\", data);\n      // 更新本地数据\n      this.updateUserFollowStatus(data.user.id, data.isFollowed);\n    },\n\n    // 更新用户关注状态的辅助方法\n    updateUserFollowStatus(userId, isFollowed) {\n      // 更新关注列表中的状态\n      if (this.tabs[2] && this.tabs[2].data) {\n        const followingUser = this.tabs[2].data.find(u => u.id === userId);\n        if (followingUser) {\n          followingUser.isFollowed = isFollowed;\n        }\n      }\n\n      // 更新粉丝列表中的状态\n      if (this.tabs[3] && this.tabs[3].data) {\n        const followerUser = this.tabs[3].data.find(u => u.id === userId);\n        if (followerUser) {\n          followerUser.isFollowed = isFollowed;\n        }\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-container {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding-bottom: 100rpx;\n}\n\n.header-section {\n  position: relative;\n  background: #fff;\n}\n\n.header-bg {\n  height: 400rpx;\n  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);\n}\n\n.header-actions {\n  position: absolute;\n  top: 60rpx;\n  right: 32rpx;\n  display: flex;\n  gap: 32rpx;\n  z-index: 10;\n}\n\n.user-info-section {\n  padding: 40rpx 50rpx;\n  background: #f8f9fa;\n}\n\n.user-info-content {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-top: 50rpx;\n  border: 1rpx solid #e9ecef;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.user-avatar-container {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 24rpx;\n  position: absolute;\n  top: 340rpx;\n  left: 12%;\n}\n\n.user-info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 24rpx;\n  padding: 20rpx 40rpx;\n}\n\n.user-details {\n  flex: 1;\n  text-align: left;\n}\n\n.edit-section {\n  flex-shrink: 0;\n  margin-left: 20rpx;\n  display: flex;\n  align-items: flex-start;\n}\n\n.edit-link {\n  font-size: 28rpx;\n  font-weight: 500;\n  border: 1rpx solid #2979ff;\n  border-radius: 10rpx;\n  padding: 10rpx 20rpx;\n  margin: 10rpx;\n  color: #2979ff;\n}\n\n.nickname {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n\n\n.dance-type {\n  font-size: 26rpx;\n  color: #999;\n  display: block;\n  margin-bottom: 16rpx;\n  font-weight: 500;\n}\n\n.bio {\n  font-size: 28rpx;\n  color: #999;\n  line-height: 1.4;\n  display: block;\n}\n\n.stats-row {\n  display: flex;\n  justify-content: center;\n  gap: 80rpx;\n  margin-bottom: 0;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-number {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.tabs-container {\n  background: #fff;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.content {\n  background: #fff;\n  min-height: 60vh;\n}\n\n.posts-content {\n  padding: 32rpx;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 14rpx;\n}\n\n.post-card-item {\n  width: calc(50% - 8rpx);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n\n/* Loading 相关样式 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx 0;\n}\n\n.loading-spinner {\n  width: 40rpx;\n  height: 40rpx;\n  border: 4rpx solid #f3f3f3;\n  border-top: 4rpx solid #007aff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20rpx;\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 用户列表样式 */\n.user-list {\n  padding: 0 32rpx;\n}\n\n.user-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.user-item:last-child {\n  border-bottom: none;\n}\n\n.user-avatar {\n  margin-right: 24rpx;\n}\n\n.user-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.user-nickname {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.user-bio {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 8rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.user-stats {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.user-action {\n  margin-left: 24rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753622789358\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/profile/index.vue'\ncreatePage(Page)"], "sourceRoot": ""}