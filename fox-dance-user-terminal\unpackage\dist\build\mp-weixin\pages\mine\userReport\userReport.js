(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/userReport/userReport"],{3054:function(t,e,o){"use strict";o.r(e);var s=o("c5ca"),a=o.n(s);for(var n in s)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return s[t]}))}(n);e["default"]=a.a},"461f":function(t,e,o){"use strict";o.r(e);var s=o("9bc4"),a=o("3054");for(var n in a)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(n);o("c6e4");var i=o("828b"),r=Object(i["a"])(a["default"],s["b"],s["c"],!1,null,"5f0b7b2c",null,!1,s["a"],void 0);e["default"]=r.exports},"9bc4":function(t,e,o){"use strict";o.d(e,"b",(function(){return s})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var s=function(){var t=this.$createElement;this._self._c},a=[]},c5ca:function(t,e,o){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=o("6061"),a={data:function(){return{isLogined:!0,navLists:["全部","周报","月报","年报"],type:0,courseLists:[],page:1,total_pages:1,zanwsj:!1,status:"loading",loadingText:"努力加载中",loadmoreText:"轻轻上拉",nomoreText:"实在没有了",imgbaseUrl:"",qjbutton:"#131315"}},onLoad:function(){this.qjbutton=t.getStorageSync("storeInfo").button},onShow:function(){this.imgbaseUrl=this.$baseUrl,this.page=1,this.courseLists=[],this.courseData()},methods:{navTap:function(t){this.type=t,this.page=1,this.courseLists=[],this.courseData()},courseData:function(){t.showLoading({title:"加载中"});var e=this;(0,s.reportListApi)({page:e.page,size:10,type:e.type}).then((function(o){if(console.log("报告列表",o),1==o.code){var s=o.data.data;e.courseLists=e.courseLists.concat(s),e.zanwsj=!!e.courseLists.length,e.page++,e.total_pages=o.data.last_page,1!=e.page&&(e.total_pages>=e.page?e.status="loading":e.status="nomore"),0==e.courseLists.length?e.zanwsj=!0:e.zanwsj=!1,1*o.data.total<=10&&(e.status="nomore"),e.loding=!0,t.hideLoading(),t.stopPullDownRefresh()}}))},onReachBottom:function(){1!=this.page&&this.total_pages>=this.page&&this.courseData()},onPullDownRefresh:function(){console.log("我被下拉了"),this.page=1,this.courseLists=[],this.courseData()},dhTap:function(e){t.openLocation({name:e.course.store.address,latitude:1*e.course.store.latitude,longitude:1*e.course.store.longitude,success:function(){console.log("success")}})},userReportTap:function(e){t.navigateTo({url:3==e?"/pages/mine/userReport/weeksUserReport?id="+item.id:4==e?"/pages/mine/userReport/monthUserReport?id="+item.id:5==e?"/pages/mine/userReport/yearsUserReport?id="+item.id:""})},navTo:function(e){t.navigateTo({url:e})}}};e.default=a}).call(this,o("df3c")["default"])},c6e4:function(t,e,o){"use strict";var s=o("f5ae"),a=o.n(s);a.a},ea5e:function(t,e,o){"use strict";(function(t,e){var s=o("47a9");o("cff9");s(o("3240"));var a=s(o("461f"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},f5ae:function(t,e,o){}},[["ea5e","common/runtime","common/vendor"]]]);