(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/prizedraw/hb_confirmOrders"],{1216:function(e,t,n){},8581:function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n("6061"),o={data:function(){return{isLogined:!0,productxq:{id:1},imgbaseUrl:"",remark:"",shouAddr:{area:""},switchVal:!1,jpid:0,price:0,payment_code:"",qjbutton:"#131315"}},onShow:function(){e.getStorageSync("diancan")?this.shouAddr=e.getStorageSync("diancan"):this.shouAddr={area:""},this.userData()},onLoad:function(t){this.qjbutton=e.getStorageSync("storeInfo").button,this.imgbaseUrl=this.$baseUrl,this.addressData(),console.log(this.productxq),this.jpid=t.jpid,this.price=t.price,e.getStorageSync("skxxMr")||e.setStorageSync("skxxMr",1)},onUnload:function(){console.log("onHide","onHide"),2==e.getStorageSync("skxxMr")&&""!=this.payment_code&&null!=this.payment_code&&(console.log(this.payment_code,"this.payment_code"),this.tjxxTap("notz"))},methods:{dhSubTap:function(){if(null==this.payment_code||""==this.payment_code)return e.showToast({title:"请传收款信息",icon:"none",duration:2e3}),!1;e.showLoading({title:"加载中"});var t=this;"cj"==e.getStorageSync("dy_type")&&(0,a.exchangeRedPackApi)({id:t.jpid}).then((function(n){console.log("红包兑换提交",n),1==n.code&&(2==e.getStorageSync("skxxMr")?t.tjxxTap():(e.hideLoading(),e.redirectTo({url:"/pages/prizedraw/success"})))})),"dj"==e.getStorageSync("dy_type")&&(0,a.exchangeRedPackLevelApi)({id:t.jpid}).then((function(n){console.log("红包兑换提交",n),1==n.code&&(2==e.getStorageSync("skxxMr")?t.tjxxTap():(e.hideLoading(),e.redirectTo({url:"/pages/prizedraw/success"})))}))},tjxxTap:function(t){(0,a.paymentcodeApi)({payment_code:""}).then((function(n){1==n.code&&(t||(e.hideLoading(),e.redirectTo({url:"/pages/prizedraw/success"})))}))},userData:function(){e.showLoading({title:"加载中"});var t=this;(0,a.userInfoApi)({}).then((function(n){1==n.code&&(console.log("个人信息",n),t.payment_code=n.data.payment_code,e.hideLoading())}))},change:function(e){console.log("change"),this.switchVal=e},goToAddr:function(t){e.navigateTo({url:"/pages/mine/address?type=".concat(t)})},addressData:function(){var t=this;(0,a.addrList)({page:1,size:999}).then((function(n){if(1==n.code){console.log(n,"地址列表");for(var a=[],o=0;o<n.data.length;o++)1==n.data[o].is_default&&a.push(n.data[o]);0==a.length?t.shouAddr={area:""}:(t.shouAddr=a[0],e.setStorageSync("diancan",a[0]))}else t.mescroll.endErr()}))},navTo:function(t){e.navigateTo({url:t})}}};t.default=o}).call(this,n("df3c")["default"])},"86da":function(e,t,n){"use strict";n.r(t);var a=n("cbd5"),o=n("e0ca");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("9a9a");var c=n("828b"),r=Object(c["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=r.exports},"9a9a":function(e,t,n){"use strict";var a=n("1216"),o=n.n(a);o.a},a787:function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("cff9");a(n("3240"));var o=a(n("86da"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},cbd5:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){}));var a=function(){var e=this.$createElement;this._self._c},o=[]},e0ca:function(e,t,n){"use strict";n.r(t);var a=n("8581"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a}},[["a787","common/runtime","common/vendor"]]]);