(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/order/logistics"],{4166:function(t,n,e){},4648:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},a=[]},"53ab":function(t,n,e){"use strict";(function(t,n){var i=e("47a9");e("cff9");i(e("3240"));var a=i(e("ae42"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"6cc6":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=e("6061"),a={data:function(){return{isLogined:!0,name:"",images:"",imgbaseUrl:"",logisticsLists:[{},{},{},{},{}],loding:!1,logisticsData:{}}},onShow:function(){},onLoad:function(t){console.log(t,"option"),this.imgbaseUrl=this.$baseUrl,this.name=t.name,this.images=t.images,this.wlData(t.id,t.type)},methods:{wlData:function(n,e){var a=this;t.showLoading({title:"加载中"}),(0,i.expressApi)({id:n,type:e?2:1}).then((function(n){console.log("物流信息",n),1==n.code?(a.loding=!0,a.logisticsData=n.data,t.hideLoading()):(t.hideLoading(),t.showToast({icon:"none",title:"暂无物流信息",duration:2e3}),setTimeout((function(){t.navigateBack({})}),2e3))}))}}};n.default=a}).call(this,e("df3c")["default"])},"9c87":function(t,n,e){"use strict";var i=e("4166"),a=e.n(i);a.a},ae42:function(t,n,e){"use strict";e.r(n);var i=e("4648"),a=e("dea2");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("9c87");var c=e("828b"),s=Object(c["a"])(a["default"],i["b"],i["c"],!1,null,"118ec276",null,!1,i["a"],void 0);n["default"]=s.exports},dea2:function(t,n,e){"use strict";e.r(n);var i=e("6cc6"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a}},[["53ab","common/runtime","common/vendor"]]]);