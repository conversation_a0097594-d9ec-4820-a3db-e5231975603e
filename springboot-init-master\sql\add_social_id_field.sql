-- 为ba_user表新增social_id字段
-- 用于显示用户的社区ID，区别于系统内部的id字段

USE admin_foxdance_c;

-- 添加social_id字段
ALTER TABLE ba_user 
ADD COLUMN social_id VARCHAR(32) NULL COMMENT '社区ID，用于用户展示' 
AFTER nickname;

-- 为现有用户生成social_id（可选：基于用户ID生成唯一的社区ID）
-- 格式：FOX + 6位数字（补零）
UPDATE ba_user 
SET social_id = CONCAT('FOX', LPAD(id, 6, '0')) 
WHERE social_id IS NULL;

-- 新用户注册时自动生成
CREATE TRIGGER IF NOT EXISTS tr_user_social_id_insert
AFTER INSERT ON ba_user
FOR EACH ROW
BEGIN
    IF NEW.social_id IS NULL THEN
        SET NEW.social_id = CONCAT('FOX', LPAD(NEW.id, 6, '0'));
    END IF;
END$$

-- 添加索引以提高查询性能
CREATE INDEX idx_social_id ON ba_user(social_id);

-- 添加唯一约束确保social_id不重复
ALTER TABLE ba_user 
ADD CONSTRAINT uk_social_id UNIQUE (social_id);
