(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/prizedraw/confirmOrder"],{"06b1":function(e,t,n){"use strict";n.r(t);var a=n("5536"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"3c30":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){}));var a=function(){var e=this.$createElement;this._self._c},o=[]},"402c":function(e,t,n){},5536:function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n("6061"),o={data:function(){return{isLogined:!0,productxq:{name:"",image:"",jpid:0},imgbaseUrl:"",remark:"",shouAddr:{area:""},qjbutton:"#131315"}},onShow:function(){e.getStorageSync("diancan")?this.shouAddr=e.getStorageSync("diancan"):this.shouAddr={area:""}},onLoad:function(t){this.qjbutton=e.getStorageSync("storeInfo").button,this.imgbaseUrl=this.$baseUrl,this.addressData(),this.productxq=e.getStorageSync("dhspGoods")},methods:{dhSubTap:function(){if(""==this.shouAddr.area)return e.showToast({icon:"none",title:"请选择收货地址~",duration:2e3}),!1;e.showLoading({title:"加载中"}),"cj"==e.getStorageSync("dy_type")&&(0,a.exchangeGoodsApi)({id:e.getStorageSync("dhspGoods").jpid,addr_id:this.shouAddr.id,remark:this.remark}).then((function(t){console.log("抽奖-提交兑换",t),1==t.code&&(e.hideLoading(),e.redirectTo({url:"/pages/prizedraw/success"}))})),"dj"==e.getStorageSync("dy_type")&&(0,a.exchangeGoodsLevelApi)({id:e.getStorageSync("dhspGoods").jpid,addr_id:this.shouAddr.id,remark:this.remark}).then((function(t){console.log("等级-提交兑换",t),1==t.code&&(e.hideLoading(),e.redirectTo({url:"/pages/prizedraw/success"}))}))},goToAddr:function(t){e.navigateTo({url:"/pages/mine/address?type=".concat(t)})},addressData:function(){var t=this;(0,a.addrList)({page:1,size:999}).then((function(n){if(1==n.code){console.log(n,"地址列表");for(var a=[],o=0;o<n.data.length;o++)1==n.data[o].is_default&&a.push(n.data[o]);0==a.length?t.shouAddr={area:""}:(t.shouAddr=a[0],e.setStorageSync("diancan",a[0]))}else t.mescroll.endErr()}))},navTo:function(t){e.navigateTo({url:t})}}};t.default=o}).call(this,n("df3c")["default"])},"68bd":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("cff9");a(n("3240"));var o=a(n("7452"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},7452:function(e,t,n){"use strict";n.r(t);var a=n("3c30"),o=n("06b1");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("a109");var d=n("828b"),i=Object(d["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=i.exports},a109:function(e,t,n){"use strict";var a=n("402c"),o=n.n(a);o.a}},[["68bd","common/runtime","common/vendor"]]]);