<template>
  <view class="likes-container">
    <!-- 消息列表 -->
    <scroll-view 
      class="message-list"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view v-if="messageList && messageList.length > 0">
        <view 
          v-for="message in messageList" 
          :key="message.id"
          class="message-card"
          @click="openMessageDetail(message)"
        >
          <!-- 用户头像 -->
          <u-avatar 
            :src="message.userAvatar" 
            size="40"
            class="user-avatar"
          ></u-avatar>
          
          <!-- 消息内容 -->
          <view class="message-content">
            <view class="message-header">
              <text class="user-name">{{ message.userName }}</text>
              <text class="action-type">{{ message.type === 'like' ? '赞了你的' : '评论了你的' }}</text>
              <text class="post-type">{{ getPostType(message.postType) }}</text>
              <text class="message-time">{{ formatTime(message.createTime) }}</text>
            </view>
            
            <!-- 评论内容 -->
            <text v-if="message.type === 'comment'" class="comment-content">{{ message.content }}</text>
            
            <!-- 相关帖子 -->
            <view class="related-post" @click.stop="openPost(message.postId)">
              <image v-if="message.postCover" :src="message.postCover" class="post-cover" mode="aspectFill"></image>
              <text class="post-title">{{ message.postTitle }}</text>
            </view>
          </view>
          
          <!-- 未读标识 -->
          <view v-if="!message.isRead" class="unread-dot"></view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <u-empty mode="data" text="暂无赞和评论"></u-empty>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && messageList && messageList.length > 0" class="load-more">
        <u-loading mode="flower" size="24"></u-loading>
        <text class="load-text">加载中...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'LikesAndComments',
  data() {
    return {
      isRefreshing: false,
      hasMore: true,
      page: 1,
      pageSize: 20,
      messageList: [
        {
          id: 1,
          type: 'like',
          userId: 'user001',
          userName: '舞蹈小仙女',
          userAvatar: 'https://picsum.photos/100/100?random=801',
          postId: 'post001',
          postTitle: '今天的舞蹈练习分享',
          postCover: 'https://picsum.photos/200/200?random=901',
          postType: 'video',
          createTime: new Date(Date.now() - 1800000),
          isRead: false
        },
        {
          id: 2,
          type: 'comment',
          userId: 'user002',
          userName: '街舞达人',
          userAvatar: 'https://picsum.photos/100/100?random=802',
          postId: 'post002',
          postTitle: '新学的Breaking动作',
          postCover: 'https://picsum.photos/200/200?random=902',
          postType: 'video',
          content: '这个动作太帅了！能教教我吗？',
          createTime: new Date(Date.now() - 3600000),
          isRead: true
        },
        {
          id: 3,
          type: 'like',
          userId: 'user003',
          userName: '芭蕾公主',
          userAvatar: 'https://picsum.photos/100/100?random=803',
          postId: 'post003',
          postTitle: '芭蕾基本功练习心得',
          postCover: null,
          postType: 'text',
          createTime: new Date(Date.now() - 7200000),
          isRead: false
        },
        {
          id: 4,
          type: 'comment',
          userId: 'user004',
          userName: '现代舞爱好者',
          userAvatar: 'https://picsum.photos/100/100?random=804',
          postId: 'post004',
          postTitle: '舞蹈服装搭配分享',
          postCover: 'https://picsum.photos/200/200?random=904',
          postType: 'image',
          content: '这套服装真的很适合现代舞，在哪里买的？',
          createTime: new Date(Date.now() - 10800000),
          isRead: true
        }
      ]
    }
  },
  onLoad() {
    this.loadMessages()
  },
  methods: {
    getPostType(type) {
      const typeMap = {
        video: '视频',
        image: '图片',
        text: '帖子'
      }
      return typeMap[type] || '帖子'
    },

    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    },

    openMessageDetail(message) {
      // 标记为已读
      message.isRead = true
      
      // 跳转到相关帖子
      this.openPost(message.postId)
    },

    openPost(postId) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${postId}`
      })
    },

    onRefresh() {
      this.isRefreshing = true
      this.page = 1
      this.loadMessages().finally(() => {
        this.isRefreshing = false
      })
    },

    loadMore() {
      if (!this.hasMore) return
      this.page++
      this.loadMessages()
    },

    async loadMessages() {
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        if (this.page >= 3) {
          this.hasMore = false
        }
      } catch (error) {
        console.error('加载消息失败:', error)
        this.$u.toast('加载失败，请重试')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.likes-container {
  //height: 90vh;
  background: #f5f5f5;
}

.message-list {
  height: 100%;
  padding: 32rpx 0;
}

.message-card {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  margin: 0 32rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.user-avatar {
  margin-right: 24rpx;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 8rpx;
}

.action-type {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.post-type {
  font-size: 28rpx;
  color: #2979ff;
  margin-right: 16rpx;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  margin-left: auto;
}

.comment-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: block;
}

.related-post {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e4e7ed;
}

.post-cover {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.post-title {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

.unread-dot {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
}

.empty-state {
  display: flex;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.load-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}
</style>
