(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/jp-signature/components/jp-signature/jp-signature"],{"0246":function(t,e,n){"use strict";(function(t){var a=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=a(n("7eb4")),i=a(n("7ca3")),r=a(n("34cf")),c=a(n("ee10")),o=n("062e"),u=n("7425"),h=n("d798"),f=a(n("6ea6"));function d(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,a)}return n}function l(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?d(Object(n),!0).forEach((function(e){(0,i.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var g={props:f.default,data:function(){return{canvasWidth:null,canvasHeight:null,offscreenWidth:null,offscreenHeight:null,useCanvas2d:!0,show:!0,offscreenStyles:"",showMask:!1,isPC:!1}},computed:{canvasId:function(){return"lime-signature".concat(this._uid)},offscreenId:function(){return this.canvasId+"offscreen"},offscreenSize:function(){var t=this.offscreenWidth,e=this.offscreenHeight;return this.landscape?[e,t]:[t,e]},canvasStyle:function(){var t=this.canvasWidth,e=this.canvasHeight,n=this.backgroundColor;return{width:t&&t+"px",height:e&&e+"px",background:n}},param:function(){var t=this.penColor,e=this.penSize,n=this.backgroundColor,a=this.backgroundImage,s=this.landscape,i=this.boundingBox,r=this.openSmooth,c=this.minLineWidth,o=this.maxLineWidth,u=this.minSpeed,h=this.maxWidthDiffRate,f=this.maxHistoryLength,d=this.disableScroll,l=this.disabled;return JSON.parse(JSON.stringify({penColor:t,penSize:e,backgroundColor:n,backgroundImage:a,landscape:s,boundingBox:i,openSmooth:r,minLineWidth:c,maxLineWidth:o,minSpeed:u,maxWidthDiffRate:h,maxHistoryLength:f,disableScroll:d,disabled:l}))}},created:function(){var e=t.getSystemInfoSync(),n=e.platform;this.isPC=/windows|mac/.test(n),this.useCanvas2d="2d"==this.type&&(0,o.canIUseCanvas2d)()&&!this.isPC,this.showMask=this.isPC},mounted:function(){var t=this;return(0,c.default)(s.default.mark((function e(){var n;return s.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.beforeDelay){e.next=3;break}return e.next=3,(0,o.sleep)(t.beforeDelay);case 3:return e.next=5,t.getContext();case 5:n=e.sent,t.signature=new u.Signature(n),t.canvasEl=t.signature.canvas.get("el"),t.offscreenWidth=t.canvasWidth=t.signature.canvas.get("width"),t.offscreenHeight=t.canvasHeight=t.signature.canvas.get("height"),t.stopWatch=t.$watch("param",(function(e){t.signature.pen.setOption(e)}),{immediate:!0});case 11:case"end":return e.stop()}}),e)})))()},beforeDestroy:function(){this.stopWatch&&this.stopWatch(),this.signature.destroy(),this.show=!1,this.signature=null},methods:{redo:function(){this.signature&&this.signature.redo()},restore:function(){this.redo()},undo:function(){this.signature&&this.signature.undo()},clear:function(){this.signature&&this.signature.clear()},isEmpty:function(){return this.signature.isEmpty()},canvasToMaskPath:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=this.isEmpty(),s=this.signature.canvas.get("width"),i=this.signature.canvas.get("height"),r=t.getSystemInfoSync(),c=r.pixelRatio;this.useCanvas2d&&(this.offscreenWidth=s*c,this.offscreenHeight=i*c);var o=t.createCanvasContext("offscreen",this),u=function(t){return n.success&&n.success(t)},f=function(t){return n.fail&&n.fail(t)};this.signature.pen.getMaskedImageData((function(r){t.canvasPutImageData({canvasId:"offscreen",x:0,y:0,width:Math.floor(e.offscreenWidth),height:Math.floor(e.offscreenHeight),data:r,fail:function(t){f(t)},success:function(t){(0,h.toDataURL)("offscreen",e,n).then((function(t){var n=Math.max(e.offscreenWidth,e.offscreenHeight);o.restore(),o.clearRect(0,0,n,n),e.offscreenWidth=s,e.offscreenHeight=i,u({tempFilePath:t,isEmpty:a})}))}},e)}))},canvasToTempFilePath:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};console.log("啊实打实1ssss");var a=this.isEmpty(),i=this.useCanvas2d,u=function(t){return n.success&&n.success(t)},f=function(t){return n.fail&&n.fail(t)},d=this.signature.canvas.get("el"),l=d.canvas,g=this.backgroundColor,v=this.landscape,p=this.boundingBox,m=this.signature.canvas.get("width"),y=this.signature.canvas.get("height"),b=0,w=0,x="devtools"==t.getSystemInfoSync().platform,C=this.preferToDataURL,S=1,E=function(s){var c=function(){var n=i&&!!t.createOffscreenCanvas&&C;if(n&&!x){var a=t.createOffscreenCanvas({type:"2d"});a.width=e.offscreenSize[0]*S,a.height=e.offscreenSize[1]*S;var s=a.getContext("2d");return[s,a]}var r=t.createCanvasContext("offscreen",e);return[r]}(),f=(0,r.default)(c,2),d=f[0],p=f[1];if(d.save(),d.setTransform(1,0,0,1,0,0),v&&(d.translate(0,m*S),d.rotate(-Math.PI/2)),g&&!(0,o.isTransparent)(g)&&(d.fillStyle=g,d.fillRect(0,0,m,y)),p){var b=l.createImage();b.src=s,b.onload=function(){d.drawImage(b,0,0,m*S,y*S);var t=p.toDataURL();u({tempFilePath:t,isEmpty:a})}}else d.drawImage(s,0,0,m*S,y*S),d.draw(!1,(function(){(0,h.toDataURL)("offscreen",e,n).then((function(t){var e=Math.max(m,y);d.restore(),d.clearRect(0,0,e,e),u({tempFilePath:t,isEmpty:a})}))}))},O=function(){var t=(0,c.default)(s.default.mark((function t(){var n;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.offscreenWidth==m&&e.offscreenHeight==y){t.next=5;break}return e.offscreenWidth=m,e.offscreenHeight=y,t.next=5,(0,o.sleep)(100);case 5:n={x:b,y:w,width:m,height:y,canvas:i?l:null,preferToDataURL:C},(0,h.toDataURL)(e.canvasId,e,n).then(E).catch(f);case 7:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();p&&!this.isPC?this.signature.getContentBoundingBox(function(){var t=(0,c.default)(s.default.mark((function t(n){return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.offscreenWidth=m=n.width,e.offscreenHeight=y=n.height,b=n.startX,w=n.startY,O();case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()):O()},getContext:function(){var e=this;return(0,o.getRect)("#".concat(this.canvasId),{context:this,type:this.useCanvas2d?"fields":"boundingClientRect"}).then((function(n){if(n){var a,s=n.width,i=n.height,r=n.node,c=n.left,u=n.top,f=n.right,d=t.getSystemInfoSync(),l=d.pixelRatio;return r?(a=r.getContext("2d"),r.width=s*l,r.height=i*l):(l=1,a=(0,h.uniContext)(e.canvasId,e),r={getContext:function(t){return"2d"==t?a:null},createImage:h.createImage,toDataURL:function(){return(0,h.toDataURL)(e.canvasId,e)},requestAnimationFrame:o.requestAnimationFrame}),a.clearRect(0,0,s,i),{left:c,top:u,right:f,width:s,height:i,context:a,canvas:r,pixelRatio:l}}}))},getTouch:function(t){var e=this;return this.isPC&&this.canvasRect&&(t.touches=t.touches.map((function(t){return l(l({},t),{},{x:t.clientX-e.canvasRect.left,y:t.clientY-e.canvasRect.top})}))),t},touchStart:function(t){var e=this;this.canvasEl&&(this.isStart=!0,this.isPC?(0,o.getRect)("#".concat(this.canvasId),{context:this}).then((function(n){e.canvasRect=n,e.canvasEl.dispatchEvent("touchstart",(0,o.wrapEvent)(e.getTouch(t)))})):this.canvasEl.dispatchEvent("touchstart",(0,o.wrapEvent)(t)))},touchMove:function(t){!this.canvasEl||!this.isStart&&this.canvasEl||this.canvasEl.dispatchEvent("touchmove",(0,o.wrapEvent)(this.getTouch(t)))},touchEnd:function(t){this.canvasEl&&(this.isStart=!1,this.canvasEl.dispatchEvent("touchend",(0,o.wrapEvent)(t)))}}};e.default=g}).call(this,n("df3c")["default"])},"3e27":function(t,e,n){},"5f3c":function(t,e,n){"use strict";n.r(e);var a=n("ebc8"),s=n("ff36");for(var i in s)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(i);n("a60d");var r=n("828b"),c=Object(r["a"])(s["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=c.exports},a60d:function(t,e,n){"use strict";var a=n("3e27"),s=n.n(a);s.a},ebc8:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=(this._self._c,this.show?this.__get_style([this.canvasStyle,this.styles]):null);this.$mp.data=Object.assign({},{$root:{s0:e}})},s=[]},ff36:function(t,e,n){"use strict";n.r(e);var a=n("0246"),s=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=s.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/jp-signature/components/jp-signature/jp-signature-create-component',
    {
        'uni_modules/jp-signature/components/jp-signature/jp-signature-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("5f3c"))
        })
    },
    [['uni_modules/jp-signature/components/jp-signature/jp-signature-create-component']]
]);
