(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/topic/list"],{3700:function(e,o,t){"use strict";(function(e){var n=t("47a9");Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var a=n(t("7eb4")),c=n(t("af34")),i=n(t("ee10")),s=t("41ad"),r={name:"TopicList",data:function(){return{searchKeyword:"",refreshing:!1,loading:!1,hasMore:!0,page:1,pageSize:20,allTopics:[]}},computed:{filteredTopics:function(){var e=this.allTopics;if(this.searchKeyword.trim()){var o=this.searchKeyword.toLowerCase();e=e.filter((function(e){return e.name.toLowerCase().includes(o)||e.description.toLowerCase().includes(o)}))}return e}},onLoad:function(){this.loadTopics()},methods:{goBack:function(){e.navigateBack()},loadTopics:function(){var e=this;return(0,i.default)(a.default.mark((function o(){var t,n,i;return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(!e.loading){o.next=2;break}return o.abrupt("return");case 2:return e.loading=!0,o.prev=3,console.log("开始加载话题列表 - page:",e.page),t=1===e.page?50:e.pageSize,o.next=8,(0,s.getHotTags)(t);case 8:n=o.sent,console.log("话题列表API返回:",n),n&&0===n.code&&n.data?(console.log("🔥 话题列表API返回的原始数据:",n.data),n.data.forEach((function(e){console.log("🔥 标签 ".concat(e.name," - coverImage: ").concat(e.coverImage))})),i=n.data.map((function(o){return{id:o.id,name:o.name,description:o.description||"关于".concat(o.name,"的精彩内容分享"),cover:o.coverImage||e.getDefaultCoverImage(o),category:e.getTagCategory(o.name),postCount:o.useCount||0,followCount:Math.floor(.3*(o.useCount||0)),isFollowed:!1}})),1===e.page?e.allTopics=i:e.allTopics=[].concat((0,c.default)(e.allTopics),(0,c.default)(i)),e.hasMore=i.length>=e.pageSize,console.log("话题列表加载成功 - 总数:",e.allTopics.length)):(console.error("话题列表API返回格式不正确:",n),1===e.page&&(e.allTopics=e.getDefaultTopics(),e.hasMore=!1)),o.next=17;break;case 13:o.prev=13,o.t0=o["catch"](3),console.error("加载话题列表失败:",o.t0),1===e.page&&(e.allTopics=e.getDefaultTopics(),e.hasMore=!1);case 17:return o.prev=17,e.loading=!1,e.refreshing=!1,o.finish(17);case 21:case"end":return o.stop()}}),o,null,[[3,13,17,21]])})))()},getDefaultCoverImage:function(e){return{"街舞":"https://file.foxdance.com.cn/tags/street-dance.jpg","爵士舞":"https://file.foxdance.com.cn/tags/jazz-dance.jpg","芭蕾":"https://file.foxdance.com.cn/tags/ballet.jpg","现代舞":"https://file.foxdance.com.cn/tags/modern-dance.jpg","拉丁舞":"https://file.foxdance.com.cn/tags/latin-dance.jpg","民族舞":"https://file.foxdance.com.cn/tags/folk-dance.jpg","古典舞":"https://file.foxdance.com.cn/tags/classical-dance.jpg","舞蹈":"https://file.foxdance.com.cn/tags/dance.jpg"}[e.name]||"https://picsum.photos/300/200?random=".concat(e.id)},getTagCategory:function(e){return{"街舞":"dance","爵士舞":"dance","芭蕾":"dance","现代舞":"dance","拉丁舞":"dance","民族舞":"dance","古典舞":"dance","舞蹈":"dance"}[e]||"other"},getDefaultTopics:function(){return[{id:1,name:"街舞",description:"关于街舞的精彩内容分享",cover:"https://picsum.photos/300/200?random=1",category:"dance",postCount:1234,followCount:456,isFollowed:!1},{id:2,name:"爵士舞",description:"关于爵士舞的精彩内容分享",cover:"https://picsum.photos/300/200?random=2",category:"dance",postCount:856,followCount:234,isFollowed:!1},{id:3,name:"芭蕾",description:"关于芭蕾的精彩内容分享",cover:"https://picsum.photos/300/200?random=3",category:"dance",postCount:642,followCount:189,isFollowed:!1},{id:4,name:"现代舞",description:"关于现代舞的精彩内容分享",cover:"https://picsum.photos/300/200?random=4",category:"dance",postCount:789,followCount:267,isFollowed:!1}]},onRefresh:function(){this.refreshing=!0,this.page=1,this.hasMore=!0,this.loadTopics()},loadMore:function(){!this.loading&&this.hasMore&&(this.page++,this.loadTopics())},onSearchInput:function(){},toggleFollow:function(e){e.isFollowed=!e.isFollowed,e.isFollowed?(e.followCount++,this.$u.toast("关注成功")):(e.followCount--,this.$u.toast("取消关注"))},goTopicDetail:function(o){e.navigateTo({url:"/pagesSub/social/topic/detail?id=".concat(o.id)})}}};o.default=r}).call(this,t("df3c")["default"])},"4e55":function(e,o,t){"use strict";var n=t("7633"),a=t.n(n);a.a},7633:function(e,o,t){},8658:function(e,o,t){"use strict";t.r(o);var n=t("9689"),a=t("d070");for(var c in a)["default"].indexOf(c)<0&&function(e){t.d(o,e,(function(){return a[e]}))}(c);t("4e55");var i=t("828b"),s=Object(i["a"])(a["default"],n["b"],n["c"],!1,null,"63e557de",null,!1,n["a"],void 0);o["default"]=s.exports},9689:function(e,o,t){"use strict";t.d(o,"b",(function(){return a})),t.d(o,"c",(function(){return c})),t.d(o,"a",(function(){return n}));var n={uIcon:function(){return t.e("components/uview-ui/components/u-icon/u-icon").then(t.bind(null,"8398"))},uButton:function(){return t.e("components/uview-ui/components/u-button/u-button").then(t.bind(null,"fcb0"))}},a=function(){var e=this.$createElement,o=(this._self._c,!this.hasMore&&this.allTopics.length>0),t=0===this.filteredTopics.length&&!this.loading;this.$mp.data=Object.assign({},{$root:{g0:o,g1:t}})},c=[]},d070:function(e,o,t){"use strict";t.r(o);var n=t("3700"),a=t.n(n);for(var c in n)["default"].indexOf(c)<0&&function(e){t.d(o,e,(function(){return n[e]}))}(c);o["default"]=a.a},df55:function(e,o,t){"use strict";(function(e,o){var n=t("47a9");t("cff9");n(t("3240"));var a=n(t("8658"));e.__webpack_require_UNI_MP_PLUGIN__=t,o(a.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])}},[["df55","common/runtime","common/vendor"]]]);