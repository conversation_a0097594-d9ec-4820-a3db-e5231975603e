(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/social/message/detail"],{2840:function(t,e,n){"use strict";n.r(e);var a=n("f1d5"),o=n("9ebc");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("8daa");var c=n("828b"),s=Object(c["a"])(o["default"],a["b"],a["c"],!1,null,"f9eda908",null,!1,a["a"],void 0);e["default"]=s.exports},"373d":function(t,e,n){},"8daa":function(t,e,n){"use strict";var a=n("373d"),o=n.n(a);o.a},"9ebc":function(t,e,n){"use strict";n.r(e);var a=n("c35b"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},c35b:function(t,e,n){"use strict";(function(t){var a=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(n("7eb4")),i=a(n("ee10")),c={name:"MessageDetail",data:function(){return{messageId:"",messageDetail:{id:1,type:"system",title:"系统维护通知",content:"系统将于今晚23:00-次日1:00进行维护升级，期间可能影响部分功能使用，请您提前做好准备。",htmlContent:'\n          <div style="line-height: 1.6; color: #333;">\n            <p>尊敬的用户：</p>\n            <p>为了给您提供更好的服务体验，我们将对系统进行维护升级。</p>\n            <p><strong>维护时间：</strong>今晚23:00-次日1:00</p>\n            <p><strong>影响范围：</strong></p>\n            <ul>\n              <li>视频上传功能暂时不可用</li>\n              <li>消息推送可能延迟</li>\n              <li>部分页面可能无法正常访问</li>\n            </ul>\n            <p><strong>建议：</strong></p>\n            <ul>\n              <li>请提前保存您的创作内容</li>\n              <li>避免在维护期间进行重要操作</li>\n            </ul>\n            <p>感谢您的理解与支持！</p>\n            <p style="text-align: right; margin-top: 20px;">Fox Dance 团队<br/>2024年7月16日</p>\n          </div>\n        ',createTime:new Date(Date.now()-36e5),isRead:!0,actions:[{id:1,type:"primary",text:"我知道了",action:"confirm"},{id:2,type:"secondary",text:"查看更多",action:"more",url:"https://help.foxdance.com"}],attachments:[{id:1,name:"维护详情说明.pdf",url:"https://example.com/maintenance.pdf",size:"2.5MB"}]}}},onLoad:function(t){this.messageId=t.id,this.loadMessageDetail()},methods:{goBack:function(){t.navigateBack()},getIconName:function(t){return{system:"setting",activity:"volume",security:"shield"}[t]||"bell"},formatTime:function(t){var e=new Date(t),n=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),i=e.getHours().toString().padStart(2,"0"),c=e.getMinutes().toString().padStart(2,"0");return"".concat(n,"-").concat(a,"-").concat(o," ").concat(i,":").concat(c)},loadMessageDetail:function(){var t=this;return(0,i.default)(o.default.mark((function e(){return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise((function(t){return setTimeout(t,500)}));case 3:t.markAsRead(),e.next=10;break;case 6:e.prev=6,e.t0=e["catch"](0),console.error("加载消息详情失败:",e.t0),t.$u.toast("加载失败，请重试");case 10:case"end":return e.stop()}}),e,null,[[0,6]])})))()},markAsRead:function(){console.log("标记消息已读:",this.messageId)},handleAction:function(e){var n=this;switch(e.action){case"confirm":this.$u.toast("已确认"),setTimeout((function(){n.goBack()}),1e3);break;case"more":e.url&&t.navigateTo({url:"/pages/webview/index?url=".concat(encodeURIComponent(e.url))});break;default:console.log("未知操作:",e)}},openAttachment:function(e){var n=this;t.downloadFile({url:e.url,success:function(e){200===e.statusCode&&t.openDocument({filePath:e.tempFilePath,success:function(){console.log("打开文档成功")},fail:function(t){console.error("打开文档失败:",t),n.$u.toast("无法打开此文件")}})},fail:function(t){console.error("下载失败:",t),n.$u.toast("下载失败")}})}}};e.default=c}).call(this,n("df3c")["default"])},dcdd:function(t,e,n){"use strict";(function(t,e){var a=n("47a9");n("cff9");a(n("3240"));var o=a(n("2840"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},f1d5:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return a}));var a={uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"8398"))}},o=function(){var t=this,e=t.$createElement,n=(t._self._c,t.getIconName(t.messageDetail.type)),a=t.formatTime(t.messageDetail.createTime),o=t.messageDetail.actions&&t.messageDetail.actions.length>0,i=t.messageDetail.attachments&&t.messageDetail.attachments.length>0;t.$mp.data=Object.assign({},{$root:{m0:n,m1:a,g0:o,g1:i}})},i=[]}},[["dcdd","common/runtime","common/vendor"]]]);