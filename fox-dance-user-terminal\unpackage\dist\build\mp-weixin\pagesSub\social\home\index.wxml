<view class="home-container data-v-2cde35c9" style="{{'height:'+('100vh')+';'+('overflow:'+('hidden')+';')}}"><view class="header data-v-2cde35c9"><view class="header-content data-v-2cde35c9"><view class="logo data-v-2cde35c9"><text class="logo-text data-v-2cde35c9">社区</text></view><view class="header-actions data-v-2cde35c9"><u-icon vue-id="ea743d72-1" name="search" size="24" color="#666" data-event-opts="{{[['^click',[['goSearch']]]]}}" bind:click="__e" class="data-v-2cde35c9" bind:__l="__l"></u-icon></view></view></view><view class="topic-tabs-container data-v-2cde35c9"><u-tabs vue-id="ea743d72-2" list="{{topicList}}" current="{{currentTopicIndex}}" scrollable="{{true}}" activeColor="#2979ff" inactiveColor="#666" fontSize="28" lineColor="#2979ff" lineWidth="40" lineHeight="6" height="80" itemStyle="padding: 0 32rpx;" data-event-opts="{{[['^change',[['selectTopic']]]]}}" bind:change="__e" class="data-v-2cde35c9" bind:__l="__l"></u-tabs></view><scroll-view class="post-list data-v-2cde35c9" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="post-grid data-v-2cde35c9"><block wx:for="{{postList}}" wx:for-item="post" wx:for-index="__i0__" wx:key="id"><post-card class="post-card-item data-v-2cde35c9" vue-id="{{'ea743d72-3-'+__i0__}}" post="{{post}}" data-event-opts="{{[['^click',[['goPostDetail']]],['^userClick',[['goUserProfile']]],['^like',[['onPostLike']]]]}}" bind:click="__e" bind:userClick="__e" bind:like="__e" bind:__l="__l"></post-card></block></view><block wx:if="{{loading}}"><view class="load-more data-v-2cde35c9"><u-loading vue-id="ea743d72-4" mode="circle" size="24" class="data-v-2cde35c9" bind:__l="__l"></u-loading><text class="load-text data-v-2cde35c9">加载中...</text></view></block><block wx:if="{{$root.g0}}"><view class="empty-state data-v-2cde35c9"><u-icon vue-id="ea743d72-5" name="file-text" color="#ccc" size="120rpx" class="data-v-2cde35c9" bind:__l="__l"></u-icon><text class="empty-text data-v-2cde35c9">{{$root.m0}}</text><text class="empty-desc data-v-2cde35c9">{{$root.m1}}</text></view></block></scroll-view></view>