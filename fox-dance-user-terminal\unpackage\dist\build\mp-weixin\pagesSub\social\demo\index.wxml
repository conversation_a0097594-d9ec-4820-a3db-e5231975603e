<view class="demo-container data-v-6b8fbc93"><view class="header data-v-6b8fbc93"><view class="header-content data-v-6b8fbc93"><text class="title data-v-6b8fbc93">帖子分享App演示</text></view></view><scroll-view class="content data-v-6b8fbc93" scroll-y="{{true}}"><view class="intro-section data-v-6b8fbc93"><text class="intro-title data-v-6b8fbc93">🎉 帖子分享社交App</text><text class="intro-desc data-v-6b8fbc93">一个现代化的社交分享平台，支持帖子发布、图片分享、实时聊天等功能</text></view><view class="feature-section data-v-6b8fbc93"><text class="section-title data-v-6b8fbc93">✨ 核心功能</text><view class="feature-grid data-v-6b8fbc93"><view data-event-opts="{{[['tap',[['goPage',['/pagesSub/social/home/<USER>']]]]]}}" class="feature-item data-v-6b8fbc93" bindtap="__e"><view class="feature-icon home data-v-6b8fbc93"><u-icon vue-id="c4c2647a-1" name="home" color="#fff" size="24" class="data-v-6b8fbc93" bind:__l="__l"></u-icon></view><text class="feature-name data-v-6b8fbc93">帖子卡片流</text><text class="feature-desc data-v-6b8fbc93">网格布局，瀑布流展示</text></view><view data-event-opts="{{[['tap',[['goPage',['/pagesSub/social/discover/index']]]]]}}" class="feature-item data-v-6b8fbc93" bindtap="__e"><view class="feature-icon discover data-v-6b8fbc93"><u-icon vue-id="c4c2647a-2" name="search" color="#fff" size="24" class="data-v-6b8fbc93" bind:__l="__l"></u-icon></view><text class="feature-name data-v-6b8fbc93">发现页面</text><text class="feature-desc data-v-6b8fbc93">热门话题和推荐</text></view><view data-event-opts="{{[['tap',[['goPage',['/pagesSub/social/publish/index']]]]]}}" class="feature-item data-v-6b8fbc93" bindtap="__e"><view class="feature-icon publish data-v-6b8fbc93"><u-icon vue-id="c4c2647a-3" name="plus" color="#fff" size="24" class="data-v-6b8fbc93" bind:__l="__l"></u-icon></view><text class="feature-name data-v-6b8fbc93">发布帖子</text><text class="feature-desc data-v-6b8fbc93">分享生活精彩瞬间</text></view><view data-event-opts="{{[['tap',[['goPage',['/pagesSub/social/message/index']]]]]}}" class="feature-item data-v-6b8fbc93" bindtap="__e"><view class="feature-icon message data-v-6b8fbc93"><u-icon vue-id="c4c2647a-4" name="chat" color="#fff" size="24" class="data-v-6b8fbc93" bind:__l="__l"></u-icon></view><text class="feature-name data-v-6b8fbc93">消息聊天</text><text class="feature-desc data-v-6b8fbc93">与朋友实时交流</text></view><view data-event-opts="{{[['tap',[['goPage',['/pagesSub/social/profile/index']]]]]}}" class="feature-item data-v-6b8fbc93" bindtap="__e"><view class="feature-icon profile data-v-6b8fbc93"><u-icon vue-id="c4c2647a-5" name="account" color="#fff" size="24" class="data-v-6b8fbc93" bind:__l="__l"></u-icon></view><text class="feature-name data-v-6b8fbc93">个人中心</text><text class="feature-desc data-v-6b8fbc93">管理个人资料</text></view><view data-event-opts="{{[['tap',[['goPage',['/pagesSub/social/post/detail?id=1']]]]]}}" class="feature-item data-v-6b8fbc93" bindtap="__e"><view class="feature-icon detail data-v-6b8fbc93"><u-icon vue-id="c4c2647a-6" name="file-text" color="#fff" size="24" class="data-v-6b8fbc93" bind:__l="__l"></u-icon></view><text class="feature-name data-v-6b8fbc93">帖子详情</text><text class="feature-desc data-v-6b8fbc93">查看帖子和评论</text></view></view></view><view class="tech-section data-v-6b8fbc93"><text class="section-title data-v-6b8fbc93">🛠 技术特性</text><view class="tech-list data-v-6b8fbc93"><view class="tech-item data-v-6b8fbc93"><u-icon vue-id="c4c2647a-7" name="checkmark-circle" color="#52c41a" size="16" class="data-v-6b8fbc93" bind:__l="__l"></u-icon><text class="tech-text data-v-6b8fbc93">基于uni-app + Vue2开发</text></view><view class="tech-item data-v-6b8fbc93"><u-icon vue-id="c4c2647a-8" name="checkmark-circle" color="#52c41a" size="16" class="data-v-6b8fbc93" bind:__l="__l"></u-icon><text class="tech-text data-v-6b8fbc93">使用uview-ui组件库</text></view><view class="tech-item data-v-6b8fbc93"><u-icon vue-id="c4c2647a-9" name="checkmark-circle" color="#52c41a" size="16" class="data-v-6b8fbc93" bind:__l="__l"></u-icon><text class="tech-text data-v-6b8fbc93">支持微信小程序平台</text></view><view class="tech-item data-v-6b8fbc93"><u-icon vue-id="c4c2647a-10" name="checkmark-circle" color="#52c41a" size="16" class="data-v-6b8fbc93" bind:__l="__l"></u-icon><text class="tech-text data-v-6b8fbc93">现代化卡片式设计</text></view><view class="tech-item data-v-6b8fbc93"><u-icon vue-id="c4c2647a-11" name="checkmark-circle" color="#52c41a" size="16" class="data-v-6b8fbc93" bind:__l="__l"></u-icon><text class="tech-text data-v-6b8fbc93">组件化开发模式</text></view><view class="tech-item data-v-6b8fbc93"><u-icon vue-id="c4c2647a-12" name="checkmark-circle" color="#52c41a" size="16" class="data-v-6b8fbc93" bind:__l="__l"></u-icon><text class="tech-text data-v-6b8fbc93">响应式布局适配</text></view></view></view><view class="design-section data-v-6b8fbc93"><text class="section-title data-v-6b8fbc93">🎨 设计亮点</text><view class="design-grid data-v-6b8fbc93"><view class="design-item data-v-6b8fbc93"><text class="design-title data-v-6b8fbc93">简约现代</text><text class="design-desc data-v-6b8fbc93">采用简洁的设计语言，注重用户体验</text></view><view class="design-item data-v-6b8fbc93"><text class="design-title data-v-6b8fbc93">色彩统一</text><text class="design-desc data-v-6b8fbc93">统一的色彩系统，保持视觉一致性</text></view><view class="design-item data-v-6b8fbc93"><text class="design-title data-v-6b8fbc93">交互流畅</text><text class="design-desc data-v-6b8fbc93">流畅的动画效果，提升操作体验</text></view><view class="design-item data-v-6b8fbc93"><text class="design-title data-v-6b8fbc93">适配完善</text><text class="design-desc data-v-6b8fbc93">完美适配不同尺寸的移动设备</text></view></view></view><view class="start-section data-v-6b8fbc93"><text class="start-title data-v-6b8fbc93">🚀 开始体验</text><text class="start-desc data-v-6b8fbc93">点击上方功能卡片，体验完整的社交分享功能</text><view class="start-buttons data-v-6b8fbc93"><u-button vue-id="c4c2647a-13" type="primary" size="large" text="进入首页" data-event-opts="{{[['^click',[['goPage',['/pagesSub/social/home/<USER>']]]]]}}" bind:click="__e" class="data-v-6b8fbc93" bind:__l="__l"></u-button><u-button vue-id="c4c2647a-14" type="default" size="large" text="发布帖子" data-event-opts="{{[['^click',[['goPage',['/pagesSub/social/publish/index']]]]]}}" bind:click="__e" class="data-v-6b8fbc93" bind:__l="__l"></u-button><u-button vue-id="c4c2647a-15" type="warning" size="large" text="组件测试" data-event-opts="{{[['^click',[['goPage',['/pagesSub/social/test/index']]]]]}}" bind:click="__e" class="data-v-6b8fbc93" bind:__l="__l"></u-button></view></view></scroll-view></view>